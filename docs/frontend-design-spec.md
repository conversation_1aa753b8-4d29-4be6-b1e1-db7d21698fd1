# Chateo UI Kit - 设计规范文档

## 项目概述

Chateo 是一个现代化的即时通讯应用 UI 套件，专为移动端设计，提供完整的聊天、联系人管理和社交功能界面组件。

## 技术栈建议

- **框架**: Vue 3 / React 18+
- **样式**: Tailwind CSS / CSS Modules
- **组件库**: 自定义组件系统
- **状态管理**: Pinia / Zustand
- **构建工具**: Vite

## 设计系统基础

### 颜色系统

#### 主色调
```css
:root {
  /* 主品牌色 */
  --primary-blue: #007AFF;
  --primary-blue-light: #5AC8FA;
  --primary-blue-dark: #0051D5;
  
  /* 辅助色 */
  --secondary-green: #34C759;
  --secondary-orange: #FF9500;
  --secondary-red: #FF3B30;
  --secondary-purple: #AF52DE;
  
  /* 中性色 */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* 语义化颜色 */
  --success: var(--secondary-green);
  --warning: var(--secondary-orange);
  --error: var(--secondary-red);
  --info: var(--primary-blue);
}
```

#### 深色模式
```css
[data-theme="dark"] {
  --background-primary: #000000;
  --background-secondary: #1C1C1E;
  --background-tertiary: #2C2C2E;
  --text-primary: #FFFFFF;
  --text-secondary: #EBEBF5;
  --text-tertiary: #EBEBF599;
}
```

### 字体系统

```css
:root {
  /* 字体族 */
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* 字体大小 */
  --text-xs: 12px;    /* 辅助文本 */
  --text-sm: 14px;    /* 正文小 */
  --text-base: 16px;  /* 正文 */
  --text-lg: 18px;    /* 正文大 */
  --text-xl: 20px;    /* 小标题 */
  --text-2xl: 24px;   /* 标题 */
  --text-3xl: 30px;   /* 大标题 */
  
  /* 字重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

### 间距系统

```css
:root {
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
}
```

### 圆角系统

```css
:root {
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;
}
```

### 阴影系统

```css
:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}
```

## 核心组件架构

### Avatar 头像组件

**用途**: 显示用户头像，支持多种状态和尺寸

**变体**:
- `with-photo`: 带照片头像
- `no-photo`: 无照片头像（显示首字母）
- `story`: 带故事圈的头像
- `online`: 在线状态头像

**Props 接口**:
```typescript
interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  status?: 'online' | 'offline' | 'away' | 'busy';
  hasStory?: boolean;
  initials?: string;
  onClick?: () => void;
}
```

**视觉规范**:
- 小尺寸 (sm): 32px × 32px
- 中尺寸 (md): 40px × 40px  
- 大尺寸 (lg): 56px × 56px
- 超大尺寸 (xl): 80px × 80px
- 圆角: 完全圆形 (border-radius: 50%)
- 在线状态指示器: 8px × 8px，绿色圆点
- 故事圈: 2px 渐变边框

**实现示例**:
```vue
<template>
  <div 
    :class="[
      'avatar',
      `avatar--${size}`,
      { 'avatar--has-story': hasStory }
    ]"
    @click="onClick"
  >
    <img 
      v-if="src" 
      :src="src" 
      :alt="alt"
      class="avatar__image"
    />
    <div v-else class="avatar__initials">
      {{ initials }}
    </div>
    <div 
      v-if="status === 'online'" 
      class="avatar__status"
    ></div>
  </div>
</template>

<style scoped>
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background: var(--gray-200);
  cursor: pointer;
}

.avatar--sm { width: 32px; height: 32px; }
.avatar--md { width: 40px; height: 40px; }
.avatar--lg { width: 56px; height: 56px; }
.avatar--xl { width: 80px; height: 80px; }

.avatar--has-story {
  border: 2px solid;
  border-image: linear-gradient(45deg, var(--primary-blue), var(--secondary-purple)) 1;
}

.avatar__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar__initials {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: calc(var(--avatar-size) * 0.4);
}

.avatar__status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(--success);
  border: 2px solid var(--background-primary);
  border-radius: 50%;
}
</style>
```

### Button 按钮组件

**用途**: 触发操作的交互元素

**变体**:
- `primary`: 主要按钮
- `ghost`: 幽灵按钮
- `icon`: 图标按钮

**Props 接口**:
```typescript
interface ButtonProps {
  variant?: 'primary' | 'ghost' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  onClick?: () => void;
}
```

**视觉规范**:
- 主要按钮: 蓝色背景，白色文字
- 幽灵按钮: 透明背景，蓝色边框和文字
- 图标按钮: 圆形，40px × 40px
- 圆角: 8px (小按钮), 12px (中按钮), 16px (大按钮)
- 内边距: 8px 16px (小), 12px 24px (中), 16px 32px (大)

### Input 输入框组件

**用途**: 文本输入和表单控件

**变体**:
- `initial`: 初始状态
- `filled`: 已填充状态
- `with-prefix`: 带前缀图标
- `with-suffix`: 带后缀图标

**Props 接口**:
```typescript
interface InputProps {
  value?: string;
  placeholder?: string;
  type?: 'text' | 'password' | 'email' | 'number';
  disabled?: boolean;
  error?: string;
  prefix?: string;
  suffix?: string;
  onInput?: (value: string) => void;
}
```

**视觉规范**:
- 高度: 48px
- 圆角: 12px
- 内边距: 12px 16px
- 边框: 1px solid var(--gray-300)
- 聚焦状态: 蓝色边框，阴影效果
- 错误状态: 红色边框

### Chat Bubble 聊天气泡组件

**用途**: 显示聊天消息

**变体**:
- `left`: 左侧消息（接收）
- `right`: 右侧消息（发送）
- `single-string`: 纯文本消息
- `with-image`: 带图片消息
- `voice-note`: 语音消息
- `replies`: 回复消息

**Props 接口**:
```typescript
interface ChatBubbleProps {
  orientation: 'left' | 'right';
  variant: 'single-string' | 'with-image' | 'voice-note' | 'replies';
  message: string;
  timestamp: string;
  avatar?: string;
  image?: string;
  voiceDuration?: number;
  replyTo?: string;
}
```

**视觉规范**:
- 左侧气泡: 灰色背景 (#F0F0F0)
- 右侧气泡: 蓝色背景 (var(--primary-blue))
- 圆角: 18px，发送方向角为 4px
- 最大宽度: 70% 屏幕宽度
- 内边距: 12px 16px
- 间距: 消息间 8px，不同用户间 16px

### Bottom Navigation 底部导航组件

**用途**: 主要页面导航

**变体**:
- `chats`: 聊天页面
- `contacts`: 联系人页面
- `more`: 更多页面

**Props 接口**:
```typescript
interface BottomNavProps {
  activeTab: 'chats' | 'contacts' | 'more';
  onTabChange: (tab: string) => void;
}
```

**视觉规范**:
- 高度: 80px (包含安全区域)
- 背景: 白色，带顶部分割线
- 图标尺寸: 24px × 24px
- 激活状态: 蓝色图标和文字
- 非激活状态: 灰色图标和文字

## 布局模式

### 页面布局

```css
.page-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-primary);
}

.page-header {
  flex-shrink: 0;
  padding: var(--space-4);
  background: var(--background-primary);
  border-bottom: 1px solid var(--gray-200);
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.page-footer {
  flex-shrink: 0;
  background: var(--background-primary);
  border-top: 1px solid var(--gray-200);
}
```

### 列表布局

```css
.list-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  transition: background-color 0.2s;
}

.list-item:hover {
  background: var(--gray-50);
}

.list-item:active {
  background: var(--gray-100);
}
```

## 交互模式

### 触摸反馈

```css
.touchable {
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.touchable:hover {
  transform: scale(1.02);
}

.touchable:active {
  transform: scale(0.98);
}
```

### 加载状态

```css
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--gray-200) 25%,
    var(--gray-100) 50%,
    var(--gray-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

## 响应式设计

### 断点系统

```css
:root {
  --breakpoint-sm: 375px;  /* 小屏手机 */
  --breakpoint-md: 768px;   /* 平板 */
  --breakpoint-lg: 1024px;  /* 桌面 */
  --breakpoint-xl: 1280px;  /* 大屏桌面 */
}

@media (min-width: 375px) {
  .container {
    max-width: 375px;
    margin: 0 auto;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 var(--space-6);
  }
}
```

## 可访问性要求

### ARIA 标签
- 所有交互元素必须有适当的 `aria-label`
- 按钮状态使用 `aria-pressed`
- 表单控件使用 `aria-describedby` 关联错误信息
- 导航使用 `role="navigation"`

### 键盘导航
- 所有交互元素支持 Tab 键导航
- 按钮支持 Enter 和 Space 键激活
- 列表支持方向键导航
- 模态框支持 Esc 键关闭

### 颜色对比
- 正文文字对比度 ≥ 4.5:1
- 大文字对比度 ≥ 3:1
- 交互元素对比度 ≥ 3:1
- 不依赖颜色传达信息

## 性能优化

### 图片优化
- 头像使用 WebP 格式，fallback 到 JPEG
- 实现懒加载和占位符
- 使用适当的图片尺寸

### 动画优化
- 使用 `transform` 和 `opacity` 进行动画
- 避免触发重排和重绘
- 使用 `will-change` 提示浏览器优化

### 代码分割
- 按页面分割代码
- 懒加载非关键组件
- 使用 Tree Shaking 移除未使用代码

## 实施路线图

### 第一阶段：基础组件 (1-2周)
- [ ] 设计令牌系统
- [ ] Avatar 组件
- [ ] Button 组件
- [ ] Input 组件
- [ ] Icon 组件

### 第二阶段：复合组件 (2-3周)
- [ ] Chat Bubble 组件
- [ ] Contact List Item 组件
- [ ] Bottom Navigation 组件
- [ ] Top Bar 组件
- [ ] Story 组件

### 第三阶段：页面布局 (1-2周)
- [ ] 聊天列表页面
- [ ] 联系人页面
- [ ] 聊天详情页面
- [ ] 设置页面

### 第四阶段：交互和动画 (1周)
- [ ] 页面转场动画
- [ ] 微交互效果
- [ ] 加载状态
- [ ] 错误状态

### 第五阶段：优化和测试 (1周)
- [ ] 可访问性测试
- [ ] 性能优化
- [ ] 跨浏览器测试
- [ ] 响应式测试

## 设计令牌文件

建议创建独立的设计令牌文件：

```javascript
// tokens/colors.js
export const colors = {
  primary: {
    50: '#EBF8FF',
    100: '#BEE3F8',
    500: '#007AFF',
    900: '#0051D5'
  },
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    500: '#6B7280',
    900: '#111827'
  }
};

// tokens/spacing.js
export const spacing = {
  1: '4px',
  2: '8px',
  4: '16px',
  6: '24px'
};

// tokens/typography.js
export const typography = {
  fontFamily: {
    sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto']
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px'
  }
};
```

## 反馈与迭代

### 设计评审检查清单
- [ ] 是否符合品牌视觉规范
- [ ] 是否满足可访问性要求
- [ ] 是否考虑了不同设备尺寸
- [ ] 是否包含所有交互状态
- [ ] 是否考虑了边界情况

### 开发实现检查清单
- [ ] 组件是否可复用
- [ ] 是否遵循命名约定
- [ ] 是否包含完整的 TypeScript 类型
- [ ] 是否包含单元测试
- [ ] 是否优化了性能

---

*本设计规范基于 Figma 设计稿分析生成，建议定期更新以保持与设计的同步。*