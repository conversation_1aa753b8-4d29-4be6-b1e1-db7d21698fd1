# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PYTHONPATH

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# Frontend (Node.js)
frontend/node_modules/
frontend/dist/
frontend/.nuxt/
frontend/.next/
frontend/.vuepress/dist/
frontend/.temp/
frontend/.cache/
frontend/coverage/
frontend/.nyc_output/
frontend/.grunt/
frontend/bower_components/
frontend/.lock-wscript
frontend/build/Release/
frontend/.deps/
frontend/.npm
frontend/.eslintcache
frontend/.stylelintcache
frontend/.node_repl_history
frontend/*.tgz
frontend/.yarn-integrity
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
frontend/.parcel-cache/
frontend/.vite/
frontend/storybook-static/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# Application specific
logs/
log/
*.log
uploads/
static/uploads/
media/
user_uploads/
profile_images/
chat_files/
*.db
*.sqlite
*.sqlite3

# Chat application specific
chat_logs/
message_attachments/
voice_messages/
profile_pictures/
group_avatars/
backup_files/
export_data/

# Redis dump files
dump.rdb

# PostgreSQL
*.sql.backup
*.dump

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
tests/
test_*.py
*_test.py
backend/tests/
frontend/tests/
frontend/test/
backend/test/

# Documentation
docs/_build/

# Backup files
*.bak
*.backup
*.old

# Local configuration
local_settings.py
config_local.py
.env.local
.env.development
.env.production
.env.test
config.local.json

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Frontend (if adding web interface later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
frontend/dist/
frontend/build/
static/js/
static/css/

# Package managers (keep uv.lock for this project)
Pipfile.lock
# poetry.lock  # Uncomment if using poetry
# uv.lock      # Uncomment if you want to ignore uv.lock

# Cache directories
.cache/
.npm/
.yarn/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/


# Crush
.crush/

forge.yaml
.claude/