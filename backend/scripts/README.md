# 脚本目录

本目录包含Vue3移动端聊天应用后端的工具脚本和辅助脚本。

## 📜 脚本列表

### 数据库工具
- **[reset_db_connection.py](./reset_db_connection.py)** - 数据库连接重置工具
  - 用于重置数据库连接配置
  - 清理连接池和会话

### API验证工具
- **[verify_file_api.py](./verify_file_api.py)** - 文件API验证工具
  - 验证文件上传API的功能
  - 测试文件处理逻辑

## 🚀 使用方法

### 运行脚本
```bash
# 在backend目录下运行
cd /path/to/backend

# 运行数据库重置脚本
uv run python scripts/reset_db_connection.py

# 运行文件API验证脚本
uv run python scripts/verify_file_api.py
```

### 开发新脚本
1. 在此目录下创建新的Python脚本
2. 添加适当的文档注释
3. 更新此README文件

## 📝 脚本开发规范

### 代码规范
- 使用Python 3.11+语法
- 遵循PEP 8代码风格
- 添加类型提示
- 包含详细的文档字符串

### 错误处理
- 使用适当的异常处理
- 提供清晰的错误消息
- 记录操作日志

### 配置管理
- 使用环境变量进行配置
- 支持命令行参数
- 提供默认值

## 🔧 常用脚本模板

### 基础脚本模板
```python
#!/usr/bin/env python3
"""
脚本描述
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        logger.info("脚本开始执行")
        # 脚本逻辑
        logger.info("脚本执行完成")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

---

*脚本目录版本: v1.0.0*  
*最后更新: 2025-01-01*
