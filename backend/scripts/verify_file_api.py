#!/usr/bin/env python3
"""
验证文件上传API是否正确实现

检查关键组件是否存在和正确配置
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_file_api_implementation():
    """验证文件API实现"""
    print("🔍 验证文件上传API实现...")
    
    checks = []
    
    # 1. 检查文件模型
    try:
        from app.models.file import File
        print("✅ 文件模型 (File) 导入成功")
        checks.append(True)
    except ImportError as e:
        print(f"❌ 文件模型导入失败: {e}")
        checks.append(False)
    
    # 2. 检查文件schemas
    try:
        from app.schemas.file import FileInfo, FileUploadResponse, FileType, UploadPurpose
        print("✅ 文件schemas导入成功")
        checks.append(True)
    except ImportError as e:
        print(f"❌ 文件schemas导入失败: {e}")
        checks.append(False)
    
    # 3. 检查文件处理器
    try:
        from app.utils.file_handler import file_handler
        print("✅ 文件处理器导入成功")
        checks.append(True)
    except ImportError as e:
        print(f"❌ 文件处理器导入失败: {e}")
        checks.append(False)
    
    # 4. 检查文件API路由
    try:
        from app.api.v1.files import router
        routes = router.routes
        print(f"✅ 文件API路由导入成功，包含 {len(routes)} 个端点")
        checks.append(True)
    except ImportError as e:
        print(f"❌ 文件API路由导入失败: {e}")
        checks.append(False)
    
    # 5. 检查路由注册
    try:
        from app.api.v1 import api_router
        # 检查OpenAPI schema
        import os
        os.environ["TESTING"] = "true"
        from main import app
        openapi_schema = app.openapi()
        file_paths = [path for path in openapi_schema.get("paths", {}).keys() if "/files" in path]
        print(f"✅ 文件路由已注册到OpenAPI，包含 {len(file_paths)} 个路径")
        checks.append(True)
    except Exception as e:
        print(f"❌ 路由注册检查失败: {e}")
        checks.append(False)
    
    # 6. 检查依赖包
    try:
        import aiofiles
        import PIL
        from fastapi import UploadFile
        print("✅ 必要依赖包已安装 (aiofiles, PIL, python-multipart)")
        checks.append(True)
    except ImportError as e:
        print(f"❌ 依赖包缺失: {e}")
        checks.append(False)
    
    # 7. 检查上传目录
    try:
        from app.core.config import get_settings
        settings = get_settings()
        upload_dir = Path(settings.upload_dir)
        if upload_dir.exists():
            print(f"✅ 上传目录存在: {upload_dir}")
            checks.append(True)
        else:
            print(f"⚠️  上传目录不存在: {upload_dir}")
            checks.append(False)
    except Exception as e:
        print(f"❌ 上传目录检查失败: {e}")
        checks.append(False)
    
    return all(checks)


def verify_api_endpoints():
    """验证API端点"""
    print("\n📋 验证API端点...")
    
    try:
        import os
        os.environ["TESTING"] = "true"
        from main import app
        
        openapi_schema = app.openapi()
        paths = openapi_schema.get("paths", {})
        
        expected_endpoints = [
            "/api/v1/files/upload",
            "/api/v1/files/avatar", 
            "/api/v1/files/",
            "/api/v1/files/chat/{chat_id}",
            "/api/v1/files/{file_id}",
            "/api/v1/files/search",
            "/api/v1/files/statistics/user"
        ]
        
        found_endpoints = []
        for endpoint in expected_endpoints:
            if endpoint in paths:
                methods = list(paths[endpoint].keys())
                print(f"✅ {endpoint} ({', '.join(methods)})")
                found_endpoints.append(endpoint)
            else:
                print(f"❌ {endpoint} 未找到")
        
        success_rate = len(found_endpoints) / len(expected_endpoints)
        print(f"\n📊 端点覆盖率: {len(found_endpoints)}/{len(expected_endpoints)} ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80%以上算成功
        
    except Exception as e:
        print(f"❌ API端点验证失败: {e}")
        return False


def main():
    """主验证函数"""
    print("🚀 开始验证文件上传API实现")
    print("=" * 60)
    
    # 验证实现
    implementation_ok = verify_file_api_implementation()
    
    # 验证端点
    endpoints_ok = verify_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📋 验证结果:")
    print(f"   实现检查: {'✅ 通过' if implementation_ok else '❌ 失败'}")
    print(f"   端点检查: {'✅ 通过' if endpoints_ok else '❌ 失败'}")
    
    if implementation_ok and endpoints_ok:
        print("\n🎉 文件上传API实现验证通过！")
        print("📝 主要功能:")
        print("   - 文件上传 (支持多种类型)")
        print("   - 头像上传 (专用端点)")
        print("   - 文件列表查询")
        print("   - 聊天文件管理")
        print("   - 文件搜索")
        print("   - 文件统计")
        print("   - 文件删除")
        return True
    else:
        print("\n⚠️  文件上传API实现存在问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
