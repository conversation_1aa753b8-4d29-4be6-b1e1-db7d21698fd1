#!/usr/bin/env python3
"""重置数据库连接脚本

用于解决asyncpg prepared statement缓存问题
"""

import asyncio
import logging
from app.core.database import db_manager, init_database, close_database

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def reset_database_connection():
    """重置数据库连接"""
    try:
        logger.info("开始重置数据库连接...")
        
        # 关闭现有连接
        await close_database()
        logger.info("已关闭现有数据库连接")
        
        # 重新初始化
        await init_database()
        logger.info("数据库连接重置完成")
        
        # 测试连接
        from app.core.database import mcp_connector
        is_connected = await mcp_connector.test_connection()
        logger.info(f"数据库连接测试: {'成功' if is_connected else '失败'}")
        
        # 再次关闭
        await close_database()
        
    except Exception as e:
        logger.error(f"重置数据库连接失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(reset_database_connection())
