"""Add database indexes

Revision ID: 8aedb1ee09e3
Revises: 0cc5647495b5
Create Date: 2025-08-01 13:36:19.449361

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8aedb1ee09e3'
down_revision = '0cc5647495b5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 只添加缺失的索引

    # 好友关系复合索引（性能优化）
    op.create_index('ix_friendships_user_friend_status', 'friendships', ['user_id', 'friend_id', 'status'])

    # 聊天成员复合索引（性能优化）
    op.create_index('ix_chat_members_user_visible_active', 'chat_members', ['user_id', 'chat_visible', 'is_active'])
    op.create_index('ix_chat_members_chat_active', 'chat_members', ['chat_id', 'is_active'])

    # 消息表复合索引（性能优化）
    op.create_index('ix_messages_chat_created', 'messages', ['chat_id', 'created_at'])
    op.create_index('ix_messages_sender_created', 'messages', ['sender_id', 'created_at'])

    # 消息状态复合索引（性能优化）
    op.create_index('ix_message_status_message_user', 'message_status', ['message_id', 'user_id'])


def downgrade() -> None:
    # 删除新添加的索引
    op.drop_index('ix_message_status_message_user')
    op.drop_index('ix_messages_sender_created')
    op.drop_index('ix_messages_chat_created')
    op.drop_index('ix_chat_members_chat_active')
    op.drop_index('ix_chat_members_user_visible_active')
    op.drop_index('ix_friendships_user_friend_status')
