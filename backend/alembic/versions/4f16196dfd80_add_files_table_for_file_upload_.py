"""Add files table for file upload functionality

Revision ID: 4f16196dfd80
Revises: c2837e927a3c
Create Date: 2025-08-01 15:39:14.894295

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4f16196dfd80'
down_revision = 'c2837e927a3c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('files',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_url', sa.String(length=500), nullable=False),
    sa.Column('file_type', sa.String(length=20), nullable=False),
    sa.Column('file_size', sa.BigInteger(), nullable=False),
    sa.Column('mime_type', sa.String(length=100), nullable=False),
    sa.Column('uploader_id', sa.Integer(), nullable=False),
    sa.Column('purpose', sa.String(length=20), nullable=False),
    sa.Column('chat_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chat_id'], ['chats.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['uploader_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('file_path')
    )
    op.create_index(op.f('ix_files_chat_id'), 'files', ['chat_id'], unique=False)
    op.create_index(op.f('ix_files_created_at'), 'files', ['created_at'], unique=False)
    op.create_index(op.f('ix_files_file_type'), 'files', ['file_type'], unique=False)
    op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)
    op.create_index(op.f('ix_files_is_deleted'), 'files', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_files_purpose'), 'files', ['purpose'], unique=False)
    op.create_index(op.f('ix_files_uploader_id'), 'files', ['uploader_id'], unique=False)
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index(op.f('ix_files_uploader_id'), table_name='files')
    op.drop_index(op.f('ix_files_purpose'), table_name='files')
    op.drop_index(op.f('ix_files_is_deleted'), table_name='files')
    op.drop_index(op.f('ix_files_id'), table_name='files')
    op.drop_index(op.f('ix_files_file_type'), table_name='files')
    op.drop_index(op.f('ix_files_created_at'), table_name='files')
    op.drop_index(op.f('ix_files_chat_id'), table_name='files')
    op.drop_table('files')
    # ### end Alembic commands ###
