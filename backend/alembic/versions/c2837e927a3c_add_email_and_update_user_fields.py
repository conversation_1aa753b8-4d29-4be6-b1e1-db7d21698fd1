"""Add email and update user fields

Revision ID: c2837e927a3c
Revises: 8aedb1ee09e3
Create Date: 2025-08-01 14:01:59.773826

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c2837e927a3c'
down_revision = '8aedb1ee09e3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_chat_members_chat_active'), table_name='chat_members')
    op.drop_index(op.f('ix_chat_members_user_visible_active'), table_name='chat_members')
    op.drop_index(op.f('ix_friendships_user_friend_status'), table_name='friendships')
    op.drop_index(op.f('ix_message_status_message_user'), table_name='message_status')
    op.drop_index(op.f('ix_messages_chat_created'), table_name='messages')
    op.drop_index(op.f('ix_messages_sender_created'), table_name='messages')

    # 添加新字段
    op.add_column('users', sa.Column('email', sa.String(length=255), nullable=True))
    op.add_column('users', sa.Column('hashed_password', sa.String(length=255), nullable=True))  # 先允许为空
    op.add_column('users', sa.Column('is_superuser', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(), nullable=True))

    # 迁移现有数据
    op.execute("UPDATE users SET hashed_password = password_hash WHERE password_hash IS NOT NULL")
    op.execute("UPDATE users SET is_superuser = false WHERE is_superuser IS NULL")
    op.execute("UPDATE users SET last_login_at = last_login WHERE last_login IS NOT NULL")

    # 设置字段为非空
    op.alter_column('users', 'hashed_password', nullable=False)
    op.alter_column('users', 'is_superuser', nullable=False)

    # 创建索引
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)

    # 删除旧字段
    op.drop_column('users', 'password_hash')
    op.drop_column('users', 'last_login')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('last_login', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('password_hash', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'is_superuser')
    op.drop_column('users', 'hashed_password')
    op.drop_column('users', 'email')
    op.create_index(op.f('ix_messages_sender_created'), 'messages', ['sender_id', 'created_at'], unique=False)
    op.create_index(op.f('ix_messages_chat_created'), 'messages', ['chat_id', 'created_at'], unique=False)
    op.create_index(op.f('ix_message_status_message_user'), 'message_status', ['message_id', 'user_id'], unique=False)
    op.create_index(op.f('ix_friendships_user_friend_status'), 'friendships', ['user_id', 'friend_id', 'status'], unique=False)
    op.create_index(op.f('ix_chat_members_user_visible_active'), 'chat_members', ['user_id', 'chat_visible', 'is_active'], unique=False)
    op.create_index(op.f('ix_chat_members_chat_active'), 'chat_members', ['chat_id', 'is_active'], unique=False)
    # ### end Alembic commands ###
