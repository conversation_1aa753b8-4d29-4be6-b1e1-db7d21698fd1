# Vue3移动端聊天应用后端 - 部署指南

本文档详细说明了如何在不同环境中部署Vue3移动端聊天应用后端系统。

## 📋 目录

- [环境要求](#环境要求)
- [快速部署](#快速部署)
- [详细部署步骤](#详细部署步骤)
- [环境配置](#环境配置)
- [数据库设置](#数据库设置)
- [SSL证书配置](#ssl证书配置)
- [性能优化](#性能优化)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 🔧 环境要求

### 最低系统要求

- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **内存**: 2GB RAM (推荐4GB+)
- **存储**: 10GB可用空间 (推荐20GB+)
- **网络**: 稳定的互联网连接

### 软件依赖

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Python**: 3.11+ (本地开发)
- **PostgreSQL**: 15+ (如果不使用Docker)
- **Redis**: 7+ (如果不使用Docker)
- **Nginx**: 1.20+ (生产环境推荐)

## 🚀 快速部署

### 使用部署脚本 (推荐)

```bash
# 1. 克隆项目
git clone <repository-url>
cd backend

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件设置必要的配置

# 3. 一键部署开发环境
./deploy.sh dev

# 4. 一键部署生产环境
./deploy.sh -c -b -m prod
```

### 使用Docker Compose

```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f app
```

## 📝 详细部署步骤

### 1. 准备工作

#### 1.1 系统更新
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 1.2 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 1.3 创建项目目录
```bash
sudo mkdir -p /opt/vue3-chat
sudo chown $USER:$USER /opt/vue3-chat
cd /opt/vue3-chat
```

### 2. 项目配置

#### 2.1 获取项目代码
```bash
# 从Git仓库克隆
git clone <repository-url> .

# 或者上传项目文件
scp -r ./backend user@server:/opt/vue3-chat/
```

#### 2.2 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**重要配置项**:
```bash
# 生产环境设置
ENVIRONMENT=production

# 数据库配置
DATABASE_URL=****************************************************/chat_db

# Redis配置
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=STRONG_REDIS_PASSWORD

# JWT密钥 (必须更改)
JWT_SECRET_KEY=your-super-strong-jwt-secret-key-for-production

# CORS配置 (设置为实际的前端域名)
CORS_ORIGINS=https://your-frontend-domain.com

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
```

#### 2.3 创建必要目录
```bash
mkdir -p uploads logs ssl
chmod 755 uploads logs
chmod 700 ssl
```

### 3. 数据库设置

#### 3.1 PostgreSQL配置
```bash
# 创建数据库初始化脚本
cat > init.sql << 'EOF'
-- 创建数据库和用户
CREATE DATABASE chat_db;
CREATE USER chat_user WITH ENCRYPTED PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE chat_db TO chat_user;

-- 创建测试数据库
CREATE DATABASE chat_test_db;
GRANT ALL PRIVILEGES ON DATABASE chat_test_db TO chat_user;
EOF
```

#### 3.2 Redis配置
```bash
# 创建Redis配置文件
cat > redis.conf << 'EOF'
# Redis配置
bind 0.0.0.0
port 6379
requirepass your_redis_password
appendonly yes
appendfsync everysec
maxmemory 256mb
maxmemory-policy allkeys-lru
EOF
```

### 4. SSL证书配置

#### 4.1 自签名证书 (开发/测试)
```bash
# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem \
  -out ssl/cert.pem \
  -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
```

#### 4.2 Let's Encrypt证书 (生产)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
sudo chown $USER:$USER ssl/*.pem
```

### 5. 服务启动

#### 5.1 构建和启动服务
```bash
# 构建镜像
docker-compose build --no-cache

# 启动数据库服务
docker-compose up -d postgres redis

# 等待数据库启动
sleep 30

# 运行数据库迁移
docker-compose run --rm app uv run alembic upgrade head

# 启动应用服务
docker-compose up -d app

# 启动Nginx
docker-compose up -d nginx
```

#### 5.2 验证部署
```bash
# 检查服务状态
docker-compose ps

# 检查健康状态
curl -f http://localhost:8000/health

# 检查API文档
curl -f http://localhost:8000/docs
```

## ⚙️ 环境配置

### 开发环境

创建 `docker-compose.dev.yml`:
```yaml
version: '3.8'
services:
  app:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
    command: uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 测试环境

创建 `docker-compose.test.yml`:
```yaml
version: '3.8'
services:
  app:
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=**************************************************/chat_test_db
```

### 生产环境

创建 `docker-compose.prod.yml`:
```yaml
version: '3.8'
services:
  app:
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

## 🔍 监控和日志

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f postgres

# 查看Nginx日志
docker-compose logs -f nginx

# 日志轮转配置
cat > /etc/logrotate.d/vue3-chat << 'EOF'
/opt/vue3-chat/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /opt/vue3-chat/docker-compose.yml restart app
    endscript
}
EOF
```

### 性能监控

```bash
# 系统资源监控
docker stats

# 应用性能监控
curl http://localhost:8000/health

# 数据库连接监控
docker-compose exec postgres psql -U chat_user -d chat_db -c "SELECT count(*) FROM pg_stat_activity;"
```

### 备份策略

```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/vue3-chat"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
docker-compose exec -T postgres pg_dump -U chat_user chat_db > $BACKUP_DIR/db_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x backup.sh

# 设置定时备份
echo "0 2 * * * /opt/vue3-chat/backup.sh" | crontab -
```

## 🚨 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs app

# 检查端口占用
netstat -tlnp | grep :8000

# 重新构建镜像
docker-compose build --no-cache app
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U chat_user

# 检查网络连接
docker-compose exec app ping postgres

# 重置数据库
docker-compose down
docker volume rm backend_postgres_data
docker-compose up -d postgres
```

#### 3. Redis连接问题
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 检查Redis配置
docker-compose exec redis redis-cli config get "*"
```

#### 4. 文件上传问题
```bash
# 检查上传目录权限
ls -la uploads/

# 修复权限
sudo chown -R 1000:1000 uploads/
sudo chmod -R 755 uploads/
```

### 性能优化

#### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_messages_chat_id ON messages(chat_id);
CREATE INDEX CONCURRENTLY idx_messages_created_at ON messages(created_at);
CREATE INDEX CONCURRENTLY idx_users_username ON users(username);

-- 配置连接池
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

#### 2. Redis优化
```bash
# 内存优化
redis-cli config set maxmemory 512mb
redis-cli config set maxmemory-policy allkeys-lru

# 持久化优化
redis-cli config set save "900 1 300 10 60 10000"
```

#### 3. 应用优化
```bash
# 增加工作进程数
export WORKERS=4

# 调整连接池大小
export DB_POOL_SIZE=20
export REDIS_MAX_CONNECTIONS=100
```

## 🔄 更新和维护

### 应用更新

```bash
# 1. 备份数据
./backup.sh

# 2. 拉取最新代码
git pull origin main

# 3. 重新构建和部署
./deploy.sh -c -b -m prod

# 4. 验证更新
curl -f http://localhost:8000/health
```

### 数据库迁移

```bash
# 运行迁移
docker-compose exec app uv run alembic upgrade head

# 回滚迁移
docker-compose exec app uv run alembic downgrade -1
```

### 安全更新

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 更新Docker镜像
docker-compose pull
docker-compose up -d

# 更新SSL证书
sudo certbot renew
```

---

*部署指南版本: v1.0.0*  
*最后更新: 2025-01-01*
