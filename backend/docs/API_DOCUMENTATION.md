# Vue3移动端聊天应用 - API文档

## 📋 概述

本文档描述了Vue3移动端聊天应用后端API的详细接口规范。后端基于FastAPI框架构建，提供RESTful API和WebSocket实时通信功能。

## 🔗 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **WebSocket URL**: `ws://localhost:8000/ws`
- **API版本**: v1.0.0
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

## 🔐 认证系统

### 用户注册
```http
POST /api/v1/auth/register
```

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "nickname": "string"
}
```

**响应**:
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "nickname": "string",
  "created_at": "datetime"
}
```

### 用户登录
```http
POST /api/v1/auth/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "bearer",
  "expires_in": "integer",
  "user": {
    "id": "integer",
    "username": "string",
    "nickname": "string"
  }
}
```

### 刷新令牌
```http
POST /api/v1/auth/refresh
```

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应**:
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": "integer"
}
```

### 获取用户信息
```http
GET /api/v1/auth/me
```

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "nickname": "string",
  "avatar": "string",
  "status": "string",
  "last_active": "datetime",
  "created_at": "datetime"
}
```

### 更新用户信息
```http
PUT /api/v1/auth/me
```

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "nickname": "string",
  "avatar": "string",
  "status": "string"
}
```

### 用户登出
```http
POST /api/v1/auth/logout
```

**请求头**:
```
Authorization: Bearer <access_token>
```

## 👥 好友管理

### 搜索用户
```http
GET /api/v1/friends/search?query=<search_term>
```

**查询参数**:
- `query`: 搜索关键词
- `limit`: 结果数量限制 (默认: 10)

**响应**:
```json
{
  "users": [
    {
      "id": "integer",
      "username": "string",
      "nickname": "string",
      "avatar": "string",
      "is_friend": "boolean"
    }
  ]
}
```

### 获取好友列表
```http
GET /api/v1/friends/
```

**响应**:
```json
{
  "friends": [
    {
      "id": "integer",
      "username": "string",
      "nickname": "string",
      "avatar": "string",
      "status": "string",
      "last_active": "datetime",
      "friendship_created": "datetime"
    }
  ]
}
```

### 发送好友请求
```http
POST /api/v1/friends/requests
```

**请求体**:
```json
{
  "user_id": "integer",
  "message": "string"
}
```

### 获取好友请求列表
```http
GET /api/v1/friends/requests
```

**查询参数**:
- `type`: `sent` | `received` (默认: `received`)

**响应**:
```json
{
  "requests": [
    {
      "id": "integer",
      "from_user": {
        "id": "integer",
        "username": "string",
        "nickname": "string",
        "avatar": "string"
      },
      "to_user": {
        "id": "integer",
        "username": "string",
        "nickname": "string",
        "avatar": "string"
      },
      "message": "string",
      "status": "pending",
      "created_at": "datetime"
    }
  ]
}
```

### 处理好友请求
```http
PUT /api/v1/friends/requests/{request_id}
```

**请求体**:
```json
{
  "action": "accept" | "reject"
}
```

### 删除好友
```http
DELETE /api/v1/friends/{friend_id}
```

## 💬 聊天管理

### 获取聊天列表
```http
GET /api/v1/chats/
```

**响应**:
```json
{
  "chats": [
    {
      "id": "integer",
      "type": "private" | "group",
      "name": "string",
      "avatar": "string",
      "participants": [
        {
          "id": "integer",
          "username": "string",
          "nickname": "string",
          "avatar": "string"
        }
      ],
      "last_message": {
        "id": "integer",
        "content": "string",
        "sender": {
          "id": "integer",
          "nickname": "string"
        },
        "created_at": "datetime"
      },
      "unread_count": "integer",
      "updated_at": "datetime"
    }
  ]
}
```

### 创建聊天
```http
POST /api/v1/chats/
```

**请求体**:
```json
{
  "type": "private" | "group",
  "name": "string",
  "participant_ids": ["integer"]
}
```

### 获取聊天详情
```http
GET /api/v1/chats/{chat_id}
```

**响应**:
```json
{
  "id": "integer",
  "type": "private" | "group",
  "name": "string",
  "avatar": "string",
  "description": "string",
  "participants": [
    {
      "id": "integer",
      "username": "string",
      "nickname": "string",
      "avatar": "string",
      "role": "admin" | "member",
      "joined_at": "datetime"
    }
  ],
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 更新聊天信息
```http
PUT /api/v1/chats/{chat_id}
```

**请求体**:
```json
{
  "name": "string",
  "description": "string",
  "avatar": "string"
}
```

### 添加聊天成员
```http
POST /api/v1/chats/{chat_id}/members
```

**请求体**:
```json
{
  "user_ids": ["integer"]
}
```

### 移除聊天成员
```http
DELETE /api/v1/chats/{chat_id}/members/{user_id}
```

### 离开聊天
```http
DELETE /api/v1/chats/{chat_id}/leave
```

## 📨 消息管理

### 获取消息历史
```http
GET /api/v1/chats/{chat_id}/messages
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 50)
- `before`: 获取指定时间之前的消息

**响应**:
```json
{
  "messages": [
    {
      "id": "integer",
      "content": "string",
      "type": "text" | "image" | "file" | "system",
      "sender": {
        "id": "integer",
        "username": "string",
        "nickname": "string",
        "avatar": "string"
      },
      "reply_to": {
        "id": "integer",
        "content": "string",
        "sender": {
          "nickname": "string"
        }
      },
      "attachments": [
        {
          "id": "integer",
          "filename": "string",
          "url": "string",
          "size": "integer",
          "mime_type": "string"
        }
      ],
      "status": "sent" | "delivered" | "read",
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  ],
  "pagination": {
    "page": "integer",
    "limit": "integer",
    "total": "integer",
    "has_next": "boolean"
  }
}
```

### 发送消息
```http
POST /api/v1/chats/{chat_id}/messages
```

**请求体**:
```json
{
  "content": "string",
  "type": "text" | "image" | "file",
  "reply_to_id": "integer",
  "attachments": ["integer"]
}
```

**响应**:
```json
{
  "id": "integer",
  "content": "string",
  "type": "text",
  "sender": {
    "id": "integer",
    "nickname": "string"
  },
  "status": "sent",
  "created_at": "datetime"
}
```

### 撤回消息
```http
DELETE /api/v1/messages/{message_id}
```

### 标记消息已读
```http
PUT /api/v1/chats/{chat_id}/messages/read
```

**请求体**:
```json
{
  "message_id": "integer"
}
```

## 📁 文件管理

### 上传文件
```http
POST /api/v1/files/upload
```

**请求体**: `multipart/form-data`
- `file`: 文件数据
- `type`: `avatar` | `attachment` (可选)

**响应**:
```json
{
  "id": "integer",
  "filename": "string",
  "original_filename": "string",
  "url": "string",
  "size": "integer",
  "mime_type": "string",
  "created_at": "datetime"
}
```

### 获取文件信息
```http
GET /api/v1/files/{file_id}
```

### 删除文件
```http
DELETE /api/v1/files/{file_id}
```

## 🔌 WebSocket实时通信

### 连接WebSocket
```
ws://localhost:8000/ws?token=<access_token>
```

### 消息格式

**客户端发送消息**:
```json
{
  "type": "message",
  "data": {
    "chat_id": "integer",
    "content": "string",
    "message_type": "text"
  }
}
```

**服务器推送消息**:
```json
{
  "type": "new_message",
  "data": {
    "id": "integer",
    "chat_id": "integer",
    "content": "string",
    "sender": {
      "id": "integer",
      "nickname": "string"
    },
    "created_at": "datetime"
  }
}
```

**在线状态更新**:
```json
{
  "type": "user_status",
  "data": {
    "user_id": "integer",
    "status": "online" | "offline",
    "last_active": "datetime"
  }
}
```

**输入状态**:
```json
{
  "type": "typing",
  "data": {
    "chat_id": "integer",
    "user_id": "integer",
    "is_typing": "boolean"
  }
}
```

## 🔍 系统接口

### 健康检查
```http
GET /health
```

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "datetime",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

### API文档
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 📊 错误响应格式

所有API错误都遵循统一的响应格式：

```json
{
  "detail": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "datetime",
  "path": "/api/v1/endpoint"
}
```

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 数据验证失败
- `429`: 请求过于频繁
- `500`: 服务器内部错误

## 🔒 安全说明

1. **认证**: 所有需要认证的接口都需要在请求头中包含有效的JWT令牌
2. **HTTPS**: 生产环境必须使用HTTPS
3. **限流**: API实现了基于IP的限流机制
4. **输入验证**: 所有输入都经过严格的验证和清理
5. **CORS**: 配置了适当的跨域资源共享策略

## 📝 使用示例

### JavaScript/TypeScript示例

```javascript
// 用户登录
const login = async (username, password) => {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });
  
  const data = await response.json();
  localStorage.setItem('access_token', data.access_token);
  return data;
};

// 发送消息
const sendMessage = async (chatId, content) => {
  const token = localStorage.getItem('access_token');
  const response = await fetch(`/api/v1/chats/${chatId}/messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ content, type: 'text' }),
  });
  
  return response.json();
};

// WebSocket连接
const connectWebSocket = () => {
  const token = localStorage.getItem('access_token');
  const ws = new WebSocket(`ws://localhost:8000/ws?token=${token}`);
  
  ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
  };
  
  return ws;
};
```

## 📚 更多文档

- [项目README](./README.md) - 项目概述和快速开始
- [部署指南](./DEPLOYMENT.md) - 详细的部署说明
- [开发指南](./DEVELOPMENT.md) - 开发环境设置和贡献指南

---

*API文档版本: v1.0.0*
*最后更新: 2025-01-01*
