version: '3.8'

services:
  # FastAPI应用服务
  app:
    build: .
    container_name: chat_app_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************************/chat_db
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
      - UPLOAD_DIR=/app/uploads
      - MAX_FILE_SIZE=10485760
      - ENVIRONMENT=production
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - chat_network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: chat_postgres
    environment:
      - POSTGRES_DB=chat_db
      - POSTGRES_USER=chat_user
      - POSTGRES_PASSWORD=chat_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chat_user -d chat_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - chat_network

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: chat_redis
    command: redis-server --appendonly yes --requirepass redis_password
    environment:
      - REDIS_PASSWORD=redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis_password", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - chat_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: chat_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - chat_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  chat_network:
    driver: bridge
