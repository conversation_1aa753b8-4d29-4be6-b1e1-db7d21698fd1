#!/bin/bash

# Vue3移动端聊天应用后端 - 部署脚本
# 自动化部署脚本，支持开发、测试和生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Vue3移动端聊天应用后端部署脚本"
    echo ""
    echo "用法: $0 [选项] <环境>"
    echo ""
    echo "环境:"
    echo "  dev         开发环境部署"
    echo "  test        测试环境部署"
    echo "  prod        生产环境部署"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --clean    清理旧的容器和镜像"
    echo "  -b, --build    强制重新构建镜像"
    echo "  -m, --migrate  运行数据库迁移"
    echo "  -s, --seed     运行数据库种子数据"
    echo "  --no-cache     构建时不使用缓存"
    echo ""
    echo "示例:"
    echo "  $0 dev                    # 部署开发环境"
    echo "  $0 -c -b prod            # 清理并重新构建生产环境"
    echo "  $0 -m -s test            # 部署测试环境并运行迁移和种子数据"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查uv
    if ! command -v uv &> /dev/null; then
        log_warning "uv未安装，将使用pip作为备选"
    fi
    
    log_success "依赖检查完成"
}

# 清理旧的容器和镜像
cleanup() {
    log_info "清理旧的容器和镜像..."
    
    # 停止并删除容器
    docker-compose down --remove-orphans || true
    
    # 删除未使用的镜像
    docker image prune -f || true
    
    # 删除未使用的卷
    docker volume prune -f || true
    
    log_success "清理完成"
}

# 设置环境变量
setup_environment() {
    local env=$1
    log_info "设置${env}环境变量..."
    
    case $env in
        "dev")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.dev.yml"
            export ENVIRONMENT="development"
            ;;
        "test")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.test.yml"
            export ENVIRONMENT="testing"
            ;;
        "prod")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"
            export ENVIRONMENT="production"
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac
    
    # 检查.env文件
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            log_warning ".env文件不存在，从.env.example复制"
            cp .env.example .env
            log_warning "请编辑.env文件并设置正确的配置值"
        else
            log_error ".env.example文件不存在，无法创建.env文件"
            exit 1
        fi
    fi
    
    log_success "环境变量设置完成"
}

# 构建镜像
build_images() {
    local no_cache=$1
    log_info "构建Docker镜像..."
    
    local build_args=""
    if [ "$no_cache" = true ]; then
        build_args="--no-cache"
    fi
    
    docker-compose build $build_args
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动数据库和Redis
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动应用服务
    docker-compose up -d app
    
    # 启动Nginx
    docker-compose up -d nginx
    
    log_success "服务启动完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待应用服务启动
    sleep 5
    
    # 运行迁移
    docker-compose exec app uv run alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 运行种子数据
run_seeds() {
    log_info "运行种子数据..."
    
    # 这里可以添加种子数据脚本
    # docker-compose exec app uv run python scripts/seed_data.py
    
    log_success "种子数据运行完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败，应用可能未正常启动"
    return 1
}

# 显示部署信息
show_deployment_info() {
    local env=$1
    
    echo ""
    log_success "🎉 部署完成！"
    echo ""
    echo "环境: $env"
    echo "应用地址: http://localhost:8000"
    echo "API文档: http://localhost:8000/docs"
    echo "健康检查: http://localhost:8000/health"
    echo ""
    echo "有用的命令:"
    echo "  查看日志: docker-compose logs -f app"
    echo "  查看状态: docker-compose ps"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart app"
    echo ""
}

# 主函数
main() {
    local environment=""
    local clean=false
    local build=false
    local migrate=false
    local seed=false
    local no_cache=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -b|--build)
                build=true
                shift
                ;;
            -m|--migrate)
                migrate=true
                shift
                ;;
            -s|--seed)
                seed=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            dev|test|prod)
                environment=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [ -z "$environment" ]; then
        log_error "请指定部署环境 (dev/test/prod)"
        show_help
        exit 1
    fi
    
    log_info "开始部署Vue3移动端聊天应用后端 - $environment环境"
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$clean" = true ]; then
        cleanup
    fi
    
    setup_environment $environment
    
    if [ "$build" = true ] || [ "$clean" = true ]; then
        build_images $no_cache
    fi
    
    start_services
    
    if [ "$migrate" = true ]; then
        run_migrations
    fi
    
    if [ "$seed" = true ]; then
        run_seeds
    fi
    
    if health_check; then
        show_deployment_info $environment
    else
        log_error "部署失败，请检查日志"
        docker-compose logs app
        exit 1
    fi
}

# 运行主函数
main "$@"
