# Vue3移动端聊天应用后端

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7+-red.svg)](https://redis.io/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

基于FastAPI的现代化聊天应用后端系统，支持实时消息推送、用户认证、好友管理等核心功能。

## ✨ 特性

- 🚀 **高性能**: 基于FastAPI异步框架，支持高并发
- 🔐 **安全认证**: JWT令牌认证，多层安全防护
- 💬 **实时通信**: WebSocket实时消息推送
- 👥 **好友系统**: 完整的好友管理和请求处理
- 📁 **文件上传**: 支持头像和文件分享
- 🔍 **全文搜索**: 用户和消息搜索功能
- 📊 **性能监控**: 内置性能监控和健康检查
- 🐳 **容器化**: Docker一键部署
- 📚 **完整文档**: 详细的API文档和部署指南

## 🏗️ 技术栈

### 核心框架
- **FastAPI** - 现代化Python Web框架
- **SQLAlchemy** - ORM数据库操作
- **Alembic** - 数据库迁移管理
- **Pydantic** - 数据验证和序列化

### 数据存储
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和消息队列

### 认证安全
- **JWT** - 令牌认证
- **bcrypt** - 密码加密
- **CORS** - 跨域资源共享

### 实时通信
- **WebSocket** - 实时双向通信
- **Redis Pub/Sub** - 消息广播

### 开发工具
- **uv** - 现代Python包管理器
- **pytest** - 测试框架
- **Docker** - 容器化部署
- **Nginx** - 反向代理

## 🚀 快速开始

### 环境要求

- Python 3.11+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd backend
```

2. **安装依赖**
```bash
# 安装uv包管理器
pip install uv

# 安装项目依赖
uv sync
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置数据库连接等配置
vim .env
```

4. **初始化数据库**
```bash
# 运行数据库迁移
uv run alembic upgrade head
```

5. **启动开发服务器**
```bash
# 启动FastAPI开发服务器
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

6. **访问应用**
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### Docker部署

1. **使用Docker Compose**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

2. **使用部署脚本**
```bash
# 开发环境部署
./deploy.sh dev

# 生产环境部署（包含数据库迁移）
./deploy.sh -m -b prod
```

## 📁 项目结构

```
backend/
├── app/                    # 应用核心代码
│   ├── api/               # API路由
│   │   └── v1/           # API版本1
│   ├── core/             # 核心配置和工具
│   ├── middleware/       # 中间件
│   ├── models/           # 数据模型
│   ├── schemas/          # Pydantic模式
│   ├── services/         # 业务逻辑服务
│   ├── utils/            # 工具函数
│   └── websocket/        # WebSocket处理
├── alembic/              # 数据库迁移
├── tests/                # 测试文件
│   ├── test_auth_api.py  # 认证API测试
│   ├── test_chats_api.py # 聊天API测试
│   ├── test_friends_api.py # 好友API测试
│   ├── test_messages_api.py # 消息API测试
│   ├── test_files_api.py # 文件API测试
│   ├── test_websocket_*.py # WebSocket测试
│   ├── test_security.py  # 安全测试
│   └── test_performance.py # 性能测试
├── docs/                 # 项目文档
│   ├── API_DOCUMENTATION.md # API文档
│   ├── DEPLOYMENT.md     # 部署指南
│   ├── PROJECT_SUMMARY.md # 项目总结
│   └── task_*_summary.md # 任务总结文档
├── scripts/              # 工具脚本
│   ├── reset_db_connection.py # 数据库重置
│   └── verify_file_api.py # API验证
├── uploads/              # 文件上传目录
├── logs/                 # 日志文件
├── docker-compose.yml    # Docker编排
├── Dockerfile           # Docker镜像
├── nginx.conf           # Nginx配置
├── deploy.sh            # 部署脚本
├── pyproject.toml       # 项目配置
└── main.py              # 应用入口
```

## 🔧 配置说明

### 环境变量

主要配置项说明：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

完整配置请参考 [.env.example](./.env.example)

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试文件
uv run pytest tests/test_auth.py

# 运行测试并生成覆盖率报告
uv run pytest --cov=app --cov-report=html
```

### 测试覆盖率

- 单元测试: 覆盖所有API端点和业务逻辑
- 集成测试: 测试API集成和WebSocket连接
- 性能测试: API响应时间和并发测试
- 安全测试: 安全漏洞和输入验证测试

## 📚 API文档

### 在线文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 离线文档
- [API文档](./docs/API_DOCUMENTATION.md) - 完整的API接口说明
- [部署指南](./docs/DEPLOYMENT.md) - 详细的部署和运维指南
- [项目总结](./docs/PROJECT_SUMMARY.md) - 项目完成总结

### 主要接口

- **认证系统**: `/api/v1/auth/*`
- **好友管理**: `/api/v1/friends/*`
- **聊天管理**: `/api/v1/chats/*`
- **消息处理**: `/api/v1/messages/*`
- **文件上传**: `/api/v1/files/*`

## 🔒 安全特性

- **JWT认证**: 无状态令牌认证
- **密码加密**: bcrypt安全哈希
- **输入验证**: 严格的数据验证和清理
- **SQL注入防护**: ORM参数化查询
- **XSS防护**: 输入转义和CSP头部
- **CSRF防护**: 令牌验证机制
- **限流保护**: API请求频率限制
- **安全头部**: 完整的HTTP安全头部

## 🚀 部署指南

### 开发环境
```bash
./deploy.sh dev
```

### 生产环境
```bash
# 完整部署（清理、构建、迁移）
./deploy.sh -c -b -m prod

# 仅更新应用
docker-compose restart app
```

### 监控和维护
```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f app

# 数据库备份
docker-compose exec postgres pg_dump -U chat_user chat_db > backup.sql

# 性能监控
curl http://localhost:8000/health
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 开发规范

- 遵循PEP 8代码风格
- 编写单元测试
- 更新相关文档
- 提交前运行测试和代码检查

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有疑问：

1. 查看 [API文档](./docs/API_DOCUMENTATION.md)
2. 参考 [部署指南](./docs/DEPLOYMENT.md)
3. 搜索现有的 [Issues](../../issues)
4. 创建新的 [Issue](../../issues/new)

## 📊 项目状态

- ✅ 用户认证系统
- ✅ 好友管理功能
- ✅ 实时聊天功能
- ✅ 文件上传功能
- ✅ WebSocket通信
- ✅ 安全防护机制
- ✅ 性能优化
- ✅ 容器化部署
- ✅ 完整测试覆盖

---

*项目版本: v1.0.0*
*最后更新: 2025-01-01*