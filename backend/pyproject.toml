[project]
name = "backend"
version = "0.1.0"
description = "Vue3移动端在线聊天应用后端API服务"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy[asyncio]>=2.0.23",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.7",
    "alembic>=1.12.1",
    "redis>=5.0.1",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "websockets>=12.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "email-validator>=2.2.0",
    "aiohttp>=3.12.15",
    "requests>=2.32.4",
    "pillow>=11.3.0",
    "aiofiles>=24.1.0",
    "psutil>=7.0.0",
    "supabase>=2.17.0",
    "aiosqlite>=0.21.0",
]

[tool.uv]
dev-dependencies = [
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "asyncio: marks tests as async",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
