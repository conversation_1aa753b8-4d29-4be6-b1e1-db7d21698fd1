"""File model."""

from datetime import datetime
from typing import Optional
import os

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from .base import Base


class File(Base):
    """File model representing the files table."""
    
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)  # 原始文件名
    file_path = Column(String(500), nullable=False, unique=True)  # 存储路径
    file_url = Column(String(500), nullable=False)  # 访问URL
    file_type = Column(String(20), nullable=False, index=True)  # image, document, audio, video, avatar, other
    file_size = Column(BigInteger, nullable=False)  # 文件大小（字节）
    mime_type = Column(String(100), nullable=False)  # MIME类型
    uploader_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    purpose = Column(String(20), nullable=False, index=True)  # avatar, chat_file, profile, attachment
    chat_id = Column(Integer, ForeignKey("chats.id", ondelete="CASCADE"), nullable=True, index=True)  # 关联聊天（可选）
    description = Column(Text, nullable=True)  # 文件描述
    is_deleted = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    uploader = relationship(
        "User",
        back_populates="uploaded_files"
    )
    
    chat = relationship(
        "Chat",
        back_populates="files"
    )
    
    def __repr__(self) -> str:
        return f"<File(id={self.id}, filename='{self.filename}', type='{self.file_type}', size={self.file_size})>"
    
    @property
    def is_image(self) -> bool:
        """Check if this is an image file."""
        return self.file_type == "image"
    
    @property
    def is_document(self) -> bool:
        """Check if this is a document file."""
        return self.file_type == "document"
    
    @property
    def is_audio(self) -> bool:
        """Check if this is an audio file."""
        return self.file_type == "audio"
    
    @property
    def is_video(self) -> bool:
        """Check if this is a video file."""
        return self.file_type == "video"
    
    @property
    def is_avatar(self) -> bool:
        """Check if this is an avatar file."""
        return self.file_type == "avatar"
    
    @property
    def size_mb(self) -> float:
        """Get file size in MB."""
        return self.file_size / (1024 * 1024)
    
    @property
    def extension(self) -> str:
        """Get file extension."""
        return os.path.splitext(self.filename)[1].lower()
    
    def soft_delete(self) -> None:
        """Soft delete the file."""
        self.is_deleted = True
        self.updated_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted file."""
        self.is_deleted = False
        self.updated_at = datetime.utcnow()
    
    def get_display_name(self) -> str:
        """Get display name for the file."""
        if self.description:
            return self.description
        return self.filename
    
    def is_owned_by(self, user_id: int) -> bool:
        """Check if the file is owned by the specified user."""
        return self.uploader_id == user_id
    
    def can_be_accessed_by(self, user_id: int) -> bool:
        """Check if the file can be accessed by the specified user."""
        # 文件所有者可以访问
        if self.uploader_id == user_id:
            return True
        
        # 如果是聊天文件，需要检查用户是否为聊天成员
        if self.chat_id and self.purpose == "chat_file":
            # 这里需要在API层面检查聊天成员身份
            return True
        
        # 头像文件公开可访问
        if self.purpose == "avatar":
            return True
        
        return False
    
    @classmethod
    def get_file_type_from_mime(cls, mime_type: str) -> str:
        """根据MIME类型确定文件类型"""
        if mime_type.startswith("image/"):
            return "image"
        elif mime_type.startswith("audio/"):
            return "audio"
        elif mime_type.startswith("video/"):
            return "video"
        elif mime_type in [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv"
        ]:
            return "document"
        else:
            return "other"
    
    @classmethod
    def is_allowed_extension(cls, filename: str, allowed_extensions: list) -> bool:
        """检查文件扩展名是否允许"""
        extension = os.path.splitext(filename)[1].lower().lstrip('.')
        return extension in [ext.lower() for ext in allowed_extensions]
    
    @classmethod
    def is_allowed_size(cls, file_size: int, max_size: int) -> bool:
        """检查文件大小是否允许"""
        return file_size <= max_size
    
    def get_thumbnail_url(self) -> Optional[str]:
        """获取缩略图URL（如果是图片）"""
        if self.is_image:
            # 生成缩略图URL
            base_url = self.file_url.rsplit('.', 1)[0]
            extension = self.file_url.rsplit('.', 1)[1]
            return f"{base_url}_thumb.{extension}"
        return None
