"""Chat member model."""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from .base import Base


class ChatMember(Base):
    """ChatMember model representing the chat_members table."""
    
    __tablename__ = "chat_members"
    
    id = Column(Integer, primary_key=True, index=True)
    chat_id = Column(Integer, ForeignKey("chats.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    role = Column(String(20), default="member")  # admin, member
    joined_at = Column(DateTime, default=datetime.utcnow)
    last_read_at = Column(DateTime)
    is_active = Column(Boolean, default=True, index=True)
    chat_visible = Column(Boolean, default=False)  # 聊天是否对该用户可见
    chat_deleted = Column(Boolean, default=False)  # 用户是否删除了聊天记录
    
    # Unique constraint to prevent duplicate memberships
    __table_args__ = (UniqueConstraint('chat_id', 'user_id', name='_chat_user_uc'),)
    
    # Relationships
    chat = relationship(
        "Chat",
        back_populates="members"
    )
    
    user = relationship(
        "User",
        back_populates="chat_memberships"
    )
    
    def __repr__(self) -> str:
        return f"<ChatMember(id={self.id}, chat_id={self.chat_id}, user_id={self.user_id}, role='{self.role}')>"
    
    @property
    def is_admin(self) -> bool:
        """Check if this member is an admin."""
        return self.role == "admin"
    
    @property
    def is_member(self) -> bool:
        """Check if this member has member role."""
        return self.role == "member"
    
    @property
    def is_visible(self) -> bool:
        """Check if the chat is visible to this member."""
        return self.chat_visible and not self.chat_deleted
    
    def promote_to_admin(self) -> None:
        """Promote this member to admin."""
        self.role = "admin"
    
    def demote_to_member(self) -> None:
        """Demote this member to regular member."""
        self.role = "member"
    
    def mark_as_read(self, timestamp: Optional[datetime] = None) -> None:
        """Mark messages as read up to the given timestamp."""
        self.last_read_at = timestamp or datetime.utcnow()
    
    def leave_chat(self) -> None:
        """Mark this member as inactive (left the chat)."""
        self.is_active = False
        self.chat_visible = False
    
    def rejoin_chat(self) -> None:
        """Mark this member as active (rejoined the chat)."""
        self.is_active = True
        self.chat_visible = True
        self.chat_deleted = False
    
    def hide_chat(self) -> None:
        """Hide the chat from this member's view."""
        self.chat_visible = False
    
    def show_chat(self) -> None:
        """Show the chat in this member's view."""
        self.chat_visible = True
        self.chat_deleted = False
    
    def delete_chat(self) -> None:
        """Mark the chat as deleted for this member."""
        self.chat_deleted = True
        self.chat_visible = False
    
    def restore_chat(self) -> None:
        """Restore the chat for this member."""
        self.chat_deleted = False
        self.chat_visible = True