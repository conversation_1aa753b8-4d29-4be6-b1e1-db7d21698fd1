"""Message status model."""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from .base import Base


class MessageStatus(Base):
    """MessageStatus model representing the message_status table."""
    
    __tablename__ = "message_status"
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    status = Column(String(20), default="sent", index=True)  # sent, delivered, read
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Unique constraint to ensure one status per message per user
    __table_args__ = (UniqueConstraint('message_id', 'user_id', name='_message_user_status_uc'),)
    
    # Relationships
    message = relationship(
        "Message",
        back_populates="statuses"
    )
    
    user = relationship(
        "User",
        back_populates="message_statuses"
    )
    
    def __repr__(self) -> str:
        return f"<MessageStatus(id={self.id}, message_id={self.message_id}, user_id={self.user_id}, status='{self.status}')>"
    
    @property
    def is_sent(self) -> bool:
        """Check if the message status is sent."""
        return self.status == "sent"
    
    @property
    def is_delivered(self) -> bool:
        """Check if the message status is delivered."""
        return self.status == "delivered"
    
    @property
    def is_read(self) -> bool:
        """Check if the message status is read."""
        return self.status == "read"
    
    def mark_as_delivered(self, timestamp: Optional[datetime] = None) -> None:
        """Mark the message as delivered."""
        if self.status == "sent":
            self.status = "delivered"
            self.timestamp = timestamp or datetime.utcnow()
    
    def mark_as_read(self, timestamp: Optional[datetime] = None) -> None:
        """Mark the message as read."""
        if self.status in ["sent", "delivered"]:
            self.status = "read"
            self.timestamp = timestamp or datetime.utcnow()
    
    @classmethod
    def create_for_message(cls, message_id: int, user_id: int, status: str = "sent") -> "MessageStatus":
        """Create a new message status for a user."""
        return cls(
            message_id=message_id,
            user_id=user_id,
            status=status,
            timestamp=datetime.utcnow()
        )
    
    @classmethod
    def bulk_create_for_chat_members(cls, message_id: int, user_ids: list[int], status: str = "sent") -> list["MessageStatus"]:
        """Create message statuses for multiple users (chat members)."""
        statuses = []
        timestamp = datetime.utcnow()
        
        for user_id in user_ids:
            status_obj = cls(
                message_id=message_id,
                user_id=user_id,
                status=status,
                timestamp=timestamp
            )
            statuses.append(status_obj)
        
        return statuses