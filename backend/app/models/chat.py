"""Chat model."""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from .base import Base


class Chat(Base):
    """Chat model representing the chats table."""
    
    __tablename__ = "chats"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    description = Column(Text)
    chat_type = Column(String(20), nullable=False, index=True)  # private, group
    avatar_url = Column(String(255))
    creator_id = Column(Integer, ForeignKey("users.id"), index=True)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = relationship(
        "User",
        back_populates="created_chats"
    )
    
    members = relationship(
        "ChatMember",
        back_populates="chat",
        cascade="all, delete-orphan"
    )
    
    messages = relationship(
        "Message",
        back_populates="chat",
        cascade="all, delete-orphan",
        order_by="Message.created_at"
    )

    files = relationship(
        "File",
        back_populates="chat",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Chat(id={self.id}, name='{self.name}', type='{self.chat_type}')>"
    
    @property
    def is_private(self) -> bool:
        """Check if this is a private chat."""
        return self.chat_type == "private"
    
    @property
    def is_group(self) -> bool:
        """Check if this is a group chat."""
        return self.chat_type == "group"
    
    @property
    def display_name(self) -> str:
        """Return the display name of the chat."""
        if self.is_group and self.name:
            return self.name
        elif self.is_private:
            # For private chats, we might want to show the other user's name
            # This would require additional logic to determine the other participant
            return self.name or "Private Chat"
        return self.name or f"Chat {self.id}"
    
    @property
    def active_members_count(self) -> int:
        """Get the count of active members in this chat."""
        return len([member for member in self.members if member.is_active])
    
    @property
    def latest_message(self) -> Optional["Message"]:
        """Get the latest message in this chat."""
        if self.messages:
            return max(self.messages, key=lambda m: m.created_at)
        return None
    
    def get_member_by_user_id(self, user_id: int) -> Optional["ChatMember"]:
        """Get a specific member by user ID."""
        for member in self.members:
            if member.user_id == user_id:
                return member
        return None
    
    def is_member(self, user_id: int) -> bool:
        """Check if a user is a member of this chat."""
        member = self.get_member_by_user_id(user_id)
        return member is not None and member.is_active
    
    def is_admin(self, user_id: int) -> bool:
        """Check if a user is an admin of this chat."""
        member = self.get_member_by_user_id(user_id)
        return member is not None and member.role == "admin" and member.is_active