"""Friendship model."""

from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from .base import Base


class Friendship(Base):
    """Friendship model representing the friendships table."""
    
    __tablename__ = "friendships"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    friend_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    status = Column(String(20), default="pending", index=True)  # pending, accepted, blocked
    request_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint to prevent duplicate friendships
    __table_args__ = (UniqueConstraint('user_id', 'friend_id', name='_user_friend_uc'),)
    
    # Relationships
    user = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="sent_friend_requests"
    )
    
    friend = relationship(
        "User",
        foreign_keys=[friend_id],
        back_populates="received_friend_requests"
    )
    
    def __repr__(self) -> str:
        return f"<Friendship(id={self.id}, user_id={self.user_id}, friend_id={self.friend_id}, status='{self.status}')>"
    
    @property
    def is_pending(self) -> bool:
        """Check if the friendship request is pending."""
        return self.status == "pending"
    
    @property
    def is_accepted(self) -> bool:
        """Check if the friendship is accepted."""
        return self.status == "accepted"
    
    @property
    def is_blocked(self) -> bool:
        """Check if the friendship is blocked."""
        return self.status == "blocked"
    
    def accept(self) -> None:
        """Accept the friendship request."""
        self.status = "accepted"
        self.updated_at = datetime.utcnow()
    
    def block(self) -> None:
        """Block the friendship."""
        self.status = "blocked"
        self.updated_at = datetime.utcnow()
    
    def reject(self) -> None:
        """Reject the friendship request (delete the record)."""
        # This would typically be handled by deleting the record
        pass