"""User model."""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from .base import Base


class User(Base):
    """User model representing the users table."""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=True, index=True)
    hashed_password = Column(String(255), nullable=False)
    nickname = Column(String(50))
    avatar_url = Column(String(255))
    signature = Column(Text)
    is_active = Column(Boolean, default=True, index=True)
    is_superuser = Column(Boolean, default=False)
    last_login_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    sent_friend_requests = relationship(
        "Friendship",
        foreign_keys="Friendship.user_id",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    received_friend_requests = relationship(
        "Friendship",
        foreign_keys="Friendship.friend_id",
        back_populates="friend",
        cascade="all, delete-orphan"
    )
    
    created_chats = relationship(
        "Chat",
        back_populates="creator",
        cascade="all, delete-orphan"
    )
    
    chat_memberships = relationship(
        "ChatMember",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    sent_messages = relationship(
        "Message",
        back_populates="sender",
        cascade="all, delete-orphan"
    )
    
    message_statuses = relationship(
        "MessageStatus",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    uploaded_files = relationship(
        "File",
        back_populates="uploader",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', nickname='{self.nickname}')>"
    
    @property
    def display_name(self) -> str:
        """Return the display name (nickname if available, otherwise username)."""
        return self.nickname or self.username
    
    def is_friend_with(self, user_id: int) -> bool:
        """Check if this user is friends with another user."""
        from .friendship import Friendship
        
        # Check if there's an accepted friendship in either direction
        friendship = (
            self.sent_friend_requests.filter(
                Friendship.friend_id == user_id,
                Friendship.status == "accepted"
            ).first() or
            self.received_friend_requests.filter(
                Friendship.user_id == user_id,
                Friendship.status == "accepted"
            ).first()
        )
        
        return friendship is not None