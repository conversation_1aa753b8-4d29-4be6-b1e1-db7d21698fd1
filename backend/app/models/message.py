"""Message model."""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from .base import Base


class Message(Base):
    """Message model representing the messages table."""
    
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    chat_id = Column(Integer, ForeignKey("chats.id", ondelete="CASCADE"), nullable=False, index=True)
    sender_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default="text")  # text, image, file, system
    reply_to_id = Column(Integer, ForeignKey("messages.id"), index=True)
    is_deleted = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    chat = relationship(
        "Chat",
        back_populates="messages"
    )
    
    sender = relationship(
        "User",
        back_populates="sent_messages"
    )
    
    reply_to = relationship(
        "Message",
        remote_side=[id],
        backref="replies"
    )
    
    statuses = relationship(
        "MessageStatus",
        back_populates="message",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"<Message(id={self.id}, chat_id={self.chat_id}, sender_id={self.sender_id}, content='{content_preview}')>"
    
    @property
    def is_text(self) -> bool:
        """Check if this is a text message."""
        return self.message_type == "text"
    
    @property
    def is_image(self) -> bool:
        """Check if this is an image message."""
        return self.message_type == "image"
    
    @property
    def is_file(self) -> bool:
        """Check if this is a file message."""
        return self.message_type == "file"
    
    @property
    def is_system(self) -> bool:
        """Check if this is a system message."""
        return self.message_type == "system"
    
    @property
    def is_reply(self) -> bool:
        """Check if this message is a reply to another message."""
        return self.reply_to_id is not None
    
    @property
    def has_replies(self) -> bool:
        """Check if this message has replies."""
        return len(self.replies) > 0
    
    def soft_delete(self) -> None:
        """Soft delete the message."""
        self.is_deleted = True
        self.updated_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted message."""
        self.is_deleted = False
        self.updated_at = datetime.utcnow()
    
    def get_status_for_user(self, user_id: int) -> Optional["MessageStatus"]:
        """Get the message status for a specific user."""
        for status in self.statuses:
            if status.user_id == user_id:
                return status
        return None
    
    def is_read_by_user(self, user_id: int) -> bool:
        """Check if the message has been read by a specific user."""
        status = self.get_status_for_user(user_id)
        return status is not None and status.status == "read"
    
    def is_delivered_to_user(self, user_id: int) -> bool:
        """Check if the message has been delivered to a specific user."""
        status = self.get_status_for_user(user_id)
        return status is not None and status.status in ["delivered", "read"]
    
    def get_read_count(self) -> int:
        """Get the number of users who have read this message."""
        return len([status for status in self.statuses if status.status == "read"])
    
    def get_delivered_count(self) -> int:
        """Get the number of users to whom this message has been delivered."""
        return len([status for status in self.statuses if status.status in ["delivered", "read"]])