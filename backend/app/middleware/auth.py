"""认证中间件模块

提供JWT认证、权限检查、用户信息获取等中间件功能。
支持路由保护、角色权限验证和请求上下文管理。
"""

import logging
from typing import Optional, List, Callable, Any
from functools import wraps

from fastapi import Request, HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.models.user import User
from app.utils.security import TokenManager
from app.utils.exceptions import AuthenticationError, AuthorizationError
from app.utils.logging import get_logger, security_logger

logger = get_logger(__name__)
security = HTTPBearer(auto_error=False)


class AuthMiddleware:
    """认证中间件类"""
    
    @staticmethod
    async def get_current_user_optional(
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
        db: AsyncSession = Depends(get_db_session)
    ) -> Optional[User]:
        """获取当前用户（可选）
        
        Args:
            credentials: HTTP认证凭据
            db: 数据库会话
            
        Returns:
            用户对象或None
        """
        if not credentials:
            return None
            
        try:
            # 验证令牌
            payload = await TokenManager.verify_token_with_blacklist(credentials.credentials)
            user_id = int(payload.get("sub"))
            
            # 查询用户
            user = await db.get(User, user_id)
            if not user or not user.is_active:
                return None
                
            return user
            
        except Exception as e:
            logger.warning(f"可选认证失败: {e}")
            return None
    
    @staticmethod
    async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db: AsyncSession = Depends(get_db_session)
    ) -> User:
        """获取当前用户（必需）
        
        Args:
            credentials: HTTP认证凭据
            db: 数据库会话
            
        Returns:
            用户对象
            
        Raises:
            AuthenticationError: 认证失败时抛出
        """
        if not credentials:
            raise AuthenticationError("缺少认证令牌")
            
        try:
            # 验证令牌
            payload = await TokenManager.verify_token_with_blacklist(credentials.credentials)
            user_id = int(payload.get("sub"))
            
            # 查询用户
            user = await db.get(User, user_id)
            if not user:
                raise AuthenticationError("用户不存在")
                
            if not user.is_active:
                raise AuthenticationError("用户账户已被禁用")
                
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            raise AuthenticationError("认证失败")
    
    @staticmethod
    async def get_current_active_user(
        current_user: User = Depends(get_current_user)
    ) -> User:
        """获取当前活跃用户
        
        Args:
            current_user: 当前用户
            
        Returns:
            活跃用户对象
            
        Raises:
            AuthenticationError: 用户不活跃时抛出
        """
        if not current_user.is_active:
            raise AuthenticationError("用户账户已被禁用")
        return current_user


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def require_permissions(permissions: List[str]):
        """要求特定权限的装饰器
        
        Args:
            permissions: 所需权限列表
            
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从kwargs中获取current_user
                current_user = kwargs.get('current_user')
                if not current_user:
                    raise AuthorizationError("缺少用户信息")
                
                # 检查权限
                if not PermissionChecker.check_permissions(current_user, permissions):
                    security_logger.log_permission_denied(
                        user_id=current_user.id,
                        resource=func.__name__,
                        action="access",
                        ip_address=kwargs.get('request', {}).get('client', {}).get('host', 'unknown')
                    )
                    raise AuthorizationError(f"缺少权限: {', '.join(permissions)}")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    @staticmethod
    def check_permissions(user: User, required_permissions: List[str]) -> bool:
        """检查用户权限
        
        Args:
            user: 用户对象
            required_permissions: 所需权限列表
            
        Returns:
            是否有权限
        """
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True
            
        # 检查用户权限（这里简化处理，实际项目中可能需要更复杂的权限系统）
        user_permissions = getattr(user, 'permissions', [])
        
        for permission in required_permissions:
            if permission not in user_permissions:
                return False
                
        return True
    
    @staticmethod
    def require_admin():
        """要求管理员权限的装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                if not current_user:
                    raise AuthorizationError("缺少用户信息")
                
                if not current_user.is_superuser:
                    security_logger.log_permission_denied(
                        user_id=current_user.id,
                        resource=func.__name__,
                        action="admin_access",
                        ip_address=kwargs.get('request', {}).get('client', {}).get('host', 'unknown')
                    )
                    raise AuthorizationError("需要管理员权限")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator


class RateLimiter:
    """API限流器"""
    
    def __init__(self, max_requests: int, window_seconds: int):
        """初始化限流器
        
        Args:
            max_requests: 时间窗口内最大请求数
            window_seconds: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
    
    async def check_rate_limit(
        self, 
        key: str, 
        request: Request
    ) -> bool:
        """检查是否超过限流
        
        Args:
            key: 限流键（通常是用户ID或IP地址）
            request: 请求对象
            
        Returns:
            是否允许请求
        """
        try:
            from app.core.redis import redis_cache
            
            # 构建Redis键
            rate_limit_key = f"rate_limit:{key}:{self.window_seconds}"
            
            # 获取当前计数
            current_count = await redis_cache.get(rate_limit_key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= self.max_requests:
                # 记录限流日志
                logger.warning(
                    f"API限流触发: {key} 在 {self.window_seconds}s 内请求 {current_count} 次",
                    extra={
                        "rate_limit_key": key,
                        "current_count": current_count,
                        "max_requests": self.max_requests,
                        "window_seconds": self.window_seconds,
                        "url": str(request.url),
                        "method": request.method,
                        "ip_address": request.client.host if request.client else "unknown"
                    }
                )
                return False
            
            # 增加计数
            if current_count == 0:
                # 第一次请求，设置过期时间
                await redis_cache.set(rate_limit_key, "1", expire=self.window_seconds)
            else:
                # 增加计数，保持原有过期时间
                await redis_cache.set(rate_limit_key, str(current_count + 1))
            
            return True
            
        except Exception as e:
            logger.error(f"限流检查失败: {e}")
            # 出错时允许请求通过
            return True
    
    def __call__(self, key_func: Callable[[Request], str] = None):
        """限流装饰器
        
        Args:
            key_func: 生成限流键的函数
            
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(request: Request, *args, **kwargs):
                # 生成限流键
                if key_func:
                    key = key_func(request)
                else:
                    # 默认使用IP地址
                    key = request.client.host if request.client else "unknown"
                
                # 检查限流
                if not await self.check_rate_limit(key, request):
                    from app.utils.exceptions import RateLimitError
                    raise RateLimitError("请求过于频繁，请稍后再试", retry_after=self.window_seconds)
                
                return await func(request, *args, **kwargs)
            return wrapper
        return decorator


# 常用的依赖注入函数
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> Optional[User]:
    """获取当前用户（可选）"""
    return await AuthMiddleware.get_current_user_optional(credentials, db)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """获取当前用户（必需）"""
    return await AuthMiddleware.get_current_user(credentials, db)


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    return await AuthMiddleware.get_current_active_user(current_user)


# 常用的限流器实例
login_rate_limiter = RateLimiter(max_requests=5, window_seconds=300)  # 5分钟内最多5次登录
api_rate_limiter = RateLimiter(max_requests=100, window_seconds=60)   # 1分钟内最多100次API调用
register_rate_limiter = RateLimiter(max_requests=3, window_seconds=3600)  # 1小时内最多3次注册


# 权限装饰器
require_admin = PermissionChecker.require_admin()
require_permissions = PermissionChecker.require_permissions
