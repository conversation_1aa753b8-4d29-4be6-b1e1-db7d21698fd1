"""数据验证中间件

提供请求数据验证、响应格式化和错误处理
"""

import json
import time
from typing import Callable, Dict, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from pydantic import ValidationError
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.validation import ResponseFormatter
from app.core.logging import logger


class ValidationMiddleware(BaseHTTPMiddleware):
    """数据验证中间件"""
    
    def __init__(self, app, enable_request_logging: bool = True):
        super().__init__(app)
        self.enable_request_logging = enable_request_logging
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""
        start_time = time.time()
        
        # 记录请求信息
        if self.enable_request_logging:
            await self._log_request(request)
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 记录响应时间
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except ValidationError as e:
            # 处理Pydantic验证错误
            return await self._handle_validation_error(e)
        
        except HTTPException as e:
            # 处理HTTP异常
            return await self._handle_http_exception(e)
        
        except Exception as e:
            # 处理未知错误
            logger.error(f"Unexpected error: {e}", exc_info=True)
            return await self._handle_unknown_error(e)
    
    async def _log_request(self, request: Request) -> None:
        """记录请求信息"""
        try:
            # 获取客户端IP
            client_ip = request.client.host if request.client else "unknown"
            
            # 获取用户代理
            user_agent = request.headers.get("user-agent", "unknown")
            
            # 记录请求
            logger.info(
                f"Request: {request.method} {request.url.path} "
                f"from {client_ip} ({user_agent})"
            )
            
            # 记录查询参数
            if request.query_params:
                logger.debug(f"Query params: {dict(request.query_params)}")
            
        except Exception as e:
            logger.warning(f"Failed to log request: {e}")
    
    async def _handle_validation_error(self, error: ValidationError) -> JSONResponse:
        """处理验证错误"""
        response_data = ResponseFormatter.validation_error_response(error)
        
        logger.warning(f"Validation error: {response_data}")
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response_data
        )
    
    async def _handle_http_exception(self, error: HTTPException) -> JSONResponse:
        """处理HTTP异常"""
        # 如果detail已经是格式化的响应，直接使用
        if isinstance(error.detail, dict) and "success" in error.detail:
            response_data = error.detail
        else:
            # 格式化错误响应
            response_data = ResponseFormatter.error_response(
                message=str(error.detail),
                error_code=f"HTTP_{error.status_code}"
            )
        
        logger.warning(f"HTTP exception {error.status_code}: {response_data}")
        
        return JSONResponse(
            status_code=error.status_code,
            content=response_data
        )
    
    async def _handle_unknown_error(self, error: Exception) -> JSONResponse:
        """处理未知错误"""
        response_data = ResponseFormatter.error_response(
            message="服务器内部错误",
            error_code="INTERNAL_SERVER_ERROR",
            details={"error_type": type(error).__name__}
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_data
        )


class ResponseFormattingMiddleware(BaseHTTPMiddleware):
    """响应格式化中间件"""
    
    def __init__(self, app, format_responses: bool = True):
        super().__init__(app)
        self.format_responses = format_responses
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """格式化响应"""
        response = await call_next(request)
        
        if not self.format_responses:
            return response
        
        # 只处理JSON响应
        if not response.headers.get("content-type", "").startswith("application/json"):
            return response
        
        # 跳过已经格式化的响应
        if hasattr(response, '_formatted'):
            return response
        
        try:
            # 读取响应内容
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            if body:
                content = json.loads(body.decode())
                
                # 如果响应不是标准格式，进行格式化
                if not isinstance(content, dict) or "success" not in content:
                    formatted_content = ResponseFormatter.success_response(
                        data=content,
                        message="操作成功"
                    )
                else:
                    formatted_content = content
                
                # 创建新的响应
                new_response = JSONResponse(
                    content=formatted_content,
                    status_code=response.status_code,
                    headers=dict(response.headers)
                )
                new_response._formatted = True
                return new_response
        
        except Exception as e:
            logger.warning(f"Failed to format response: {e}")
        
        return response


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """请求验证中间件"""
    
    def __init__(self, app, max_request_size: int = 10 * 1024 * 1024):  # 10MB
        super().__init__(app)
        self.max_request_size = max_request_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """验证请求"""
        try:
            # 检查请求大小
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > self.max_request_size:
                return JSONResponse(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    content=ResponseFormatter.error_response(
                        message="请求体过大",
                        error_code="REQUEST_TOO_LARGE"
                    )
                )
            
            # 验证Content-Type
            if request.method in ["POST", "PUT", "PATCH"]:
                content_type = request.headers.get("content-type", "")
                
                # 对于文件上传，允许multipart/form-data
                if "/upload" in str(request.url.path):
                    if not content_type.startswith("multipart/form-data"):
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content=ResponseFormatter.error_response(
                                message="文件上传需要multipart/form-data格式",
                                error_code="INVALID_CONTENT_TYPE"
                            )
                        )
                # 其他POST请求需要JSON格式
                elif not content_type.startswith("application/json"):
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content=ResponseFormatter.error_response(
                            message="请求需要application/json格式",
                            error_code="INVALID_CONTENT_TYPE"
                        )
                    )
            
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"Request validation error: {e}", exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=ResponseFormatter.error_response(
                    message="请求验证失败",
                    error_code="REQUEST_VALIDATION_ERROR"
                )
            )


def setup_validation_middleware(app):
    """设置验证中间件"""
    # 添加请求验证中间件
    app.add_middleware(RequestValidationMiddleware)
    
    # 添加数据验证中间件
    app.add_middleware(ValidationMiddleware)
    
    # 添加响应格式化中间件
    app.add_middleware(ResponseFormattingMiddleware)
    
    logger.info("Validation middleware setup completed")
