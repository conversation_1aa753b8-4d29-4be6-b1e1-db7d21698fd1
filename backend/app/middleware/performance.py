"""性能监控中间件

监控API请求性能，记录响应时间和资源使用情况，集成限流和缓存功能
"""

import time
import logging
import asyncio
from typing import Callable
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.performance import (
    performance_monitor_instance,
    rate_limiter,
    cache_manager,
    PerformanceConfig
)

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app, enable_rate_limiting: bool = True, enable_caching: bool = True):
        super().__init__(app)
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_caching = enable_caching
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并监控性能"""
        start_time = time.time()
        
        # 记录请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        client_ip = self._get_client_ip(request)
        
        try:
            # 检查限流
            if self.enable_rate_limiting and not self._check_rate_limit(client_ip, path):
                return JSONResponse(
                    status_code=429,
                    content={"detail": "请求过于频繁，请稍后再试"},
                    headers={"Retry-After": "60"}
                )
            
            # 检查缓存（仅对GET请求）
            if self.enable_caching and method == "GET":
                cached_response = await self._get_cached_response(request)
                if cached_response:
                    process_time = time.time() - start_time
                    cached_response.headers["X-Process-Time"] = str(process_time)
                    cached_response.headers["X-Cache"] = "HIT"
                    return cached_response
            
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            process_time = time.time() - start_time
            
            # 添加性能头部
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Cache"] = "MISS"
            
            # 记录性能指标
            performance_monitor_instance.record_request_time(path, process_time)
            
            # 记录性能日志
            self._log_request(method, url, response.status_code, process_time, client_ip)
            
            # 缓存响应（仅对成功的GET请求）
            if (self.enable_caching and method == "GET" and 
                response.status_code == 200 and self._is_cacheable_path(path)):
                await self._cache_response(request, response)
            
            return response
            
        except Exception as e:
            # 记录错误和响应时间
            process_time = time.time() - start_time
            logger.error(
                f"{method} {url} - ERROR: {str(e)} - {process_time:.3f}s - IP: {client_ip}"
            )
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端地址
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    def _check_rate_limit(self, client_ip: str, path: str) -> bool:
        """检查请求限流"""
        # 对不同路径使用不同的限流策略
        if path.startswith("/api/v1/auth"):
            # 认证接口更严格的限流
            return rate_limiter.is_allowed(f"auth:{client_ip}", limit=20, window=60)
        elif path.startswith("/api/v1/websocket"):
            # WebSocket连接限流
            return rate_limiter.is_allowed(f"ws:{client_ip}", limit=10, window=60)
        else:
            # 普通API限流
            return rate_limiter.is_allowed(f"api:{client_ip}")
    
    async def _get_cached_response(self, request: Request) -> Response:
        """获取缓存的响应"""
        cache_key = self._generate_cache_key(request)
        cached_data = await cache_manager.get(cache_key)
        
        if cached_data:
            try:
                # 这里简化处理，实际应该序列化完整的响应对象
                return JSONResponse(content={"cached": True, "data": cached_data})
            except Exception as e:
                logger.error(f"缓存响应解析失败: {e}")
        
        return None
    
    async def _cache_response(self, request: Request, response: Response) -> None:
        """缓存响应"""
        try:
            cache_key = self._generate_cache_key(request)
            # 这里简化处理，实际应该序列化完整的响应
            await cache_manager.set(cache_key, "cached_response", ttl=300)
        except Exception as e:
            logger.error(f"缓存响应失败: {e}")
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        path = request.url.path
        query = str(request.query_params)
        return f"response:{path}:{hash(query)}"
    
    def _is_cacheable_path(self, path: str) -> bool:
        """判断路径是否可缓存"""
        # 只缓存特定的只读接口
        cacheable_patterns = [
            "/health",
            "/docs",
            "/openapi.json",
            "/api/v1/users/profile",  # 用户资料
        ]
        
        return any(path.startswith(pattern) for pattern in cacheable_patterns)
    
    def _log_request(self, method: str, url: str, status_code: int, 
                    process_time: float, client_ip: str) -> None:
        """记录请求日志"""
        # 根据响应时间选择日志级别
        if process_time > PerformanceConfig.SLOW_REQUEST_THRESHOLD:
            logger.warning(
                f"SLOW REQUEST: {method} {url} - {status_code} - "
                f"{process_time:.3f}s - IP: {client_ip}"
            )
        elif status_code >= 400:
            logger.warning(
                f"ERROR REQUEST: {method} {url} - {status_code} - "
                f"{process_time:.3f}s - IP: {client_ip}"
            )
        else:
            logger.info(
                f"{method} {url} - {status_code} - "
                f"{process_time:.3f}s - IP: {client_ip}"
            )


class ResourceMonitoringMiddleware(BaseHTTPMiddleware):
    """资源监控中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self._request_count = 0
        self._active_requests = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """监控资源使用"""
        self._request_count += 1
        self._active_requests += 1
        
        try:
            # 检查活跃请求数
            if self._active_requests > 100:  # 最大并发请求数
                logger.warning(f"高并发请求: {self._active_requests} 个活跃请求")
            
            response = await call_next(request)
            
            # 添加资源监控头部
            response.headers["X-Request-Count"] = str(self._request_count)
            response.headers["X-Active-Requests"] = str(self._active_requests)
            
            return response
            
        finally:
            self._active_requests -= 1


class CompressionMiddleware(BaseHTTPMiddleware):
    """响应压缩中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理响应压缩"""
        response = await call_next(request)
        
        # 检查是否支持压缩
        accept_encoding = request.headers.get("accept-encoding", "")
        
        if "gzip" in accept_encoding and self._should_compress(response):
            # 这里应该实现实际的gzip压缩
            # 为了简化，只添加头部标识
            response.headers["Content-Encoding"] = "gzip"
            response.headers["Vary"] = "Accept-Encoding"
        
        return response
    
    def _should_compress(self, response: Response) -> bool:
        """判断是否应该压缩响应"""
        # 检查内容类型
        content_type = response.headers.get("content-type", "")
        
        compressible_types = [
            "application/json",
            "text/html",
            "text/css",
            "text/javascript",
            "application/javascript"
        ]
        
        return any(ct in content_type for ct in compressible_types)


# 性能监控API端点
async def get_performance_stats():
    """获取性能统计信息"""
    return performance_monitor_instance.get_performance_stats()


async def get_cache_stats():
    """获取缓存统计信息"""
    return {
        "memory_cache_size": len(cache_manager._memory_cache),
        "redis_connected": cache_manager._redis_client is not None
    }


async def get_rate_limit_stats(client_ip: str):
    """获取限流统计信息"""
    return {
        "remaining_requests": rate_limiter.get_remaining_requests(f"api:{client_ip}"),
        "auth_remaining": rate_limiter.get_remaining_requests(f"auth:{client_ip}", limit=20),
        "ws_remaining": rate_limiter.get_remaining_requests(f"ws:{client_ip}", limit=10)
    }
