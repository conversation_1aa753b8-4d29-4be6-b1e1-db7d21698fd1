"""安全防护中间件

提供CSRF防护、安全头部、XSS防护等安全中间件功能。
"""

import secrets
import time
from typing import Dict, List, Optional, Callable
from urllib.parse import urlparse

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from app.core.config import settings
from app.utils.logging import get_logger, security_logger

logger = get_logger(__name__)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头部中间件
    
    添加各种安全相关的HTTP头部，防止XSS、点击劫持等攻击。
    """
    
    def __init__(self, app, **kwargs):
        super().__init__(app)
        self.security_headers = {
            # XSS防护
            "X-XSS-Protection": "1; mode=block",
            # 内容类型嗅探防护
            "X-Content-Type-Options": "nosniff",
            # 点击劫持防护
            "X-Frame-Options": "DENY",
            # 强制HTTPS（生产环境）
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains" if not settings.debug else None,
            # 内容安全策略
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' ws: wss:; "
                "frame-ancestors 'none';"
            ),
            # 引用策略
            "Referrer-Policy": "strict-origin-when-cross-origin",
            # 权限策略
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            )
        }
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理请求并添加安全头部"""
        response = await call_next(request)
        
        # 添加安全头部
        for header_name, header_value in self.security_headers.items():
            if header_value:  # 跳过None值
                response.headers[header_name] = header_value
        
        return response


class CSRFProtectionMiddleware(BaseHTTPMiddleware):
    """CSRF防护中间件
    
    为状态改变的请求（POST、PUT、DELETE、PATCH）提供CSRF保护。
    """
    
    def __init__(self, app, **kwargs):
        super().__init__(app)
        self.csrf_token_header = "X-CSRF-Token"
        self.csrf_cookie_name = "csrf_token"
        self.exempt_paths = {
            "/docs", "/redoc", "/openapi.json",
            "/api/v1/auth/login", "/api/v1/auth/register"  # 登录注册豁免
        }
        self.state_changing_methods = {"POST", "PUT", "DELETE", "PATCH"}
    
    def _generate_csrf_token(self) -> str:
        """生成CSRF令牌"""
        return secrets.token_urlsafe(32)
    
    def _is_exempt_path(self, path: str) -> bool:
        """检查路径是否豁免CSRF检查"""
        return any(path.startswith(exempt) for exempt in self.exempt_paths)
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理CSRF保护"""
        
        # 检查是否需要CSRF保护
        if (request.method in self.state_changing_methods and 
            not self._is_exempt_path(request.url.path)):
            
            # 获取CSRF令牌
            csrf_token_header = request.headers.get(self.csrf_token_header)
            csrf_token_cookie = request.cookies.get(self.csrf_cookie_name)
            
            # 验证CSRF令牌
            if not csrf_token_header or not csrf_token_cookie:
                security_logger.log_security_event(
                    event_type="csrf_token_missing",
                    ip_address=request.client.host if request.client else "unknown",
                    user_agent=request.headers.get("user-agent", "unknown"),
                    url=str(request.url),
                    method=request.method
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="CSRF令牌缺失"
                )
            
            if csrf_token_header != csrf_token_cookie:
                security_logger.log_security_event(
                    event_type="csrf_token_mismatch",
                    ip_address=request.client.host if request.client else "unknown",
                    user_agent=request.headers.get("user-agent", "unknown"),
                    url=str(request.url),
                    method=request.method
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="CSRF令牌无效"
                )
        
        # 处理请求
        response = await call_next(request)
        
        # 为GET请求设置CSRF令牌
        if request.method == "GET" and not request.cookies.get(self.csrf_cookie_name):
            csrf_token = self._generate_csrf_token()
            response.set_cookie(
                key=self.csrf_cookie_name,
                value=csrf_token,
                httponly=True,
                secure=not settings.debug,
                samesite="strict",
                max_age=3600  # 1小时
            )
        
        return response


class GlobalRateLimitMiddleware(BaseHTTPMiddleware):
    """全局API限流中间件
    
    为所有API请求提供基础的限流保护。
    """
    
    def __init__(self, app, max_requests: int = 1000, window_seconds: int = 3600):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.exempt_paths = {"/health", "/", "/docs", "/redoc", "/openapi.json"}
    
    def _get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        # 优先使用认证用户ID
        if hasattr(request.state, 'user') and request.state.user:
            return f"user:{request.state.user.id}"
        
        # 否则使用IP地址
        return f"ip:{request.client.host if request.client else 'unknown'}"
    
    def _is_exempt_path(self, path: str) -> bool:
        """检查路径是否豁免限流"""
        return path in self.exempt_paths
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理全局限流"""
        
        # 检查是否豁免
        if self._is_exempt_path(request.url.path):
            return await call_next(request)
        
        try:
            from app.core.redis import redis_cache
            
            # 获取客户端标识符
            client_id = self._get_client_identifier(request)
            rate_limit_key = f"global_rate_limit:{client_id}:{self.window_seconds}"
            
            # 获取当前计数
            current_count = await redis_cache.get(rate_limit_key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= self.max_requests:
                # 记录限流日志
                security_logger.log_security_event(
                    event_type="global_rate_limit_exceeded",
                    ip_address=request.client.host if request.client else "unknown",
                    user_agent=request.headers.get("user-agent", "unknown"),
                    url=str(request.url),
                    method=request.method,
                    details={
                        "client_id": client_id,
                        "current_count": current_count,
                        "max_requests": self.max_requests,
                        "window_seconds": self.window_seconds
                    }
                )
                
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="请求过于频繁，请稍后再试",
                    headers={"Retry-After": str(self.window_seconds)}
                )
            
            # 增加计数
            if current_count == 0:
                await redis_cache.set(rate_limit_key, "1", expire=self.window_seconds)
            else:
                await redis_cache.set(rate_limit_key, str(current_count + 1))
            
            # 处理请求
            response = await call_next(request)
            
            # 添加限流头部
            response.headers["X-RateLimit-Limit"] = str(self.max_requests)
            response.headers["X-RateLimit-Remaining"] = str(max(0, self.max_requests - current_count - 1))
            response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.window_seconds)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"全局限流检查失败: {e}")
            # 出错时允许请求通过
            return await call_next(request)


class InputSanitizationMiddleware(BaseHTTPMiddleware):
    """输入清理中间件
    
    自动清理请求中的潜在恶意输入。
    """
    
    def __init__(self, app, **kwargs):
        super().__init__(app)
        self.dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'data:text/html',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'onclick\s*=',
        ]
    
    def _sanitize_value(self, value) -> any:
        """清理单个值"""
        if isinstance(value, str):
            from app.utils.security import SecurityUtils
            return SecurityUtils.sanitize_input(value)
        elif isinstance(value, dict):
            return {k: self._sanitize_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._sanitize_value(item) for item in value]
        else:
            return value
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理输入清理"""
        
        # 只处理包含JSON数据的请求
        if request.headers.get("content-type", "").startswith("application/json"):
            try:
                # 获取请求体
                body = await request.body()
                if body:
                    import json
                    data = json.loads(body)
                    
                    # 清理数据
                    sanitized_data = self._sanitize_value(data)
                    
                    # 重新设置请求体
                    request._body = json.dumps(sanitized_data).encode()
                    
            except Exception as e:
                logger.warning(f"输入清理失败: {e}")
                # 清理失败时继续处理原始请求
        
        return await call_next(request)


# 中间件实例
security_headers_middleware = SecurityHeadersMiddleware
csrf_protection_middleware = CSRFProtectionMiddleware
global_rate_limit_middleware = GlobalRateLimitMiddleware
input_sanitization_middleware = InputSanitizationMiddleware
