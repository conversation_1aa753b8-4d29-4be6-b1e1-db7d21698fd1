"""WebSocket在线状态同步服务

提供用户在线状态管理、状态广播和活跃时间跟踪功能
"""

import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from enum import Enum
import asyncio

from app.schemas.websocket import (
    OnlineStatus, MessageType, create_websocket_message,
    UserStatusData, TypingStatusData
)
from app.websocket.connection_manager import connection_manager
from app.websocket.utils import WebSocketUtils

logger = logging.getLogger(__name__)


class TypingStatus(str, Enum):
    """输入状态枚举"""
    TYPING = "typing"
    STOPPED = "stopped"


class UserPresence:
    """用户在线状态信息"""
    
    def __init__(self, user_id: int, status: OnlineStatus = OnlineStatus.ONLINE):
        self.user_id = user_id
        self.status = status
        self.last_seen = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        self.typing_in_chats: Set[int] = set()  # 正在输入的聊天室
        self.custom_status_message: Optional[str] = None
    
    def update_activity(self):
        """更新最后活跃时间"""
        self.last_activity = datetime.utcnow()
        if self.status == OnlineStatus.AWAY:
            # 如果用户有活动，从离开状态恢复到在线
            self.status = OnlineStatus.ONLINE
    
    def set_typing(self, chat_id: int):
        """设置在指定聊天中输入"""
        self.typing_in_chats.add(chat_id)
        self.update_activity()
    
    def stop_typing(self, chat_id: int):
        """停止在指定聊天中输入"""
        self.typing_in_chats.discard(chat_id)
    
    def is_typing_in_chat(self, chat_id: int) -> bool:
        """检查是否在指定聊天中输入"""
        return chat_id in self.typing_in_chats
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "status": self.status.value,
            "last_seen": self.last_seen.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "typing_in_chats": list(self.typing_in_chats),
            "custom_status_message": self.custom_status_message
        }


class StatusSyncService:
    """在线状态同步服务"""
    
    def __init__(self):
        # 用户在线状态：user_id -> UserPresence
        self.user_presence: Dict[int, UserPresence] = {}
        
        # 自动离开检测间隔（秒）
        self.away_threshold = 300  # 5分钟无活动自动设为离开
        self.offline_threshold = 1800  # 30分钟无活动自动设为离线
        
        # 输入状态超时（秒）
        self.typing_timeout = 10  # 10秒后自动停止输入状态
        
        # 启动后台任务
        self._background_tasks_started = False
    
    async def start_background_tasks(self):
        """启动后台任务"""
        if not self._background_tasks_started:
            asyncio.create_task(self._status_monitor_task())
            asyncio.create_task(self._typing_timeout_task())
            self._background_tasks_started = True
            logger.info("状态同步后台任务已启动")
    
    async def user_connected(self, user_id: int) -> Dict[str, Any]:
        """用户连接时的状态处理
        
        Args:
            user_id: 用户ID
            
        Returns:
            状态变化信息
        """
        try:
            # 获取或创建用户状态
            if user_id not in self.user_presence:
                self.user_presence[user_id] = UserPresence(user_id, OnlineStatus.ONLINE)
            else:
                # 更新为在线状态
                old_status = self.user_presence[user_id].status
                self.user_presence[user_id].status = OnlineStatus.ONLINE
                self.user_presence[user_id].update_activity()
            
            # 广播用户上线状态
            await self._broadcast_user_status_change(user_id, OnlineStatus.ONLINE)
            
            result = {
                "user_id": user_id,
                "status": OnlineStatus.ONLINE.value,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"用户 {user_id} 上线")
            return result
            
        except Exception as e:
            logger.error(f"处理用户连接状态失败: {e}")
            return {"error": str(e)}
    
    async def user_disconnected(self, user_id: int) -> Dict[str, Any]:
        """用户断开连接时的状态处理
        
        Args:
            user_id: 用户ID
            
        Returns:
            状态变化信息
        """
        try:
            if user_id in self.user_presence:
                # 检查用户是否还有其他连接
                if not connection_manager.is_user_online(user_id):
                    # 更新为离线状态
                    self.user_presence[user_id].status = OnlineStatus.OFFLINE
                    self.user_presence[user_id].last_seen = datetime.utcnow()
                    
                    # 清除所有输入状态
                    typing_chats = list(self.user_presence[user_id].typing_in_chats)
                    for chat_id in typing_chats:
                        await self.stop_typing(user_id, chat_id)
                    
                    # 广播用户离线状态
                    await self._broadcast_user_status_change(user_id, OnlineStatus.OFFLINE)
            
            result = {
                "user_id": user_id,
                "status": OnlineStatus.OFFLINE.value,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"用户 {user_id} 离线")
            return result
            
        except Exception as e:
            logger.error(f"处理用户断开状态失败: {e}")
            return {"error": str(e)}
    
    async def update_user_status(
        self,
        user_id: int,
        status: OnlineStatus,
        custom_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新用户状态
        
        Args:
            user_id: 用户ID
            status: 新状态
            custom_message: 自定义状态消息
            
        Returns:
            更新结果
        """
        try:
            old_status = None

            if user_id not in self.user_presence:
                self.user_presence[user_id] = UserPresence(user_id, status)
                self.user_presence[user_id].custom_status_message = custom_message
                # 新用户连接，广播状态
                await self._broadcast_user_status_change(user_id, status, custom_message)
            else:
                old_status = self.user_presence[user_id].status
                self.user_presence[user_id].status = status
                self.user_presence[user_id].custom_status_message = custom_message
                self.user_presence[user_id].update_activity()

                # 只有状态真正改变时才广播
                if old_status != status:
                    await self._broadcast_user_status_change(user_id, status, custom_message)

            result = {
                "user_id": user_id,
                "old_status": old_status.value if old_status else None,
                "new_status": status.value,
                "custom_message": custom_message,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"用户 {user_id} 状态更新: {status.value}")
            return result
            
        except Exception as e:
            logger.error(f"更新用户状态失败: {e}")
            return {"error": str(e)}
    
    async def start_typing(self, user_id: int, chat_id: int) -> bool:
        """开始输入状态
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            是否成功
        """
        try:
            if user_id not in self.user_presence:
                self.user_presence[user_id] = UserPresence(user_id)
            
            # 设置输入状态
            was_typing = self.user_presence[user_id].is_typing_in_chat(chat_id)
            self.user_presence[user_id].set_typing(chat_id)
            
            # 只有状态改变时才广播
            if not was_typing:
                await self._broadcast_typing_status(user_id, chat_id, TypingStatus.TYPING)
            
            logger.debug(f"用户 {user_id} 开始在聊天 {chat_id} 中输入")
            return True
            
        except Exception as e:
            logger.error(f"设置输入状态失败: {e}")
            return False
    
    async def stop_typing(self, user_id: int, chat_id: int) -> bool:
        """停止输入状态
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            是否成功
        """
        try:
            if user_id in self.user_presence:
                was_typing = self.user_presence[user_id].is_typing_in_chat(chat_id)
                self.user_presence[user_id].stop_typing(chat_id)
                
                # 只有状态改变时才广播
                if was_typing:
                    await self._broadcast_typing_status(user_id, chat_id, TypingStatus.STOPPED)
            
            logger.debug(f"用户 {user_id} 停止在聊天 {chat_id} 中输入")
            return True
            
        except Exception as e:
            logger.error(f"停止输入状态失败: {e}")
            return False
    
    async def update_user_activity(self, user_id: int):
        """更新用户活跃时间
        
        Args:
            user_id: 用户ID
        """
        if user_id in self.user_presence:
            self.user_presence[user_id].update_activity()
    
    def get_user_status(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户状态
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户状态信息
        """
        if user_id in self.user_presence:
            return self.user_presence[user_id].to_dict()
        return None
    
    def get_online_users_status(self) -> List[Dict[str, Any]]:
        """获取所有在线用户状态
        
        Returns:
            在线用户状态列表
        """
        online_users = []
        for user_id, presence in self.user_presence.items():
            if presence.status != OnlineStatus.OFFLINE:
                online_users.append(presence.to_dict())
        return online_users
    
    async def _broadcast_user_status_change(
        self,
        user_id: int,
        status: OnlineStatus,
        custom_message: Optional[str] = None
    ):
        """广播用户状态变化
        
        Args:
            user_id: 用户ID
            status: 新状态
            custom_message: 自定义状态消息
        """
        try:
            # 创建状态变化消息
            status_message = create_websocket_message(
                MessageType.USER_STATUS,
                {
                    "user_id": user_id,
                    "status": status.value,
                    "custom_message": custom_message,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # 广播给所有在线用户（可以优化为只广播给好友）
            online_users = connection_manager.get_online_users()
            success_count = 0
            
            for target_user_id in online_users:
                if target_user_id != user_id:  # 不发送给自己
                    if await connection_manager.send_personal_message(target_user_id, status_message):
                        success_count += 1
            
            logger.debug(f"用户 {user_id} 状态变化广播给 {success_count} 个用户")
            
        except Exception as e:
            logger.error(f"广播用户状态变化失败: {e}")
    
    async def _broadcast_typing_status(
        self,
        user_id: int,
        chat_id: int,
        typing_status: TypingStatus
    ):
        """广播输入状态
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            typing_status: 输入状态
        """
        try:
            # 创建输入状态消息
            typing_message = create_websocket_message(
                MessageType.TYPING_STATUS,
                {
                    "user_id": user_id,
                    "chat_id": chat_id,
                    "status": typing_status.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # 发送给聊天室其他成员
            chat_members = connection_manager.chat_members.get(chat_id, set())
            success_count = 0
            
            for member_id in chat_members:
                if member_id != user_id and connection_manager.is_user_online(member_id):
                    if await connection_manager.send_personal_message(member_id, typing_message):
                        success_count += 1
            
            logger.debug(f"用户 {user_id} 在聊天 {chat_id} 的输入状态广播给 {success_count} 个成员")
            
        except Exception as e:
            logger.error(f"广播输入状态失败: {e}")
    
    async def _status_monitor_task(self):
        """状态监控后台任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                current_time = datetime.utcnow()
                status_changes = []
                
                for user_id, presence in self.user_presence.items():
                    if presence.status == OnlineStatus.OFFLINE:
                        continue
                    
                    # 计算无活动时间
                    inactive_time = (current_time - presence.last_activity).total_seconds()
                    
                    # 检查是否需要设为离开状态
                    if (presence.status == OnlineStatus.ONLINE and 
                        inactive_time > self.away_threshold):
                        presence.status = OnlineStatus.AWAY
                        status_changes.append((user_id, OnlineStatus.AWAY))
                    
                    # 检查是否需要设为离线状态（如果连接已断开）
                    elif (inactive_time > self.offline_threshold and 
                          not connection_manager.is_user_online(user_id)):
                        presence.status = OnlineStatus.OFFLINE
                        presence.last_seen = current_time
                        status_changes.append((user_id, OnlineStatus.OFFLINE))
                
                # 广播状态变化
                for user_id, new_status in status_changes:
                    await self._broadcast_user_status_change(user_id, new_status)
                
                if status_changes:
                    logger.info(f"自动状态更新: {len(status_changes)} 个用户")
                
            except Exception as e:
                logger.error(f"状态监控任务错误: {e}")
    
    async def _typing_timeout_task(self):
        """输入状态超时检查任务"""
        while True:
            try:
                await asyncio.sleep(5)  # 每5秒检查一次
                
                current_time = datetime.utcnow()
                timeout_users = []
                
                for user_id, presence in self.user_presence.items():
                    if presence.typing_in_chats:
                        # 检查输入状态是否超时
                        inactive_time = (current_time - presence.last_activity).total_seconds()
                        if inactive_time > self.typing_timeout:
                            # 清除所有输入状态
                            typing_chats = list(presence.typing_in_chats)
                            for chat_id in typing_chats:
                                timeout_users.append((user_id, chat_id))
                                presence.stop_typing(chat_id)
                
                # 广播输入状态停止
                for user_id, chat_id in timeout_users:
                    await self._broadcast_typing_status(user_id, chat_id, TypingStatus.STOPPED)
                
                if timeout_users:
                    logger.debug(f"输入状态超时清理: {len(timeout_users)} 个状态")
                
            except Exception as e:
                logger.error(f"输入状态超时任务错误: {e}")


# 全局状态同步服务实例
status_sync_service = StatusSyncService()


# 导出
__all__ = [
    'TypingStatus',
    'UserPresence',
    'StatusSyncService',
    'status_sync_service'
]
