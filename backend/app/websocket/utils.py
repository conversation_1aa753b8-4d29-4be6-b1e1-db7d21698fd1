"""WebSocket工具函数

提供WebSocket相关的工具函数和辅助方法
"""

import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from fastapi import WebSocket, WebSocketDisconnect
import logging

from app.schemas.websocket import (
    WebSocketMessage, MessageType, OnlineStatus,
    create_websocket_message, validate_message_data
)

logger = logging.getLogger(__name__)


class WebSocketUtils:
    """WebSocket工具类"""
    
    @staticmethod
    def generate_message_id() -> str:
        """生成消息ID"""
        return f"msg_{uuid.uuid4().hex[:12]}"
    
    @staticmethod
    def generate_connection_id() -> str:
        """生成连接ID"""
        return f"conn_{uuid.uuid4().hex[:12]}"
    
    @staticmethod
    async def safe_send_json(websocket: WebSocket, data: Dict[str, Any]) -> bool:
        """安全发送JSON数据"""
        try:
            await websocket.send_json(data)
            return True
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during send")
            return False
        except Exception as e:
            logger.error(f"Failed to send JSON data: {e}")
            return False
    
    @staticmethod
    async def safe_send_text(websocket: WebSocket, text: str) -> bool:
        """安全发送文本数据"""
        try:
            await websocket.send_text(text)
            return True
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during send")
            return False
        except Exception as e:
            logger.error(f"Failed to send text data: {e}")
            return False
    
    @staticmethod
    async def safe_receive_json(websocket: WebSocket) -> Optional[Dict[str, Any]]:
        """安全接收JSON数据"""
        try:
            data = await websocket.receive_json()
            return data
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during receive")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to receive JSON data: {e}")
            return None
    
    @staticmethod
    async def safe_receive_text(websocket: WebSocket) -> Optional[str]:
        """安全接收文本数据"""
        try:
            text = await websocket.receive_text()
            return text
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during receive")
            return None
        except Exception as e:
            logger.error(f"Failed to receive text data: {e}")
            return None
    
    @staticmethod
    def parse_websocket_message(data: Union[str, Dict[str, Any]]) -> Optional[WebSocketMessage]:
        """解析WebSocket消息"""
        try:
            if isinstance(data, str):
                data = json.loads(data)
            
            return WebSocketMessage.model_validate(data)
            
        except Exception as e:
            logger.error(f"Failed to parse WebSocket message: {e}")
            return None
    
    @staticmethod
    def create_error_message(
        error_code: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> WebSocketMessage:
        """创建错误消息"""
        return create_websocket_message(
            MessageType.ERROR,
            {
                "error_code": error_code,
                "message": message,
                "details": details or {}
            }
        )
    
    @staticmethod
    def create_heartbeat_message() -> WebSocketMessage:
        """创建心跳消息"""
        return create_websocket_message(
            MessageType.HEARTBEAT,
            {"timestamp": datetime.utcnow().isoformat()}
        )
    
    @staticmethod
    def create_chat_message(
        chat_id: int,
        sender_id: int,
        content: str,
        message_type: str = "text",
        reply_to_id: Optional[int] = None
    ) -> WebSocketMessage:
        """创建聊天消息"""
        return create_websocket_message(
            MessageType.CHAT_MESSAGE,
            {
                "chat_id": chat_id,
                "sender_id": sender_id,
                "content": content,
                "message_type": message_type,
                "reply_to_id": reply_to_id
            },
            WebSocketUtils.generate_message_id()
        )
    
    @staticmethod
    def create_typing_status_message(
        chat_id: int,
        user_id: int,
        is_typing: bool
    ) -> WebSocketMessage:
        """创建输入状态消息"""
        return create_websocket_message(
            MessageType.TYPING_STATUS,
            {
                "chat_id": chat_id,
                "user_id": user_id,
                "is_typing": is_typing
            }
        )
    
    @staticmethod
    def create_user_status_message(
        user_id: int,
        status: OnlineStatus,
        last_seen: Optional[datetime] = None
    ) -> WebSocketMessage:
        """创建用户状态消息"""
        return create_websocket_message(
            MessageType.USER_STATUS,
            {
                "user_id": user_id,
                "status": status.value,
                "last_seen": last_seen.isoformat() if last_seen else None
            }
        )
    
    @staticmethod
    def create_system_notification(
        title: str,
        content: str,
        level: str = "info",
        action_url: Optional[str] = None
    ) -> WebSocketMessage:
        """创建系统通知消息"""
        return create_websocket_message(
            MessageType.SYSTEM_NOTIFICATION,
            {
                "title": title,
                "content": content,
                "level": level,
                "action_url": action_url
            }
        )


class MessageValidator:
    """消息验证器"""
    
    @staticmethod
    def validate_message_type(message_type: str) -> bool:
        """验证消息类型"""
        try:
            MessageType(message_type)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_message_data(message: WebSocketMessage) -> bool:
        """验证消息数据"""
        try:
            # 验证消息类型
            if not MessageValidator.validate_message_type(message.type):
                return False
            
            # 验证消息数据
            validated_data = validate_message_data(message.type, message.data)
            return validated_data is not None
            
        except Exception as e:
            logger.error(f"Message validation failed: {e}")
            return False
    
    @staticmethod
    def sanitize_message_content(content: str) -> str:
        """清理消息内容"""
        if not content:
            return ""
        
        # 移除危险字符
        dangerous_chars = ['<script', '</script>', 'javascript:', 'onload=']
        sanitized = content
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()


class ConnectionTracker:
    """连接跟踪器"""
    
    def __init__(self):
        self.connection_events: List[Dict[str, Any]] = []
        self.max_events = 1000
    
    def log_connection_event(
        self,
        event_type: str,
        user_id: int,
        connection_id: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录连接事件"""
        event = {
            "event_type": event_type,
            "user_id": user_id,
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {}
        }
        
        self.connection_events.append(event)
        
        # 保持事件列表大小
        if len(self.connection_events) > self.max_events:
            self.connection_events = self.connection_events[-self.max_events:]
    
    def get_recent_events(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的连接事件"""
        return self.connection_events[-limit:]
    
    def get_user_events(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """获取用户的连接事件"""
        user_events = [
            event for event in self.connection_events
            if event["user_id"] == user_id
        ]
        return user_events[-limit:]


# 全局连接跟踪器
connection_tracker = ConnectionTracker()


# 导出
__all__ = [
    'WebSocketUtils',
    'MessageValidator',
    'ConnectionTracker',
    'connection_tracker'
]
