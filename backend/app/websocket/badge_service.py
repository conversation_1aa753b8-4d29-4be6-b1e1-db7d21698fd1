"""徽标数量广播服务

提供未读消息数量的实时广播功能
"""

import asyncio
from datetime import datetime
from typing import Optional, Dict, Set
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.core.database import get_db
from app.models.message import Message
from app.models.chat import Chat, ChatMember
from app.schemas.websocket import WebSocketMessage, MessageType, BadgeCountData, create_websocket_message
from app.websocket.connection_manager import connection_manager

logger = logging.getLogger(__name__)


class BadgeService:
    """徽标数量服务"""
    
    def __init__(self):
        self._user_badge_cache: Dict[int, int] = {}
    
    async def get_user_unread_count(self, user_id: int, db: AsyncSession) -> int:
        """获取用户未读消息数量"""
        try:
            # 查询用户参与的所有聊天
            user_chats_query = select(ChatMember.chat_id).where(
                ChatMember.user_id == user_id
            )
            user_chat_ids = (await db.execute(user_chats_query)).scalars().all()
            
            if not user_chat_ids:
                return 0
            
            # 查询这些聊天中的未读消息数量
            unread_query = select(func.count(Message.id)).where(
                Message.chat_id.in_(user_chat_ids),
                Message.sender_id != user_id,
                Message.is_read == False
            )
            
            unread_count = (await db.execute(unread_query)).scalar() or 0
            
            # 更新缓存
            self._user_badge_cache[user_id] = unread_count
            
            return unread_count
            
        except Exception as e:
            logger.error(f"Failed to get unread count for user {user_id}: {e}")
            return self._user_badge_cache.get(user_id, 0)
    
    async def broadcast_badge_count(self, user_id: int, unread_count: Optional[int] = None) -> bool:
        """广播用户的徽标数量"""
        try:
            # 如果没有提供未读数量，则查询数据库
            if unread_count is None:
                async for db in get_db():
                    unread_count = await self.get_user_unread_count(user_id, db)
                    break
            
            # 创建徽标数量消息
            badge_data = BadgeCountData(
                user_id=user_id,
                unread_count=unread_count,
                timestamp=datetime.utcnow()
            )
            
            message = create_websocket_message(
                MessageType.BADGE_COUNT,
                badge_data.model_dump()
            )
            
            # 发送给指定用户
            success = await connection_manager.send_personal_message(user_id, message)
            
            if success:
                logger.info(f"Badge count broadcasted to user {user_id}: {unread_count}")
            else:
                logger.warning(f"Failed to broadcast badge count to user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to broadcast badge count for user {user_id}: {e}")
            return False
    
    async def broadcast_badge_count_to_chat_members(self, chat_id: int, exclude_user_id: Optional[int] = None) -> int:
        """广播徽标数量给聊天室成员"""
        try:
            async for db in get_db():
                # 获取聊天室成员
                members_query = select(ChatMember.user_id).where(
                    ChatMember.chat_id == chat_id
                )
                member_ids = (await db.execute(members_query)).scalars().all()
                
                success_count = 0
                
                for user_id in member_ids:
                    if exclude_user_id and user_id == exclude_user_id:
                        continue
                    
                    # 获取用户未读数量并广播
                    unread_count = await self.get_user_unread_count(user_id, db)
                    if await self.broadcast_badge_count(user_id, unread_count):
                        success_count += 1
                
                logger.info(f"Badge count broadcasted to {success_count} members of chat {chat_id}")
                return success_count
                
        except Exception as e:
            logger.error(f"Failed to broadcast badge count to chat {chat_id} members: {e}")
            return 0
    
    async def update_badge_on_new_message(self, chat_id: int, sender_id: int):
        """新消息时更新徽标数量"""
        await self.broadcast_badge_count_to_chat_members(chat_id, exclude_user_id=sender_id)
    
    async def update_badge_on_message_read(self, user_id: int):
        """消息已读时更新徽标数量"""
        await self.broadcast_badge_count(user_id)
    
    async def update_badge_on_chat_enter(self, user_id: int, chat_id: int):
        """用户进入聊天时更新徽标数量（标记消息为已读）"""
        try:
            async for db in get_db():
                # 标记聊天中的消息为已读
                from sqlalchemy import update
                await db.execute(
                    update(Message)
                    .where(
                        Message.chat_id == chat_id,
                        Message.sender_id != user_id,
                        Message.is_read == False
                    )
                    .values(is_read=True, read_at=datetime.utcnow())
                )
                await db.commit()
                
                # 广播更新后的徽标数量
                await self.broadcast_badge_count(user_id)
                
        except Exception as e:
            logger.error(f"Failed to update badge on chat enter for user {user_id}, chat {chat_id}: {e}")
    
    def clear_cache(self, user_id: Optional[int] = None):
        """清除缓存"""
        if user_id:
            self._user_badge_cache.pop(user_id, None)
        else:
            self._user_badge_cache.clear()


# 全局实例
badge_service = BadgeService()
