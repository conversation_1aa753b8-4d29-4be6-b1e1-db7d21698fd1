"""WebSocket认证和授权模块

提供WebSocket连接的JWT认证、用户身份验证和权限检查功能
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from fastapi import WebSocket, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.models.user import User
from app.utils.security import TokenManager
from app.schemas.websocket import create_websocket_message, MessageType
from app.websocket.utils import WebSocketUtils

logger = logging.getLogger(__name__)


class WebSocketAuthenticator:
    """WebSocket认证器"""
    
    @staticmethod
    async def authenticate_websocket_user(
        token: Optional[str],
        db: AsyncSession
    ) -> Optional[User]:
        """认证WebSocket用户
        
        Args:
            token: JWT访问令牌
            db: 数据库会话
            
        Returns:
            用户对象或None
        """
        if not token:
            logger.warning("WebSocket连接缺少认证令牌")
            return None
        
        try:
            # 验证JWT令牌
            payload = await TokenManager.verify_token_with_blacklist(token, "access")
            user_id = int(payload.get("sub"))
            
            # 查询用户
            user = await db.get(User, user_id)
            if not user:
                logger.warning(f"WebSocket认证失败: 用户 {user_id} 不存在")
                return None
            
            if not user.is_active:
                logger.warning(f"WebSocket认证失败: 用户 {user_id} 账户已禁用")
                return None
            
            logger.info(f"WebSocket用户认证成功: {user.username} (ID: {user.id})")
            return user
            
        except Exception as e:
            logger.error(f"WebSocket认证失败: {e}")
            return None
    
    @staticmethod
    async def get_websocket_user_with_db(
        websocket: WebSocket,
        token: Optional[str]
    ) -> Optional[User]:
        """获取WebSocket用户（包含数据库会话管理）

        Args:
            websocket: WebSocket连接
            token: JWT访问令牌

        Returns:
            用户对象或None
        """
        try:
            # 获取数据库会话
            async for db in get_db_session():
                user = await WebSocketAuthenticator.authenticate_websocket_user(token, db)
                return user

        except Exception as e:
            logger.error(f"WebSocket用户获取失败: {e}")
            return None
    
    @staticmethod
    async def close_websocket_with_auth_error(
        websocket: WebSocket,
        error_code: int = 4001,
        reason: str = "Authentication failed"
    ):
        """因认证失败关闭WebSocket连接
        
        Args:
            websocket: WebSocket连接
            error_code: 错误代码
            reason: 错误原因
        """
        try:
            # 发送错误消息
            error_message = WebSocketUtils.create_error_message(
                "AUTHENTICATION_FAILED",
                reason
            )
            
            await WebSocketUtils.safe_send_json(
                websocket,
                error_message.model_dump()
            )
            
            # 关闭连接
            await websocket.close(code=error_code, reason=reason)
            
        except Exception as e:
            logger.error(f"关闭WebSocket连接失败: {e}")


class WebSocketAuthorizer:
    """WebSocket授权器"""
    
    @staticmethod
    def check_chat_access_permission(user: User, chat_id: int) -> bool:
        """检查用户是否有访问聊天的权限
        
        Args:
            user: 用户对象
            chat_id: 聊天ID
            
        Returns:
            是否有权限
        """
        # 这里简化处理，实际项目中需要查询数据库
        # 检查用户是否是聊天成员
        return True  # 暂时允许所有用户
    
    @staticmethod
    def check_message_send_permission(user: User, chat_id: int) -> bool:
        """检查用户是否有发送消息的权限
        
        Args:
            user: 用户对象
            chat_id: 聊天ID
            
        Returns:
            是否有权限
        """
        # 检查用户是否被禁言、是否是聊天成员等
        if not user.is_active:
            return False
        
        # 这里可以添加更多权限检查逻辑
        return True
    
    @staticmethod
    def check_broadcast_permission(user: User) -> bool:
        """检查用户是否有广播权限
        
        Args:
            user: 用户对象
            
        Returns:
            是否有权限
        """
        # 只有管理员可以广播
        return user.is_superuser
    
    @staticmethod
    def check_admin_permission(user: User) -> bool:
        """检查用户是否有管理员权限
        
        Args:
            user: 用户对象
            
        Returns:
            是否有权限
        """
        return user.is_superuser


class WebSocketPermissionChecker:
    """WebSocket权限检查器"""
    
    @staticmethod
    async def require_chat_access(
        user: User,
        chat_id: int,
        websocket: WebSocket
    ) -> bool:
        """要求聊天访问权限
        
        Args:
            user: 用户对象
            chat_id: 聊天ID
            websocket: WebSocket连接
            
        Returns:
            是否有权限
        """
        if not WebSocketAuthorizer.check_chat_access_permission(user, chat_id):
            await WebSocketAuthenticator.close_websocket_with_auth_error(
                websocket,
                4003,
                f"No permission to access chat {chat_id}"
            )
            return False
        
        return True
    
    @staticmethod
    async def require_message_send_permission(
        user: User,
        chat_id: int,
        websocket: WebSocket
    ) -> bool:
        """要求消息发送权限
        
        Args:
            user: 用户对象
            chat_id: 聊天ID
            websocket: WebSocket连接
            
        Returns:
            是否有权限
        """
        if not WebSocketAuthorizer.check_message_send_permission(user, chat_id):
            error_message = WebSocketUtils.create_error_message(
                "PERMISSION_DENIED",
                f"No permission to send message to chat {chat_id}"
            )
            
            await WebSocketUtils.safe_send_json(
                websocket,
                error_message.model_dump()
            )
            return False
        
        return True
    
    @staticmethod
    async def require_admin_permission(
        user: User,
        websocket: WebSocket
    ) -> bool:
        """要求管理员权限
        
        Args:
            user: 用户对象
            websocket: WebSocket连接
            
        Returns:
            是否有权限
        """
        if not WebSocketAuthorizer.check_admin_permission(user):
            error_message = WebSocketUtils.create_error_message(
                "PERMISSION_DENIED",
                "Admin permission required"
            )
            
            await WebSocketUtils.safe_send_json(
                websocket,
                error_message.model_dump()
            )
            return False
        
        return True


class WebSocketSessionManager:
    """WebSocket会话管理器"""
    
    def __init__(self):
        # 用户会话信息：user_id -> session_info
        self.user_sessions: Dict[int, Dict[str, Any]] = {}
    
    async def create_session(
        self,
        user: User,
        connection_id: str,
        websocket: WebSocket
    ) -> Dict[str, Any]:
        """创建用户会话
        
        Args:
            user: 用户对象
            connection_id: 连接ID
            websocket: WebSocket连接
            
        Returns:
            会话信息
        """
        session_info = {
            "user_id": user.id,
            "username": user.username,
            "connection_id": connection_id,
            "connected_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "permissions": self._get_user_permissions(user),
            "is_admin": user.is_superuser
        }
        
        self.user_sessions[user.id] = session_info
        
        logger.info(f"WebSocket会话创建: 用户 {user.username} (连接 {connection_id})")
        return session_info
    
    async def update_session_activity(self, user_id: int):
        """更新会话活动时间
        
        Args:
            user_id: 用户ID
        """
        if user_id in self.user_sessions:
            self.user_sessions[user_id]["last_activity"] = datetime.utcnow()
    
    async def remove_session(self, user_id: int):
        """移除用户会话
        
        Args:
            user_id: 用户ID
        """
        if user_id in self.user_sessions:
            session_info = self.user_sessions.pop(user_id)
            logger.info(f"WebSocket会话移除: 用户 {session_info['username']}")
    
    def get_session(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户会话信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话信息或None
        """
        return self.user_sessions.get(user_id)
    
    def _get_user_permissions(self, user: User) -> List[str]:
        """获取用户权限列表
        
        Args:
            user: 用户对象
            
        Returns:
            权限列表
        """
        permissions = ["chat:read", "chat:send"]
        
        if user.is_superuser:
            permissions.extend([
                "admin:broadcast",
                "admin:manage_users",
                "admin:view_stats"
            ])
        
        return permissions


# 全局会话管理器实例
session_manager = WebSocketSessionManager()


# 导出
__all__ = [
    'WebSocketAuthenticator',
    'WebSocketAuthorizer', 
    'WebSocketPermissionChecker',
    'WebSocketSessionManager',
    'session_manager'
]
