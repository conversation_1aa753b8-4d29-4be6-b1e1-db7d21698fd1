"""WebSocket消息推送服务

提供实时消息推送、消息路由和广播功能
"""

import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from enum import Enum

from app.schemas.websocket import (
    MessageType, create_websocket_message, WebSocketMessage
)
from app.websocket.connection_manager import connection_manager
from app.websocket.utils import WebSocketUtils
from app.websocket.auth import session_manager

logger = logging.getLogger(__name__)


class MessagePriority(str, Enum):
    """消息优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MessagePushService:
    """消息推送服务"""
    
    def __init__(self):
        # 消息队列：优先级 -> 消息列表
        self.message_queues: Dict[MessagePriority, List[Dict[str, Any]]] = {
            priority: [] for priority in MessagePriority
        }
        
        # 离线消息存储：user_id -> 消息列表
        self.offline_messages: Dict[int, List[Dict[str, Any]]] = {}
    
    async def push_chat_message(
        self,
        chat_id: int,
        sender_id: int,
        content: str,
        message_type: str = "text",
        priority: MessagePriority = MessagePriority.NORMAL,
        reply_to_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """推送聊天消息
        
        Args:
            chat_id: 聊天室ID
            sender_id: 发送者ID
            content: 消息内容
            message_type: 消息类型
            priority: 消息优先级
            reply_to_id: 回复的消息ID
            
        Returns:
            推送结果
        """
        try:
            # 创建消息
            message = create_websocket_message(
                MessageType.CHAT_MESSAGE,
                {
                    "chat_id": chat_id,
                    "sender_id": sender_id,
                    "content": content,
                    "message_type": message_type,
                    "reply_to_id": reply_to_id,
                    "priority": priority.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # 获取聊天室成员（这里简化处理）
            chat_members = await self._get_chat_members(chat_id)
            
            # 推送给在线成员
            online_count = 0
            offline_count = 0
            
            for member_id in chat_members:
                if member_id == sender_id:
                    continue  # 跳过发送者
                
                if await self._is_user_online(member_id):
                    if await self._push_to_online_user(member_id, message.model_dump()):
                        online_count += 1
                else:
                    # 存储离线消息到Redis
                    from app.websocket.redis_message_queue import redis_message_queue_service
                    await redis_message_queue_service.store_offline_message(
                        member_id,
                        message.model_dump(),
                        priority
                    )
                    offline_count += 1
            
            result = {
                "message_id": message.message_id,
                "chat_id": chat_id,
                "online_recipients": online_count,
                "offline_recipients": offline_count,
                "total_recipients": online_count + offline_count,
                "timestamp": message.timestamp
            }
            
            logger.info(f"聊天消息推送完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"推送聊天消息失败: {e}")
            return {"error": str(e)}
    
    async def push_private_message(
        self,
        sender_id: int,
        receiver_id: int,
        content: str,
        message_type: str = "text",
        priority: MessagePriority = MessagePriority.NORMAL
    ) -> Dict[str, Any]:
        """推送私聊消息
        
        Args:
            sender_id: 发送者ID
            receiver_id: 接收者ID
            content: 消息内容
            message_type: 消息类型
            priority: 消息优先级
            
        Returns:
            推送结果
        """
        try:
            # 创建私聊消息
            message = create_websocket_message(
                MessageType.PRIVATE_MESSAGE,
                {
                    "sender_id": sender_id,
                    "receiver_id": receiver_id,
                    "content": content,
                    "message_type": message_type,
                    "priority": priority.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # 推送消息
            if await self._is_user_online(receiver_id):
                success = await self._push_to_online_user(receiver_id, message.model_dump())
                status = "delivered" if success else "failed"
            else:
                # 存储离线消息到Redis
                from app.websocket.redis_message_queue import redis_message_queue_service
                await redis_message_queue_service.store_offline_message(
                    receiver_id,
                    message.model_dump(),
                    priority
                )
                status = "stored_offline"
            
            result = {
                "message_id": message.message_id,
                "sender_id": sender_id,
                "receiver_id": receiver_id,
                "status": status,
                "timestamp": message.timestamp
            }
            
            logger.info(f"私聊消息推送: {sender_id} -> {receiver_id}, 状态: {status}")
            return result
            
        except Exception as e:
            logger.error(f"推送私聊消息失败: {e}")
            return {"error": str(e)}
    
    async def push_system_notification(
        self,
        title: str,
        content: str,
        target_users: Optional[List[int]] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
        level: str = "info"
    ) -> Dict[str, Any]:
        """推送系统通知
        
        Args:
            title: 通知标题
            content: 通知内容
            target_users: 目标用户列表（None表示广播给所有用户）
            priority: 消息优先级
            level: 通知级别
            
        Returns:
            推送结果
        """
        try:
            # 创建系统通知
            notification = create_websocket_message(
                MessageType.SYSTEM_NOTIFICATION,
                {
                    "title": title,
                    "content": content,
                    "level": level,
                    "priority": priority.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            if target_users:
                # 推送给指定用户
                online_count = 0
                offline_count = 0
                
                for user_id in target_users:
                    if await self._is_user_online(user_id):
                        if await self._push_to_online_user(user_id, notification.model_dump()):
                            online_count += 1
                    else:
                        await self._store_offline_message(user_id, notification.model_dump())
                        offline_count += 1
                
                result = {
                    "notification_id": notification.message_id,
                    "target_users": len(target_users),
                    "online_recipients": online_count,
                    "offline_recipients": offline_count
                }
            else:
                # 广播给所有在线用户
                online_users = await self._get_all_online_users()
                success_count = 0
                
                for user_id in online_users:
                    if await self._push_to_online_user(user_id, notification.model_dump()):
                        success_count += 1
                
                result = {
                    "notification_id": notification.message_id,
                    "broadcast": True,
                    "online_recipients": success_count,
                    "total_online_users": len(online_users)
                }
            
            logger.info(f"系统通知推送完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"推送系统通知失败: {e}")
            return {"error": str(e)}
    
    async def push_message_status_update(
        self,
        user_id: int,
        message_id: str,
        status: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """推送消息状态更新
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            status: 新状态
            additional_data: 额外数据
            
        Returns:
            是否推送成功
        """
        try:
            status_update = create_websocket_message(
                MessageType.MESSAGE_STATUS,
                {
                    "message_id": message_id,
                    "status": status,
                    "timestamp": datetime.utcnow().isoformat(),
                    **(additional_data or {})
                }
            )
            
            if await self._is_user_online(user_id):
                success = await self._push_to_online_user(user_id, status_update.model_dump())
                logger.debug(f"消息状态更新推送: 用户 {user_id}, 消息 {message_id} -> {status}")
                return success
            else:
                # 状态更新通常不需要离线存储
                logger.debug(f"用户 {user_id} 不在线，跳过状态更新推送")
                return False
            
        except Exception as e:
            logger.error(f"推送消息状态更新失败: {e}")
            return False
    
    async def get_offline_messages(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的离线消息
        
        Args:
            user_id: 用户ID
            
        Returns:
            离线消息列表
        """
        messages = self.offline_messages.get(user_id, [])
        
        # 清空离线消息
        if user_id in self.offline_messages:
            del self.offline_messages[user_id]
        
        logger.info(f"获取用户 {user_id} 的离线消息: {len(messages)} 条")
        return messages
    
    async def _get_chat_members(self, chat_id: int) -> List[int]:
        """获取聊天室成员列表
        
        Args:
            chat_id: 聊天室ID
            
        Returns:
            成员ID列表
        """
        # 这里简化处理，实际项目中需要查询数据库
        # 返回所有在线用户作为聊天室成员
        return list(connection_manager.user_connections.keys())
    
    async def _is_user_online(self, user_id: int) -> bool:
        """检查用户是否在线
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否在线
        """
        return user_id in connection_manager.user_connections and \
               len(connection_manager.user_connections[user_id]) > 0
    
    async def _push_to_online_user(self, user_id: int, message_data: Dict[str, Any]) -> bool:
        """推送消息到在线用户
        
        Args:
            user_id: 用户ID
            message_data: 消息数据
            
        Returns:
            是否推送成功
        """
        try:
            user_connections = connection_manager.user_connections.get(user_id, set())
            
            if not user_connections:
                return False
            
            success = False
            
            # 推送到用户的所有连接
            for connection_id in user_connections.copy():
                websocket = connection_manager.active_connections.get(connection_id)
                if websocket:
                    if await WebSocketUtils.safe_send_json(websocket, message_data):
                        success = True
                    else:
                        # 连接已断开，清理
                        await connection_manager.disconnect(connection_id)
            
            return success
            
        except Exception as e:
            logger.error(f"推送到在线用户 {user_id} 失败: {e}")
            return False
    
    async def _store_offline_message(self, user_id: int, message_data: Dict[str, Any]):
        """存储离线消息
        
        Args:
            user_id: 用户ID
            message_data: 消息数据
        """
        if user_id not in self.offline_messages:
            self.offline_messages[user_id] = []
        
        # 添加存储时间戳
        message_data["stored_at"] = datetime.utcnow().isoformat()
        
        self.offline_messages[user_id].append(message_data)
        
        # 限制离线消息数量（防止内存溢出）
        max_offline_messages = 100
        if len(self.offline_messages[user_id]) > max_offline_messages:
            self.offline_messages[user_id] = self.offline_messages[user_id][-max_offline_messages:]
        
        logger.debug(f"存储离线消息: 用户 {user_id}, 总计 {len(self.offline_messages[user_id])} 条")
    
    async def _get_all_online_users(self) -> List[int]:
        """获取所有在线用户
        
        Returns:
            在线用户ID列表
        """
        return [
            user_id for user_id, connections in connection_manager.user_connections.items()
            if connections
        ]


# 全局消息推送服务实例
message_push_service = MessagePushService()


# 导出
__all__ = [
    'MessagePriority',
    'MessagePushService',
    'message_push_service'
]
