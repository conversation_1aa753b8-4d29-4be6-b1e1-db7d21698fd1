"""WebSocket事件处理器

处理各种WebSocket事件和消息类型
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import WebSocket
import logging

from app.schemas.websocket import (
    WebSocketMessage, MessageType, OnlineStatus,
    ChatMessageData, TypingStatusData, UserStatusData,
    create_websocket_message
)
from app.websocket.connection_manager import connection_manager
from app.websocket.utils import (
    WebSocketUtils, MessageValidator, connection_tracker
)
from app.websocket.auth import (
    WebSocketPer<PERSON><PERSON>he<PERSON>, WebSocketAuthorizer, session_manager
)

logger = logging.getLogger(__name__)


class WebSocketEventHandler:
    """WebSocket事件处理器"""
    
    def __init__(self):
        # 事件处理器映射
        self.event_handlers = {
            MessageType.HEARTBEAT: self._handle_heartbeat,
            MessageType.CHAT_MESSAGE: self._handle_chat_message,
            MessageType.TYPING_STATUS: self._handle_typing_status,
            MessageType.USER_STATUS: self._handle_user_status,
            MessageType.MESSAGE_STATUS: self._handle_message_status,
        }
    
    async def handle_message(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理WebSocket消息"""
        try:
            # 验证消息
            if not MessageValidator.validate_message_data(message):
                await self._send_error(
                    websocket,
                    "INVALID_MESSAGE",
                    "消息格式无效"
                )
                return False
            
            # 获取处理器
            handler = self.event_handlers.get(message.type)
            if not handler:
                await self._send_error(
                    websocket,
                    "UNSUPPORTED_MESSAGE_TYPE",
                    f"不支持的消息类型: {message.type}"
                )
                return False
            
            # 处理消息
            return await handler(websocket, connection_id, message)
            
        except Exception as e:
            logger.error(f"Failed to handle message: {e}")
            await self._send_error(
                websocket,
                "MESSAGE_HANDLING_ERROR",
                "消息处理失败"
            )
            return False
    
    async def _handle_heartbeat(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理心跳消息"""
        try:
            # 更新心跳时间
            await connection_manager.update_heartbeat(connection_id)
            
            # 发送心跳响应
            response = create_websocket_message(
                MessageType.HEARTBEAT,
                {"timestamp": datetime.utcnow().isoformat()}
            )
            
            return await WebSocketUtils.safe_send_json(
                websocket,
                response.model_dump()
            )
            
        except Exception as e:
            logger.error(f"Heartbeat handling failed: {e}")
            return False
    
    async def _handle_chat_message(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理聊天消息"""
        try:
            # 验证消息数据
            chat_data = ChatMessageData.model_validate(message.data)
            
            # 获取发送者信息
            conn_info = connection_manager.connection_info.get(connection_id)
            if not conn_info:
                await self._send_error(websocket, "INVALID_CONNECTION", "无效的连接")
                return False
            
            sender_id = conn_info.user_id
            
            # 检查发送者权限（这里简化处理）
            if chat_data.sender_id != sender_id:
                await self._send_error(websocket, "PERMISSION_DENIED", "权限不足")
                return False
            
            # 清理消息内容
            sanitized_content = MessageValidator.sanitize_message_content(chat_data.content)
            
            # 创建转发消息
            forward_message = create_websocket_message(
                MessageType.CHAT_MESSAGE,
                {
                    "chat_id": chat_data.chat_id,
                    "sender_id": sender_id,
                    "content": sanitized_content,
                    "message_type": chat_data.message_type,
                    "reply_to_id": chat_data.reply_to_id,
                    "timestamp": datetime.utcnow().isoformat()
                },
                WebSocketUtils.generate_message_id()
            )
            
            # 更新用户活跃时间
            from app.websocket.status_sync import status_sync_service
            await status_sync_service.update_user_activity(sender_id)

            # 实时推送消息到聊天室成员（排除发送者）
            success_count = await self._push_message_to_chat_members(
                chat_data.chat_id,
                forward_message.model_dump(),
                sender_id
            )
            
            # 发送确认给发送者
            confirmation = create_websocket_message(
                MessageType.MESSAGE_STATUS,
                {
                    "message_id": forward_message.message_id,
                    "status": "sent",
                    "recipients": success_count
                }
            )
            
            await WebSocketUtils.safe_send_json(websocket, confirmation.model_dump())
            
            logger.info(f"Chat message sent to {success_count} recipients in chat {chat_data.chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Chat message handling failed: {e}")
            await self._send_error(websocket, "CHAT_MESSAGE_ERROR", "聊天消息处理失败")
            return False
    
    async def _handle_typing_status(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理输入状态消息"""
        try:
            # 验证消息数据
            typing_data = TypingStatusData.model_validate(message.data)

            # 获取发送者信息
            conn_info = connection_manager.connection_info.get(connection_id)
            if not conn_info:
                await self._send_error(websocket, "INVALID_CONNECTION", "无效的连接")
                return False

            sender_id = conn_info.user_id

            # 检查权限
            if typing_data.user_id != sender_id:
                await self._send_error(websocket, "PERMISSION_DENIED", "权限不足")
                return False

            # 使用状态同步服务处理输入状态
            from app.websocket.status_sync import status_sync_service

            if typing_data.status == "typing":
                success = await status_sync_service.start_typing(sender_id, typing_data.chat_id)
            else:  # stopped
                success = await status_sync_service.stop_typing(sender_id, typing_data.chat_id)

            # 更新用户活跃时间
            await status_sync_service.update_user_activity(sender_id)

            logger.debug(f"输入状态处理: 用户 {sender_id}, 聊天 {typing_data.chat_id}, 状态 {typing_data.status}")
            return success

        except Exception as e:
            logger.error(f"Typing status handling failed: {e}")
            await self._send_error(websocket, "TYPING_STATUS_ERROR", "输入状态处理失败")
            return False
    
    async def _handle_user_status(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理用户状态消息"""
        try:
            # 验证消息数据
            status_data = UserStatusData.model_validate(message.data)

            # 获取发送者信息
            conn_info = connection_manager.connection_info.get(connection_id)
            if not conn_info:
                await self._send_error(websocket, "INVALID_CONNECTION", "无效的连接")
                return False

            sender_id = conn_info.user_id

            # 检查权限（只能更新自己的状态）
            if status_data.user_id != sender_id:
                await self._send_error(websocket, "PERMISSION_DENIED", "只能更新自己的状态")
                return False

            # 使用状态同步服务更新用户状态
            from app.websocket.status_sync import status_sync_service

            # 提取自定义状态消息（如果有）
            custom_message = getattr(status_data, 'custom_message', None)

            result = await status_sync_service.update_user_status(
                sender_id,
                status_data.status,
                custom_message
            )

            if "error" in result:
                await self._send_error(websocket, "STATUS_UPDATE_ERROR", "状态更新失败")
                return False

            # 发送确认消息
            confirmation = create_websocket_message(
                MessageType.USER_STATUS,
                {
                    "user_id": sender_id,
                    "status": status_data.status.value,
                    "updated": True,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

            await WebSocketUtils.safe_send_json(websocket, confirmation.model_dump())

            logger.info(f"用户状态更新: 用户 {sender_id} -> {status_data.status.value}")
            return True

        except Exception as e:
            logger.error(f"User status handling failed: {e}")
            await self._send_error(websocket, "USER_STATUS_ERROR", "用户状态处理失败")
            return False
    
    async def _handle_message_status(
        self,
        websocket: WebSocket,
        connection_id: str,
        message: WebSocketMessage
    ) -> bool:
        """处理消息状态消息"""
        try:
            from app.websocket.message_status_sync import message_status_sync_service

            # 获取发送者ID
            sender_id = await connection_manager.get_user_id_by_connection(connection_id)
            if not sender_id:
                await self._send_error(websocket, "UNAUTHORIZED", "未认证的连接")
                return False

            # 验证消息数据
            required_fields = ["message_id", "status"]
            for field in required_fields:
                if field not in message.data:
                    await self._send_error(websocket, "INVALID_DATA", f"缺少必需字段: {field}")
                    return False

            message_id = message.data["message_id"]
            status = message.data["status"]
            chat_id = message.data.get("chat_id")

            # 验证状态值
            if status not in ["delivered", "read"]:
                await self._send_error(websocket, "INVALID_STATUS", "无效的消息状态")
                return False

            # 处理状态更新
            if status == "delivered":
                result = await message_status_sync_service.mark_messages_as_delivered(
                    sender_id, [message_id]
                )
            elif status == "read":
                result = await message_status_sync_service.mark_message_as_read(
                    sender_id, message_id, chat_id
                )

            # 发送确认消息
            confirmation = create_websocket_message(
                MessageType.MESSAGE_STATUS,
                {
                    "message_id": message_id,
                    "status": status,
                    "user_id": sender_id,
                    "timestamp": result["timestamp"],
                    "confirmed": True
                }
            )

            await WebSocketUtils.safe_send_json(websocket, confirmation.model_dump())

            logger.info(f"消息状态更新成功: 用户 {sender_id}, 消息 {message_id} -> {status}")
            return True

        except Exception as e:
            logger.error(f"Message status handling failed: {e}")
            await self._send_error(websocket, "MESSAGE_STATUS_ERROR", "消息状态处理失败")
            return False
    
    async def _send_error(
        self,
        websocket: WebSocket,
        error_code: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """发送错误消息"""
        error_message = WebSocketUtils.create_error_message(
            error_code, message, details
        )
        
        await WebSocketUtils.safe_send_json(
            websocket,
            error_message.model_dump()
        )

    async def _push_message_to_chat_members(
        self,
        chat_id: int,
        message_data: Dict[str, Any],
        exclude_user_id: Optional[int] = None
    ) -> int:
        """推送消息到聊天室成员

        Args:
            chat_id: 聊天室ID
            message_data: 消息数据
            exclude_user_id: 排除的用户ID（通常是发送者）

        Returns:
            成功推送的用户数量
        """
        try:
            # 这里简化处理，实际项目中需要查询数据库获取聊天室成员
            # 获取所有在线用户（排除发送者）
            online_users = []
            for user_id, connections in connection_manager.user_connections.items():
                if user_id != exclude_user_id and connections:
                    online_users.append(user_id)

            success_count = 0

            # 推送消息到每个在线成员
            for user_id in online_users:
                if await self._push_message_to_user(user_id, message_data):
                    success_count += 1

            logger.info(f"消息推送到聊天 {chat_id}: {success_count}/{len(online_users)} 用户")
            return success_count

        except Exception as e:
            logger.error(f"推送消息到聊天室失败: {e}")
            return 0

    async def _push_message_to_user(
        self,
        user_id: int,
        message_data: Dict[str, Any]
    ) -> bool:
        """推送消息到指定用户

        Args:
            user_id: 用户ID
            message_data: 消息数据

        Returns:
            是否推送成功
        """
        try:
            # 获取用户的所有连接
            user_connections = connection_manager.user_connections.get(user_id, set())

            if not user_connections:
                logger.debug(f"用户 {user_id} 不在线，无法推送消息")
                return False

            success = False

            # 推送到用户的所有连接
            for connection_id in user_connections.copy():
                websocket = connection_manager.active_connections.get(connection_id)
                if websocket:
                    if await WebSocketUtils.safe_send_json(websocket, message_data):
                        success = True
                    else:
                        # 连接已断开，清理
                        await connection_manager.disconnect(connection_id)

            return success

        except Exception as e:
            logger.error(f"推送消息到用户 {user_id} 失败: {e}")
            return False


class WebSocketBroadcaster:
    """WebSocket广播器"""
    
    @staticmethod
    async def broadcast_friend_request(
        sender_id: int,
        receiver_id: int,
        request_id: int
    ):
        """广播好友请求"""
        message = create_websocket_message(
            MessageType.FRIEND_REQUEST,
            {
                "request_id": request_id,
                "sender_id": sender_id,
                "receiver_id": receiver_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        await connection_manager.send_personal_message(receiver_id, message)
    
    @staticmethod
    async def broadcast_friend_accepted(
        sender_id: int,
        receiver_id: int
    ):
        """广播好友请求被接受"""
        message = create_websocket_message(
            MessageType.FRIEND_ACCEPTED,
            {
                "sender_id": sender_id,
                "receiver_id": receiver_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # 通知双方
        await connection_manager.send_personal_message(sender_id, message)
        await connection_manager.send_personal_message(receiver_id, message)
    
    @staticmethod
    async def broadcast_chat_created(
        chat_id: int,
        creator_id: int,
        member_ids: List[int]
    ):
        """广播聊天创建"""
        message = create_websocket_message(
            MessageType.CHAT_CREATED,
            {
                "chat_id": chat_id,
                "creator_id": creator_id,
                "member_ids": member_ids,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # 通知所有成员
        for member_id in member_ids:
            await connection_manager.send_personal_message(member_id, message)
            # 添加成员到聊天室
            await connection_manager.add_user_to_chat(member_id, chat_id)
    
    @staticmethod
    async def broadcast_system_notification(
        title: str,
        content: str,
        target_users: Optional[List[int]] = None,
        level: str = "info"
    ):
        """广播系统通知"""
        message = WebSocketUtils.create_system_notification(
            title, content, level
        )
        
        if target_users:
            # 发送给指定用户
            for user_id in target_users:
                await connection_manager.send_personal_message(user_id, message)
        else:
            # 广播给所有在线用户
            await connection_manager.broadcast_to_all(message)

    @staticmethod
    async def push_private_message(
        sender_id: int,
        receiver_id: int,
        content: str,
        message_type: str = "text"
    ) -> bool:
        """推送私聊消息

        Args:
            sender_id: 发送者ID
            receiver_id: 接收者ID
            content: 消息内容
            message_type: 消息类型

        Returns:
            是否推送成功
        """
        try:
            # 创建私聊消息
            private_message = create_websocket_message(
                MessageType.PRIVATE_MESSAGE,
                {
                    "sender_id": sender_id,
                    "receiver_id": receiver_id,
                    "content": content,
                    "message_type": message_type,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

            # 推送给接收者
            success = await connection_manager.send_personal_message(
                receiver_id,
                private_message
            )

            if success:
                logger.info(f"私聊消息推送成功: {sender_id} -> {receiver_id}")
            else:
                logger.warning(f"私聊消息推送失败: 用户 {receiver_id} 不在线")

            return success

        except Exception as e:
            logger.error(f"推送私聊消息失败: {e}")
            return False

    @staticmethod
    async def push_group_message(
        chat_id: int,
        sender_id: int,
        content: str,
        message_type: str = "text",
        exclude_sender: bool = True
    ) -> int:
        """推送群组消息

        Args:
            chat_id: 聊天室ID
            sender_id: 发送者ID
            content: 消息内容
            message_type: 消息类型
            exclude_sender: 是否排除发送者

        Returns:
            成功推送的用户数量
        """
        try:
            # 创建群组消息
            group_message = create_websocket_message(
                MessageType.CHAT_MESSAGE,
                {
                    "chat_id": chat_id,
                    "sender_id": sender_id,
                    "content": content,
                    "message_type": message_type,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

            # 推送给聊天室成员
            success_count = await connection_manager.send_to_chat(
                chat_id,
                group_message,
                exclude_user_id=sender_id if exclude_sender else None
            )

            logger.info(f"群组消息推送: 聊天 {chat_id}, {success_count} 用户接收")
            return success_count

        except Exception as e:
            logger.error(f"推送群组消息失败: {e}")
            return 0

    @staticmethod
    async def push_message_status_update(
        user_id: int,
        message_id: str,
        status: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """推送消息状态更新

        Args:
            user_id: 用户ID
            message_id: 消息ID
            status: 新状态
            additional_data: 额外数据

        Returns:
            是否推送成功
        """
        try:
            status_update = create_websocket_message(
                MessageType.MESSAGE_STATUS,
                {
                    "message_id": message_id,
                    "status": status,
                    "timestamp": datetime.utcnow().isoformat(),
                    **(additional_data or {})
                }
            )

            success = await connection_manager.send_personal_message(user_id, status_update)

            if success:
                logger.debug(f"消息状态更新推送成功: 用户 {user_id}, 消息 {message_id} -> {status}")

            return success

        except Exception as e:
            logger.error(f"推送消息状态更新失败: {e}")
            return False


# 全局事件处理器实例
event_handler = WebSocketEventHandler()
broadcaster = WebSocketBroadcaster()


# 导出
__all__ = [
    'WebSocketEventHandler',
    'WebSocketBroadcaster',
    'event_handler',
    'broadcaster'
]
