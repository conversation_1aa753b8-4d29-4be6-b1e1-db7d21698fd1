"""WebSocket模块

提供WebSocket连接管理、事件处理和实时通信功能
"""

from .connection_manager import ConnectionManager, connection_manager
from .event_handler import WebSocketEventHandler, WebSocketBroadcaster, event_handler, broadcaster
from .utils import WebSocketUtils, MessageValidator, ConnectionTracker, connection_tracker
from .auth import (
    WebSocketAuthenticator, WebSocketAuthorizer, WebSocketPermissionChecker,
    WebSocketSessionManager, session_manager
)
from .message_push import (
    MessagePriority, MessagePushService, message_push_service
)
from .status_sync import (
    TypingStatus, UserPresence, StatusSyncService, status_sync_service
)
from .badge_service import BadgeService, badge_service

__all__ = [
    # 连接管理
    'ConnectionManager',
    'connection_manager',

    # 事件处理
    'WebSocketEventHandler',
    'WebSocketBroadcaster',
    'event_handler',
    'broadcaster',

    # 认证授权
    'WebSocketAuthenticator',
    'WebSocketAuthorizer',
    'WebSocketPermissionChecker',
    'WebSocketSessionManager',
    'session_manager',

    # 工具函数
    'WebSocketUtils',
    'MessageValidator',
    'ConnectionTracker',
    'connection_tracker',

    # 消息推送
    'MessagePriority',
    'MessagePushService',
    'message_push_service',

    # 状态同步
    'TypingStatus',
    'UserPresence',
    'StatusSyncService',
    'status_sync_service'
]