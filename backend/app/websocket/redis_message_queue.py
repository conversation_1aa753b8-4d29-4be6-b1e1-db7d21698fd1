"""基于Redis的消息队列和离线处理服务

提供持久化的消息队列、离线消息存储和用户上线同步功能
"""

import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from enum import Enum

from app.core.redis import redis_manager, redis_queue, redis_cache
from app.schemas.websocket import MessageType, create_websocket_message
from app.websocket.connection_manager import connection_manager
from app.utils.logging import get_logger

logger = get_logger(__name__)


class QueuePriority(str, Enum):
    """队列优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MessageQueueStatus(str, Enum):
    """消息队列状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    DELIVERED = "delivered"
    FAILED = "failed"
    EXPIRED = "expired"


class RedisMessageQueueService:
    """基于Redis的消息队列服务"""
    
    def __init__(self):
        # Redis键前缀
        self.QUEUE_PREFIX = "msg_queue"
        self.OFFLINE_PREFIX = "offline_msg"
        self.RETRY_PREFIX = "retry_msg"
        self.STATUS_PREFIX = "msg_status"
        
        # 配置参数
        self.max_offline_messages = 500  # 每用户最大离线消息数
        self.message_expire_days = 7     # 消息过期天数
        self.max_retry_attempts = 3      # 最大重试次数
        self.retry_delay_seconds = 60    # 重试延迟秒数
        self.batch_size = 50             # 批量处理大小
        
        # 后台任务状态
        self._background_tasks_started = False
        self._queue_processor_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def start_background_tasks(self):
        """启动后台任务"""
        if not self._background_tasks_started:
            self._queue_processor_task = asyncio.create_task(self._queue_processor())
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_messages())
            self._background_tasks_started = True
            logger.info("Redis消息队列后台任务已启动")
    
    async def stop_background_tasks(self):
        """停止后台任务"""
        if self._queue_processor_task:
            self._queue_processor_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        self._background_tasks_started = False
        logger.info("Redis消息队列后台任务已停止")
    
    async def enqueue_message(
        self,
        user_id: int,
        message_data: Dict[str, Any],
        priority: QueuePriority = QueuePriority.NORMAL,
        delay_seconds: int = 0
    ) -> str:
        """将消息加入队列
        
        Args:
            user_id: 目标用户ID
            message_data: 消息数据
            priority: 消息优先级
            delay_seconds: 延迟发送秒数
            
        Returns:
            消息队列ID
        """
        try:
            if not redis_manager.client:
                logger.warning("Redis不可用，无法加入消息队列")
                return ""
            
            # 生成消息ID
            message_id = f"{user_id}_{datetime.utcnow().timestamp()}_{priority.value}"
            
            # 构建队列消息
            queue_message = {
                "id": message_id,
                "user_id": user_id,
                "message_data": message_data,
                "priority": priority.value,
                "status": MessageQueueStatus.PENDING.value,
                "created_at": datetime.utcnow().isoformat(),
                "retry_count": 0,
                "max_retries": self.max_retry_attempts,
                "delay_until": (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat() if delay_seconds > 0 else None
            }
            
            # 选择队列名称
            queue_name = f"{self.QUEUE_PREFIX}:{priority.value}"
            
            # 加入Redis队列
            success = await redis_queue.push(queue_name, queue_message)
            
            if success:
                logger.debug(f"消息已加入队列: {message_id}, 用户: {user_id}, 优先级: {priority.value}")
                return message_id
            else:
                logger.error(f"消息加入队列失败: {message_id}")
                return ""
                
        except Exception as e:
            logger.error(f"加入消息队列失败: {e}")
            return ""
    
    async def store_offline_message(
        self,
        user_id: int,
        message_data: Dict[str, Any],
        priority: QueuePriority = QueuePriority.NORMAL
    ) -> bool:
        """存储离线消息
        
        Args:
            user_id: 用户ID
            message_data: 消息数据
            priority: 消息优先级
            
        Returns:
            是否存储成功
        """
        try:
            if not redis_manager.client:
                logger.warning("Redis不可用，无法存储离线消息")
                return False
            
            # 构建离线消息
            offline_message = {
                "message_data": message_data,
                "priority": priority.value,
                "stored_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(days=self.message_expire_days)).isoformat()
            }
            
            # Redis键
            offline_key = f"{self.OFFLINE_PREFIX}:{user_id}"
            
            # 获取现有离线消息
            existing_messages = await redis_cache.get_json(offline_key) or []
            
            # 添加新消息
            existing_messages.append(offline_message)
            
            # 限制消息数量
            if len(existing_messages) > self.max_offline_messages:
                existing_messages = existing_messages[-self.max_offline_messages:]
            
            # 存储到Redis（设置过期时间）
            expire_seconds = self.message_expire_days * 24 * 3600
            success = await redis_cache.set_json(offline_key, existing_messages, expire_seconds)
            
            if success:
                logger.debug(f"离线消息已存储: 用户 {user_id}, 总计 {len(existing_messages)} 条")
                return True
            else:
                logger.error(f"离线消息存储失败: 用户 {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"存储离线消息失败: {e}")
            return False
    
    async def get_offline_messages(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的离线消息
        
        Args:
            user_id: 用户ID
            
        Returns:
            离线消息列表
        """
        try:
            if not redis_manager.client:
                logger.warning("Redis不可用，无法获取离线消息")
                return []
            
            # Redis键
            offline_key = f"{self.OFFLINE_PREFIX}:{user_id}"
            
            # 获取离线消息
            messages = await redis_cache.get_json(offline_key) or []
            
            # 过滤过期消息
            current_time = datetime.utcnow()
            valid_messages = []
            
            for msg in messages:
                try:
                    expires_at = datetime.fromisoformat(msg.get("expires_at", ""))
                    if expires_at > current_time:
                        valid_messages.append(msg["message_data"])
                except (ValueError, KeyError):
                    # 忽略格式错误的消息
                    continue
            
            # 清空Redis中的离线消息
            await redis_cache.delete(offline_key)
            
            logger.info(f"获取用户 {user_id} 的离线消息: {len(valid_messages)} 条")
            return valid_messages
            
        except Exception as e:
            logger.error(f"获取离线消息失败: {e}")
            return []
    
    async def sync_offline_messages_on_connect(self, user_id: int) -> Dict[str, Any]:
        """用户上线时同步离线消息
        
        Args:
            user_id: 用户ID
            
        Returns:
            同步结果
        """
        try:
            # 获取离线消息
            offline_messages = await self.get_offline_messages(user_id)
            
            if not offline_messages:
                return {
                    "user_id": user_id,
                    "synced_count": 0,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            # 按时间排序（确保消息顺序）
            offline_messages.sort(key=lambda x: x.get("timestamp", ""))
            
            # 分批推送消息
            synced_count = 0
            failed_count = 0
            
            for i in range(0, len(offline_messages), self.batch_size):
                batch = offline_messages[i:i + self.batch_size]
                
                for message_data in batch:
                    success = await self._push_message_to_user(user_id, message_data)
                    if success:
                        synced_count += 1
                    else:
                        failed_count += 1
                
                # 批次间短暂延迟，避免过载
                if i + self.batch_size < len(offline_messages):
                    await asyncio.sleep(0.1)
            
            result = {
                "user_id": user_id,
                "synced_count": synced_count,
                "failed_count": failed_count,
                "total_messages": len(offline_messages),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"用户 {user_id} 离线消息同步完成: {synced_count}/{len(offline_messages)} 条成功")
            return result
            
        except Exception as e:
            logger.error(f"离线消息同步失败: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def _push_message_to_user(self, user_id: int, message_data: Dict[str, Any]) -> bool:
        """推送消息到用户

        Args:
            user_id: 用户ID
            message_data: 消息数据

        Returns:
            是否推送成功
        """
        try:
            # 检查用户是否在线
            if not connection_manager.is_user_online(user_id):
                return False

            # 创建WebSocket消息对象
            from app.schemas.websocket import WebSocketMessage

            # 如果message_data已经是WebSocketMessage格式，直接使用
            if isinstance(message_data, dict) and "type" in message_data and "data" in message_data:
                websocket_message = WebSocketMessage(**message_data)
            else:
                # 否则包装成WebSocket消息
                websocket_message = create_websocket_message(
                    MessageType.CHAT_MESSAGE,
                    message_data
                )

            # 发送消息
            success = await connection_manager.send_personal_message(user_id, websocket_message)
            return success

        except Exception as e:
            logger.error(f"推送消息到用户 {user_id} 失败: {e}")
            return False


    async def _queue_processor(self):
        """队列处理器后台任务"""
        logger.info("消息队列处理器已启动")

        while True:
            try:
                # 按优先级处理队列
                for priority in [QueuePriority.URGENT, QueuePriority.HIGH, QueuePriority.NORMAL, QueuePriority.LOW]:
                    queue_name = f"{self.QUEUE_PREFIX}:{priority.value}"

                    # 从队列获取消息（非阻塞）
                    message_json = await redis_queue.pop(queue_name, timeout=0)

                    if message_json:
                        await self._process_queue_message(message_json)

                # 处理重试队列
                await self._process_retry_queue()

                # 短暂休眠
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"队列处理器错误: {e}")
                await asyncio.sleep(5)

    async def _process_queue_message(self, message_json: str):
        """处理队列消息

        Args:
            message_json: JSON格式的消息
        """
        try:
            message = json.loads(message_json)
            user_id = message["user_id"]
            message_data = message["message_data"]

            # 检查延迟发送
            if message.get("delay_until"):
                delay_until = datetime.fromisoformat(message["delay_until"])
                if datetime.utcnow() < delay_until:
                    # 重新加入队列
                    queue_name = f"{self.QUEUE_PREFIX}:{message['priority']}"
                    await redis_queue.push(queue_name, message_json)
                    return

            # 尝试推送消息
            success = await self._push_message_to_user(user_id, message_data)

            if success:
                # 更新消息状态为已送达
                await self._update_message_status(message["id"], MessageQueueStatus.DELIVERED)
                logger.debug(f"队列消息处理成功: {message['id']}")
            else:
                # 推送失败，加入重试队列
                await self._add_to_retry_queue(message)

        except Exception as e:
            logger.error(f"处理队列消息失败: {e}")

    async def _process_retry_queue(self):
        """处理重试队列"""
        try:
            retry_queue_name = f"{self.RETRY_PREFIX}:queue"

            # 获取重试消息
            message_json = await redis_queue.pop(retry_queue_name, timeout=0)

            if message_json:
                message = json.loads(message_json)

                # 检查重试时间
                retry_after = datetime.fromisoformat(message.get("retry_after", ""))
                if datetime.utcnow() < retry_after:
                    # 重新加入重试队列
                    await redis_queue.push(retry_queue_name, message_json)
                    return

                # 检查重试次数
                if message["retry_count"] >= message["max_retries"]:
                    # 超过最大重试次数，标记为失败
                    await self._update_message_status(message["id"], MessageQueueStatus.FAILED)
                    logger.warning(f"消息重试次数超限，标记为失败: {message['id']}")
                    return

                # 尝试重新推送
                user_id = message["user_id"]
                message_data = message["message_data"]

                success = await self._push_message_to_user(user_id, message_data)

                if success:
                    await self._update_message_status(message["id"], MessageQueueStatus.DELIVERED)
                    logger.debug(f"重试消息处理成功: {message['id']}")
                else:
                    # 增加重试次数，重新加入重试队列
                    message["retry_count"] += 1
                    message["retry_after"] = (datetime.utcnow() + timedelta(seconds=self.retry_delay_seconds)).isoformat()
                    await redis_queue.push(retry_queue_name, json.dumps(message))
                    logger.debug(f"消息重试失败，重新加入重试队列: {message['id']}, 重试次数: {message['retry_count']}")

        except Exception as e:
            logger.error(f"处理重试队列失败: {e}")

    async def _add_to_retry_queue(self, message: Dict[str, Any]):
        """添加消息到重试队列

        Args:
            message: 消息数据
        """
        try:
            message["retry_count"] = message.get("retry_count", 0) + 1
            message["retry_after"] = (datetime.utcnow() + timedelta(seconds=self.retry_delay_seconds)).isoformat()
            message["status"] = MessageQueueStatus.PROCESSING.value

            retry_queue_name = f"{self.RETRY_PREFIX}:queue"
            await redis_queue.push(retry_queue_name, message)

            logger.debug(f"消息已加入重试队列: {message['id']}, 重试次数: {message['retry_count']}")

        except Exception as e:
            logger.error(f"添加到重试队列失败: {e}")

    async def _update_message_status(self, message_id: str, status: MessageQueueStatus):
        """更新消息状态

        Args:
            message_id: 消息ID
            status: 新状态
        """
        try:
            status_key = f"{self.STATUS_PREFIX}:{message_id}"
            status_data = {
                "status": status.value,
                "updated_at": datetime.utcnow().isoformat()
            }

            # 设置状态（24小时过期）
            await redis_cache.set_json(status_key, status_data, expire=86400)

        except Exception as e:
            logger.error(f"更新消息状态失败: {e}")

    async def _cleanup_expired_messages(self):
        """清理过期消息后台任务"""
        logger.info("消息清理任务已启动")

        while True:
            try:
                # 每小时执行一次清理
                await asyncio.sleep(3600)

                # 清理过期的离线消息
                await self._cleanup_expired_offline_messages()

                # 清理过期的消息状态
                await self._cleanup_expired_message_status()

            except Exception as e:
                logger.error(f"消息清理任务错误: {e}")
                await asyncio.sleep(300)  # 出错时5分钟后重试

    async def _cleanup_expired_offline_messages(self):
        """清理过期的离线消息"""
        try:
            if not redis_manager.client:
                return

            # 获取所有离线消息键
            pattern = f"{self.OFFLINE_PREFIX}:*"
            keys = await redis_manager.client.keys(pattern)

            cleaned_count = 0

            for key in keys:
                messages = await redis_cache.get_json(key) or []
                current_time = datetime.utcnow()

                # 过滤未过期的消息
                valid_messages = []
                for msg in messages:
                    try:
                        expires_at = datetime.fromisoformat(msg.get("expires_at", ""))
                        if expires_at > current_time:
                            valid_messages.append(msg)
                        else:
                            cleaned_count += 1
                    except (ValueError, KeyError):
                        cleaned_count += 1

                # 更新Redis中的消息
                if len(valid_messages) != len(messages):
                    if valid_messages:
                        expire_seconds = self.message_expire_days * 24 * 3600
                        await redis_cache.set_json(key, valid_messages, expire_seconds)
                    else:
                        await redis_cache.delete(key)

            if cleaned_count > 0:
                logger.info(f"清理过期离线消息: {cleaned_count} 条")

        except Exception as e:
            logger.error(f"清理过期离线消息失败: {e}")

    async def _cleanup_expired_message_status(self):
        """清理过期的消息状态"""
        try:
            if not redis_manager.client:
                return

            # Redis会自动清理过期的键，这里只是记录日志
            logger.debug("消息状态清理检查完成")

        except Exception as e:
            logger.error(f"清理消息状态失败: {e}")


# 全局Redis消息队列服务实例
redis_message_queue_service = RedisMessageQueueService()
