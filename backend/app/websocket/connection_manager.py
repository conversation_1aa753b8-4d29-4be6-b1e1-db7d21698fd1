"""WebSocket连接管理器

管理WebSocket连接、用户映射、心跳检测和消息广播
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from collections import defaultdict

from fastapi import WebSocket, WebSocketDisconnect
import logging

from app.schemas.websocket import (
    WebSocketMessage, MessageType, OnlineStatus, ConnectionInfo,
    ConnectionStats, create_websocket_message
)

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接：connection_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}

        # 用户连接映射：user_id -> Set[connection_id]
        self.user_connections: Dict[int, Set[str]] = defaultdict(set)

        # 连接信息：connection_id -> ConnectionInfo
        self.connection_info: Dict[str, ConnectionInfo] = {}

        # 聊天室成员：chat_id -> Set[user_id]
        self.chat_members: Dict[int, Set[int]] = defaultdict(set)

        # 用户状态：user_id -> OnlineStatus
        self.user_status: Dict[int, OnlineStatus] = {}

        # 心跳检测
        self.heartbeat_interval = 30  # 30秒
        self.heartbeat_timeout = 60   # 60秒超时

        # 启动时间
        self.start_time = time.time()

        # 心跳检测任务（延迟启动）
        self._heartbeat_task: Optional[asyncio.Task] = None
    
    async def connect(
        self,
        websocket: WebSocket,
        user_id: int,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """建立WebSocket连接"""
        try:
            await websocket.accept()

            # 启动心跳检测任务（如果还没启动）
            if self._heartbeat_task is None or self._heartbeat_task.done():
                self._heartbeat_task = asyncio.create_task(self._heartbeat_checker())

            # 生成连接ID
            connection_id = str(uuid.uuid4())

            # 存储连接
            self.active_connections[connection_id] = websocket
            self.user_connections[user_id].add(connection_id)

            # 存储连接信息
            self.connection_info[connection_id] = ConnectionInfo(
                user_id=user_id,
                connection_id=connection_id,
                connected_at=datetime.utcnow(),
                last_heartbeat=datetime.utcnow(),
                user_agent=user_agent,
                ip_address=ip_address
            )

            # 更新用户状态为在线
            self.user_status[user_id] = OnlineStatus.ONLINE

            logger.info(f"User {user_id} connected with connection {connection_id}")

            # 通知状态同步服务用户连接
            from app.websocket.status_sync import status_sync_service
            await status_sync_service.user_connected(user_id)

            # 通知消息状态同步服务用户连接（自动标记消息为已送达）
            from app.websocket.message_status_sync import message_status_sync_service
            await message_status_sync_service.mark_messages_as_delivered(user_id)

            # 通知Redis消息队列服务用户连接（同步离线消息）
            from app.websocket.redis_message_queue import redis_message_queue_service
            await redis_message_queue_service.sync_offline_messages_on_connect(user_id)

            # 启动后台任务（如果还没启动）
            await status_sync_service.start_background_tasks()
            await redis_message_queue_service.start_background_tasks()

            return connection_id

        except Exception as e:
            logger.error(f"Connection failed for user {user_id}: {e}")
            raise
    
    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        try:
            if connection_id not in self.active_connections:
                return
            
            # 获取连接信息
            conn_info = self.connection_info.get(connection_id)
            if not conn_info:
                return
            
            user_id = conn_info.user_id
            
            # 移除连接
            del self.active_connections[connection_id]
            self.user_connections[user_id].discard(connection_id)
            del self.connection_info[connection_id]
            
            # 如果用户没有其他连接，更新状态为离线
            if not self.user_connections[user_id]:
                self.user_status[user_id] = OnlineStatus.OFFLINE
                del self.user_connections[user_id]

                # 通知状态同步服务用户断开
                from app.websocket.status_sync import status_sync_service
                await status_sync_service.user_disconnected(user_id)
            
            logger.info(f"User {user_id} disconnected from connection {connection_id}")
            
        except Exception as e:
            logger.error(f"Disconnect failed for connection {connection_id}: {e}")
    
    async def send_personal_message(
        self, 
        user_id: int, 
        message: WebSocketMessage
    ) -> bool:
        """发送个人消息"""
        try:
            connections = self.user_connections.get(user_id, set())
            if not connections:
                logger.warning(f"User {user_id} has no active connections")
                return False
            
            message_data = message.model_dump_json()
            success_count = 0
            
            # 发送到用户的所有连接
            for connection_id in connections.copy():
                websocket = self.active_connections.get(connection_id)
                if websocket:
                    try:
                        await websocket.send_text(message_data)
                        success_count += 1
                    except Exception as e:
                        logger.error(f"Failed to send message to connection {connection_id}: {e}")
                        # 移除失效连接
                        await self.disconnect(connection_id)
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Failed to send personal message to user {user_id}: {e}")
            return False
    
    async def send_to_chat(
        self, 
        chat_id: int, 
        message: WebSocketMessage,
        exclude_user_id: Optional[int] = None
    ) -> int:
        """发送消息到聊天室"""
        try:
            chat_members = self.chat_members.get(chat_id, set())
            if not chat_members:
                logger.warning(f"Chat {chat_id} has no members")
                return 0
            
            success_count = 0
            
            for user_id in chat_members:
                if exclude_user_id and user_id == exclude_user_id:
                    continue
                
                if await self.send_personal_message(user_id, message):
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"Failed to send message to chat {chat_id}: {e}")
            return 0
    
    async def broadcast_to_all(
        self, 
        message: WebSocketMessage,
        exclude_user_ids: Optional[Set[int]] = None
    ) -> int:
        """广播消息给所有在线用户"""
        try:
            exclude_user_ids = exclude_user_ids or set()
            success_count = 0
            
            for user_id in list(self.user_connections.keys()):
                if user_id not in exclude_user_ids:
                    if await self.send_personal_message(user_id, message):
                        success_count += 1
            
            return success_count
            
        except Exception as e:
            logger.error(f"Failed to broadcast message: {e}")
            return 0
    
    async def update_heartbeat(self, connection_id: str) -> bool:
        """更新连接心跳"""
        try:
            if connection_id in self.connection_info:
                self.connection_info[connection_id].last_heartbeat = datetime.utcnow()
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to update heartbeat for {connection_id}: {e}")
            return False
    
    async def add_user_to_chat(self, user_id: int, chat_id: int):
        """添加用户到聊天室"""
        self.chat_members[chat_id].add(user_id)
        logger.info(f"Added user {user_id} to chat {chat_id}")
    
    async def remove_user_from_chat(self, user_id: int, chat_id: int):
        """从聊天室移除用户"""
        self.chat_members[chat_id].discard(user_id)
        logger.info(f"Removed user {user_id} from chat {chat_id}")
    
    async def update_user_status(self, user_id: int, status: OnlineStatus):
        """更新用户状态"""
        old_status = self.user_status.get(user_id)
        self.user_status[user_id] = status
        
        if old_status != status:
            await self._broadcast_user_status(user_id, status)
            logger.info(f"User {user_id} status changed from {old_status} to {status}")
    
    def get_connection_stats(self) -> ConnectionStats:
        """获取连接统计"""
        status_counts = defaultdict(int)
        for status in self.user_status.values():
            status_counts[status.value] += 1
        
        return ConnectionStats(
            total_connections=len(self.active_connections),
            active_users=len(self.user_connections),
            connections_by_status=dict(status_counts),
            uptime_seconds=time.time() - self.start_time
        )
    
    def get_user_connections(self, user_id: int) -> List[str]:
        """获取用户的所有连接ID"""
        return list(self.user_connections.get(user_id, set()))
    
    def is_user_online(self, user_id: int) -> bool:
        """检查用户是否在线"""
        return user_id in self.user_connections and len(self.user_connections[user_id]) > 0
    
    def get_online_users(self) -> List[int]:
        """获取所有在线用户ID"""
        return list(self.user_connections.keys())
    
    def get_chat_online_members(self, chat_id: int) -> List[int]:
        """获取聊天室在线成员"""
        chat_members = self.chat_members.get(chat_id, set())
        return [user_id for user_id in chat_members if self.is_user_online(user_id)]
    
    async def _broadcast_user_status(self, user_id: int, status: OnlineStatus):
        """广播用户状态变化"""
        try:
            message = create_websocket_message(
                MessageType.USER_STATUS,
                {
                    "user_id": user_id,
                    "status": status.value,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # 广播给所有在线用户（除了状态变化的用户）
            await self.broadcast_to_all(message, exclude_user_ids={user_id})
            
        except Exception as e:
            logger.error(f"Failed to broadcast user status: {e}")
    
    async def _heartbeat_checker(self):
        """心跳检测任务"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                current_time = datetime.utcnow()
                timeout_threshold = current_time - timedelta(seconds=self.heartbeat_timeout)
                
                # 检查超时连接
                timeout_connections = []
                for connection_id, conn_info in self.connection_info.items():
                    if conn_info.last_heartbeat < timeout_threshold:
                        timeout_connections.append(connection_id)
                
                # 断开超时连接
                for connection_id in timeout_connections:
                    logger.warning(f"Connection {connection_id} timed out")
                    await self.disconnect(connection_id)
                
            except Exception as e:
                logger.error(f"Heartbeat checker error: {e}")


# 全局连接管理器实例
connection_manager = ConnectionManager()


# 导出
__all__ = [
    'ConnectionManager',
    'connection_manager'
]
