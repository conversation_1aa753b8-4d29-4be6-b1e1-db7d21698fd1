"""消息状态同步服务

实现消息已送达、已读状态的实时同步和批量状态更新功能。
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Any
from sqlalchemy import select, and_, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.models.message import Message
from app.models.message_status import MessageStatus
from app.models.chat_member import ChatMember
from app.schemas.websocket import create_websocket_message, MessageType
from app.schemas.message_status import MessageStatusType
from app.websocket.connection_manager import connection_manager

logger = logging.getLogger(__name__)


class MessageStatusSyncService:
    """消息状态同步服务"""
    
    def __init__(self):
        self._pending_deliveries: Dict[int, Set[int]] = {}  # user_id -> set of message_ids
        self._lock = asyncio.Lock()
    
    async def mark_messages_as_delivered(
        self,
        user_id: int,
        message_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """标记消息为已送达
        
        Args:
            user_id: 用户ID
            message_ids: 消息ID列表，如果为None则标记所有未送达消息
            
        Returns:
            操作结果
        """
        try:
            async with self._lock:
                async for db in get_db_session():
                    if message_ids:
                        # 标记指定消息
                        query = select(MessageStatus).where(
                            and_(
                                MessageStatus.user_id == user_id,
                                MessageStatus.message_id.in_(message_ids),
                                MessageStatus.status == "sent"
                            )
                        )
                    else:
                        # 标记所有未送达消息
                        query = select(MessageStatus).where(
                            and_(
                                MessageStatus.user_id == user_id,
                                MessageStatus.status == "sent"
                            )
                        )
                    
                    result = await db.execute(query)
                    statuses = result.scalars().all()
                    
                    updated_messages = []
                    for status in statuses:
                        status.mark_as_delivered()
                        updated_messages.append({
                            "message_id": status.message_id,
                            "user_id": user_id,
                            "old_status": "sent",
                            "new_status": "delivered",
                            "timestamp": status.timestamp.isoformat()
                        })
                    
                    await db.commit()
                    
                    # 广播状态变化
                    for msg_info in updated_messages:
                        await self._broadcast_status_change(
                            msg_info["message_id"],
                            user_id,
                            "delivered",
                            msg_info["timestamp"]
                        )
                    
                    logger.info(f"用户 {user_id} 标记了 {len(updated_messages)} 条消息为已送达")
                    
                    return {
                        "user_id": user_id,
                        "updated_count": len(updated_messages),
                        "updated_messages": updated_messages,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    
        except Exception as e:
            logger.error(f"标记消息为已送达失败: {e}")
            raise
    
    async def mark_message_as_read(
        self,
        user_id: int,
        message_id: int,
        chat_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """标记单条消息为已读
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            chat_id: 聊天ID（可选，用于验证）
            
        Returns:
            操作结果
        """
        try:
            async for db in get_db_session():
                # 查询消息状态
                query = select(MessageStatus).where(
                    and_(
                        MessageStatus.user_id == user_id,
                        MessageStatus.message_id == message_id
                    )
                )
                
                result = await db.execute(query)
                message_status = result.scalar_one_or_none()
                
                if not message_status:
                    # 创建新的已读状态
                    message_status = MessageStatus.create_for_message(
                        message_id, user_id, "read"
                    )
                    db.add(message_status)
                    old_status = None
                else:
                    old_status = message_status.status
                    message_status.mark_as_read()
                
                await db.commit()
                
                # 广播状态变化
                await self._broadcast_status_change(
                    message_id,
                    user_id,
                    "read",
                    message_status.timestamp.isoformat()
                )
                
                logger.info(f"用户 {user_id} 标记消息 {message_id} 为已读")
                
                return {
                    "message_id": message_id,
                    "user_id": user_id,
                    "old_status": old_status,
                    "new_status": "read",
                    "timestamp": message_status.timestamp.isoformat()
                }
                
        except Exception as e:
            logger.error(f"标记消息为已读失败: {e}")
            raise
    
    async def bulk_mark_as_read(
        self,
        user_id: int,
        message_ids: List[int],
        chat_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """批量标记消息为已读
        
        Args:
            user_id: 用户ID
            message_ids: 消息ID列表
            chat_id: 聊天ID（可选，用于验证）
            
        Returns:
            操作结果
        """
        try:
            async with self._lock:
                async for db in get_db_session():
                    # 查询现有状态
                    query = select(MessageStatus).where(
                        and_(
                            MessageStatus.user_id == user_id,
                            MessageStatus.message_id.in_(message_ids)
                        )
                    )
                    
                    result = await db.execute(query)
                    existing_statuses = {status.message_id: status for status in result.scalars().all()}
                    
                    updated_messages = []
                    new_statuses = []
                    
                    for message_id in message_ids:
                        if message_id in existing_statuses:
                            # 更新现有状态
                            status = existing_statuses[message_id]
                            old_status = status.status
                            status.mark_as_read()
                            updated_messages.append({
                                "message_id": message_id,
                                "old_status": old_status,
                                "new_status": "read",
                                "timestamp": status.timestamp.isoformat()
                            })
                        else:
                            # 创建新状态
                            new_status = MessageStatus.create_for_message(
                                message_id, user_id, "read"
                            )
                            new_statuses.append(new_status)
                            updated_messages.append({
                                "message_id": message_id,
                                "old_status": None,
                                "new_status": "read",
                                "timestamp": new_status.timestamp.isoformat()
                            })
                    
                    # 添加新状态
                    for new_status in new_statuses:
                        db.add(new_status)
                    
                    await db.commit()
                    
                    # 广播状态变化
                    for msg_info in updated_messages:
                        await self._broadcast_status_change(
                            msg_info["message_id"],
                            user_id,
                            "read",
                            msg_info["timestamp"]
                        )
                    
                    logger.info(f"用户 {user_id} 批量标记了 {len(updated_messages)} 条消息为已读")
                    
                    return {
                        "user_id": user_id,
                        "updated_count": len(updated_messages),
                        "updated_messages": updated_messages,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    
        except Exception as e:
            logger.error(f"批量标记消息为已读失败: {e}")
            raise
    
    async def get_message_status_summary(
        self,
        message_id: int,
        sender_id: int
    ) -> Dict[str, Any]:
        """获取消息状态摘要
        
        Args:
            message_id: 消息ID
            sender_id: 发送者ID（用于权限验证）
            
        Returns:
            消息状态摘要
        """
        try:
            async for db in get_db_session():
                # 验证消息发送者
                message_query = select(Message).where(
                    and_(
                        Message.id == message_id,
                        Message.sender_id == sender_id
                    )
                )
                
                message_result = await db.execute(message_query)
                message = message_result.scalar_one_or_none()
                
                if not message:
                    raise ValueError("消息不存在或无权限查看")
                
                # 查询所有状态
                status_query = select(MessageStatus).where(
                    MessageStatus.message_id == message_id
                )
                
                status_result = await db.execute(status_query)
                statuses = status_result.scalars().all()
                
                # 统计状态
                status_counts = {"sent": 0, "delivered": 0, "read": 0}
                status_details = []
                
                for status in statuses:
                    status_counts[status.status] += 1
                    status_details.append({
                        "user_id": status.user_id,
                        "status": status.status,
                        "timestamp": status.timestamp.isoformat()
                    })
                
                return {
                    "message_id": message_id,
                    "total_recipients": len(statuses),
                    "status_counts": status_counts,
                    "status_details": status_details,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取消息状态摘要失败: {e}")
            raise
    
    async def _broadcast_status_change(
        self,
        message_id: int,
        user_id: int,
        new_status: str,
        timestamp: str
    ):
        """广播消息状态变化
        
        Args:
            message_id: 消息ID
            user_id: 状态变化的用户ID
            new_status: 新状态
            timestamp: 时间戳
        """
        try:
            # 查询消息发送者
            async for db in get_db_session():
                message_query = select(Message.sender_id).where(Message.id == message_id)
                result = await db.execute(message_query)
                sender_id = result.scalar_one_or_none()
                
                if not sender_id or sender_id == user_id:
                    # 消息不存在或者是发送者自己，不需要广播
                    return
                
                # 创建状态更新消息
                status_message = create_websocket_message(
                    MessageType.MESSAGE_STATUS,
                    {
                        "message_id": message_id,
                        "user_id": user_id,
                        "status": new_status,
                        "timestamp": timestamp
                    }
                )
                
                # 发送给消息发送者
                success = await connection_manager.send_personal_message(
                    sender_id, status_message.model_dump()
                )
                
                if success:
                    logger.debug(f"消息状态更新广播成功: 消息 {message_id}, 用户 {user_id} -> {new_status}")
                else:
                    logger.debug(f"消息状态更新广播失败: 发送者 {sender_id} 不在线")
                
        except Exception as e:
            logger.error(f"广播消息状态变化失败: {e}")


# 全局消息状态同步服务实例
message_status_sync_service = MessageStatusSyncService()
