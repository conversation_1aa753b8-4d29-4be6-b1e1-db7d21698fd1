"""数据验证配置

定义各种数据验证规则和限制
"""

from typing import Dict, List, Set
from enum import Enum


class ValidationLimits:
    """验证限制常量"""
    
    # 用户相关限制
    USERNAME_MIN_LENGTH = 3
    USERNAME_MAX_LENGTH = 50
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    NICKNAME_MAX_LENGTH = 50
    SIGNATURE_MAX_LENGTH = 500
    
    # 聊天相关限制
    CHAT_NAME_MAX_LENGTH = 100
    CHAT_DESCRIPTION_MAX_LENGTH = 500
    MESSAGE_MAX_LENGTH = 2000
    
    # 文件相关限制
    FILENAME_MAX_LENGTH = 255
    FILE_DESCRIPTION_MAX_LENGTH = 500
    
    # 文件大小限制（字节）
    AVATAR_MAX_SIZE = 2 * 1024 * 1024  # 2MB
    IMAGE_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    DOCUMENT_MAX_SIZE = 50 * 1024 * 1024  # 50MB
    VIDEO_MAX_SIZE = 100 * 1024 * 1024  # 100MB
    AUDIO_MAX_SIZE = 20 * 1024 * 1024  # 20MB
    DEFAULT_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    
    # 分页限制
    MAX_PAGE_SIZE = 100
    DEFAULT_PAGE_SIZE = 20
    
    # 搜索限制
    SEARCH_QUERY_MIN_LENGTH = 1
    SEARCH_QUERY_MAX_LENGTH = 100
    MAX_SEARCH_RESULTS = 1000
    
    # 批量操作限制
    MAX_BATCH_SIZE = 100
    MAX_FRIEND_REQUESTS_PER_DAY = 50
    MAX_MESSAGES_PER_MINUTE = 60


class FileTypeConfig:
    """文件类型配置"""
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {
        'image': ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
        'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        'audio': ['mp3', 'wav', 'aac', 'flac', 'ogg'],
        'video': ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
        'avatar': ['jpg', 'jpeg', 'png', 'webp']
    }
    
    # MIME类型映射
    MIME_TYPE_MAPPING = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'bmp': 'image/bmp',
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'txt': 'text/plain',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'aac': 'audio/aac',
        'flac': 'audio/flac',
        'ogg': 'audio/ogg',
        'mp4': 'video/mp4',
        'avi': 'video/x-msvideo',
        'mov': 'video/quicktime',
        'wmv': 'video/x-ms-wmv',
        'flv': 'video/x-flv',
        'webm': 'video/webm'
    }
    
    # 文件大小限制映射
    SIZE_LIMITS = {
        'image': ValidationLimits.IMAGE_MAX_SIZE,
        'document': ValidationLimits.DOCUMENT_MAX_SIZE,
        'audio': ValidationLimits.AUDIO_MAX_SIZE,
        'video': ValidationLimits.VIDEO_MAX_SIZE,
        'avatar': ValidationLimits.AVATAR_MAX_SIZE,
        'other': ValidationLimits.DEFAULT_MAX_SIZE
    }


class SecurityConfig:
    """安全配置"""
    
    # 禁用的用户名
    FORBIDDEN_USERNAMES = {
        'admin', 'root', 'system', 'administrator', 'moderator',
        'null', 'undefined', 'anonymous', 'guest', 'test',
        'api', 'www', 'mail', 'ftp', 'support', 'service'
    }
    
    # 敏感词列表
    SENSITIVE_WORDS = {
        '管理员', '系统', '客服', '官方',
        'fuck', 'shit', 'damn', 'hell'
    }
    
    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = {
        'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js',
        'jar', 'app', 'deb', 'pkg', 'dmg', 'iso'
    }
    
    # XSS防护字符
    XSS_DANGEROUS_CHARS = {
        '<script', '</script>', 'javascript:', 'onload=', 'onerror=',
        'onclick=', 'onmouseover=', 'onfocus=', 'onblur='
    }


class ValidationMessages:
    """验证错误消息"""
    
    # 用户相关消息
    USERNAME_REQUIRED = "用户名不能为空"
    USERNAME_TOO_SHORT = f"用户名长度至少{ValidationLimits.USERNAME_MIN_LENGTH}位"
    USERNAME_TOO_LONG = f"用户名长度不能超过{ValidationLimits.USERNAME_MAX_LENGTH}位"
    USERNAME_INVALID_FORMAT = "用户名只能包含字母、数字和下划线"
    USERNAME_FORBIDDEN = "用户名包含禁用词汇"
    
    PASSWORD_REQUIRED = "密码不能为空"
    PASSWORD_TOO_SHORT = f"密码长度至少{ValidationLimits.PASSWORD_MIN_LENGTH}位"
    PASSWORD_TOO_LONG = f"密码长度不能超过{ValidationLimits.PASSWORD_MAX_LENGTH}位"
    PASSWORD_TOO_WEAK = "密码强度不足，需要包含字母、数字或特殊字符"
    
    EMAIL_REQUIRED = "邮箱不能为空"
    EMAIL_INVALID_FORMAT = "邮箱格式不正确"
    
    # 文件相关消息
    FILE_REQUIRED = "文件不能为空"
    FILE_TOO_LARGE = "文件大小超出限制"
    FILE_TYPE_NOT_ALLOWED = "文件类型不被允许"
    FILE_EXTENSION_DANGEROUS = "文件扩展名存在安全风险"
    
    # 通用消息
    FIELD_REQUIRED = "字段不能为空"
    FIELD_TOO_LONG = "字段长度超出限制"
    INVALID_ID = "无效的ID"
    INVALID_DATE_RANGE = "日期范围无效"
    PERMISSION_DENIED = "权限不足"
    RATE_LIMIT_EXCEEDED = "请求过于频繁"


class ValidationRules:
    """验证规则配置"""
    
    @staticmethod
    def get_file_size_limit(file_type: str) -> int:
        """获取文件大小限制"""
        return FileTypeConfig.SIZE_LIMITS.get(file_type, ValidationLimits.DEFAULT_MAX_SIZE)
    
    @staticmethod
    def get_allowed_extensions(file_type: str) -> List[str]:
        """获取允许的文件扩展名"""
        return FileTypeConfig.ALLOWED_EXTENSIONS.get(file_type, [])
    
    @staticmethod
    def is_extension_allowed(filename: str, file_type: str) -> bool:
        """检查文件扩展名是否允许"""
        if not filename or '.' not in filename:
            return False
        
        ext = filename.lower().split('.')[-1]
        allowed_extensions = ValidationRules.get_allowed_extensions(file_type)
        
        return ext in allowed_extensions
    
    @staticmethod
    def is_extension_dangerous(filename: str) -> bool:
        """检查文件扩展名是否危险"""
        if not filename or '.' not in filename:
            return False
        
        ext = filename.lower().split('.')[-1]
        return ext in SecurityConfig.DANGEROUS_EXTENSIONS
    
    @staticmethod
    def contains_sensitive_words(text: str) -> bool:
        """检查文本是否包含敏感词"""
        if not text:
            return False
        
        text_lower = text.lower()
        return any(word in text_lower for word in SecurityConfig.SENSITIVE_WORDS)
    
    @staticmethod
    def contains_xss_patterns(text: str) -> bool:
        """检查文本是否包含XSS模式"""
        if not text:
            return False
        
        text_lower = text.lower()
        return any(pattern in text_lower for pattern in SecurityConfig.XSS_DANGEROUS_CHARS)
    
    @staticmethod
    def is_username_forbidden(username: str) -> bool:
        """检查用户名是否被禁用"""
        if not username:
            return False
        
        return username.lower() in SecurityConfig.FORBIDDEN_USERNAMES


# 导出配置类
__all__ = [
    'ValidationLimits',
    'FileTypeConfig', 
    'SecurityConfig',
    'ValidationMessages',
    'ValidationRules'
]
