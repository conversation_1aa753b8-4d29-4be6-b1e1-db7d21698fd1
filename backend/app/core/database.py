"""数据库连接和配置模块

使用异步SQLAlchemy和连接池优化，支持PostgreSQL数据库操作。
实现了高性能的数据库连接管理和MCP集成。
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

import asyncpg
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
    AsyncEngine
)
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import NullPool
from sqlalchemy import text

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class Base(DeclarativeBase):
    """SQLAlchemy基础模型类"""
    pass


class DatabaseManager:
    """数据库管理器 - 单例模式"""
    
    _instance: Optional['DatabaseManager'] = None
    _engine: Optional[AsyncEngine] = None
    _session_factory: Optional[async_sessionmaker[AsyncSession]] = None
    
    def __new__(cls) -> 'DatabaseManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
    
    async def initialize(self) -> None:
        """初始化数据库连接"""
        if self._engine is None:
            logger.info("初始化数据库连接...")
            
            # 根据数据库类型设置连接参数
            if settings.postgres_url.startswith("sqlite"):
                # SQLite连接参数
                connect_args = {"check_same_thread": False}
                engine_kwargs = {
                    "poolclass": NullPool,
                    "echo": settings.debug,
                    "future": True,
                    "connect_args": connect_args
                }
            else:
                # PostgreSQL连接参数
                connect_args = {
                    "statement_cache_size": 0,
                    "prepared_statement_cache_size": 0,
                    "server_settings": {
                        "jit": "off",
                        "application_name": "chat_app_backend"
                    }
                }
                engine_kwargs = {
                    "poolclass": NullPool,
                    "echo": settings.debug,
                    "future": True,
                    "connect_args": connect_args,
                    "pool_pre_ping": True,
                    "pool_recycle": 3600
                }

            self._engine = create_async_engine(
                settings.postgres_url,
                **engine_kwargs
            )
            
            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            logger.info("数据库连接初始化完成")
    
    async def close(self) -> None:
        """关闭数据库连接"""
        if self._engine:
            logger.info("关闭数据库连接...")
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
            logger.info("数据库连接已关闭")
    
    @property
    def engine(self) -> AsyncEngine:
        """获取数据库引擎"""
        if self._engine is None:
            raise RuntimeError("数据库未初始化，请先调用 initialize() 方法")
        return self._engine
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话（上下文管理器）"""
        if self._session_factory is None:
            raise RuntimeError("数据库未初始化，请先调用 initialize() 方法")
        
        async with self._session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"数据库操作失败: {e}")
                raise
            finally:
                await session.close()
    
    async def get_raw_connection(self):
        """获取原始asyncpg连接（用于高性能操作）"""
        return await asyncpg.connect(
            host=settings.postgres_host,
            port=settings.postgres_port,
            user=settings.postgres_user,
            password=settings.postgres_password,
            database=settings.postgres_db
        )


class MCPDatabaseConnector:
    """MCP数据库连接器
    
    提供与MCP PostgREST服务的集成接口
    """
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_postgrest_config(self) -> dict:
        """获取PostgREST配置信息"""
        return {
            "database_url": settings.postgres_url,
            "connection_info": {
                "host": settings.postgres_host,
                "port": settings.postgres_port,
                "database": settings.postgres_db,
                "user": settings.postgres_user
            }
        }
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            async with self.db_manager.get_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    async def execute_sql(self, sql: str, params: dict = None) -> list:
        """执行SQL查询（用于MCP调用）"""
        try:
            conn = await self.db_manager.get_raw_connection()
            try:
                if params:
                    result = await conn.fetch(sql, *params.values())
                else:
                    result = await conn.fetch(sql)
                return [dict(row) for row in result]
            finally:
                await conn.close()
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            raise


# 全局数据库管理器实例
db_manager = DatabaseManager()
mcp_connector = MCPDatabaseConnector(db_manager)


# 便捷函数
async def init_database() -> None:
    """初始化数据库连接"""
    await db_manager.initialize()


async def close_database() -> None:
    """关闭数据库连接"""
    await db_manager.close()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（依赖注入用）"""
    async with db_manager.get_session() as session:
        yield session


def get_mcp_config() -> dict:
    """获取MCP配置（用于MCP服务调用）"""
    return mcp_connector.get_postgrest_config()


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_database():
        """测试数据库连接"""
        await init_database()
        
        # 测试连接
        is_connected = await mcp_connector.test_connection()
        print(f"数据库连接状态: {'成功' if is_connected else '失败'}")
        
        # 显示配置
        config = get_mcp_config()
        print(f"MCP配置: {config}")
        
        await close_database()
    
    asyncio.run(test_database())