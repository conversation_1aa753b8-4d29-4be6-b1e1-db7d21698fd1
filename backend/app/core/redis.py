"""Redis连接和配置模块

提供Redis连接池管理、缓存操作和消息队列功能。
支持高性能的异步Redis操作和连接池优化。
"""

import json
import logging
from typing import Any, Optional, Union, Dict, List
from contextlib import asynccontextmanager

import redis.asyncio as redis
from redis.asyncio import ConnectionPool, Redis
from redis.exceptions import RedisError, ConnectionError

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class RedisManager:
    """Redis管理器 - 单例模式"""
    
    _instance: Optional['RedisManager'] = None
    _redis_client: Optional[Redis] = None
    _connection_pool: Optional[ConnectionPool] = None
    
    def __new__(cls) -> 'RedisManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
    
    async def initialize(self) -> None:
        """初始化Redis连接"""
        if self._redis_client is None:
            logger.info("初始化Redis连接...")
            
            try:
                # 创建连接池
                self._connection_pool = ConnectionPool(
                    host=settings.redis_host,
                    port=settings.redis_port,
                    password=settings.redis_password,
                    db=settings.redis_db,
                    max_connections=settings.redis_max_connections,
                    retry_on_timeout=True,
                    socket_timeout=settings.redis_socket_timeout,
                    socket_connect_timeout=settings.redis_connect_timeout,
                    decode_responses=True,  # 自动解码响应
                    encoding='utf-8'
                )
                
                # 创建Redis客户端
                self._redis_client = Redis(
                    connection_pool=self._connection_pool,
                    socket_keepalive=True,
                    socket_keepalive_options={}
                )
                
                # 测试连接
                await self._redis_client.ping()
                logger.info("Redis连接初始化完成")
                
            except Exception as e:
                logger.warning(f"Redis连接初始化失败: {e}")
                logger.info("Redis服务不可用，将在需要时重试连接")
                # 不抛出异常，允许应用在没有Redis的情况下运行
    
    async def close(self) -> None:
        """关闭Redis连接"""
        if self._redis_client:
            logger.info("关闭Redis连接...")
            await self._redis_client.close()
            self._redis_client = None
            self._connection_pool = None
            logger.info("Redis连接已关闭")
    
    @property
    def client(self) -> Optional[Redis]:
        """获取Redis客户端"""
        return self._redis_client
    
    async def ping(self) -> bool:
        """测试Redis连接"""
        try:
            if self.client is None:
                return False
            result = await self.client.ping()
            return result is True
        except Exception as e:
            logger.error(f"Redis连接测试失败: {e}")
            return False


class RedisCache:
    """Redis缓存操作类"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
    
    @property
    def client(self) -> Optional[Redis]:
        return self.redis_manager.client
    
    async def get(self, key: str) -> Optional[str]:
        """获取缓存值"""
        try:
            if self.client is None:
                logger.warning("Redis不可用，跳过缓存获取操作")
                return None
            return await self.client.get(key)
        except RedisError as e:
            logger.error(f"Redis GET操作失败 {key}: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Union[str, int, float, dict, list],
        expire: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        try:
            if self.client is None:
                logger.warning("Redis不可用，跳过缓存设置操作")
                return False

            # 如果是复杂类型，序列化为JSON
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)

            result = await self.client.set(key, value, ex=expire)
            return result is True
        except RedisError as e:
            logger.error(f"Redis SET操作失败 {key}: {e}")
            return False
    
    async def delete(self, *keys: str) -> int:
        """删除缓存键"""
        try:
            return await self.client.delete(*keys)
        except RedisError as e:
            logger.error(f"Redis DELETE操作失败 {keys}: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            result = await self.client.exists(key)
            return result > 0
        except RedisError as e:
            logger.error(f"Redis EXISTS操作失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置键过期时间"""
        try:
            result = await self.client.expire(key, seconds)
            return result is True
        except RedisError as e:
            logger.error(f"Redis EXPIRE操作失败 {key}: {e}")
            return False
    
    async def get_json(self, key: str) -> Optional[Union[dict, list]]:
        """获取JSON格式的缓存值"""
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"JSON解析失败 {key}: {e}")
            return None
    
    async def set_json(
        self, 
        key: str, 
        value: Union[dict, list], 
        expire: Optional[int] = None
    ) -> bool:
        """设置JSON格式的缓存值"""
        return await self.set(key, value, expire)


class RedisQueue:
    """Redis消息队列操作类"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
    
    @property
    def client(self) -> Redis:
        return self.redis_manager.client
    
    async def push(self, queue_name: str, message: Union[str, dict]) -> bool:
        """推送消息到队列"""
        try:
            if isinstance(message, dict):
                message = json.dumps(message, ensure_ascii=False)
            
            result = await self.client.lpush(queue_name, message)
            return result > 0
        except RedisError as e:
            logger.error(f"Redis队列推送失败 {queue_name}: {e}")
            return False
    
    async def pop(self, queue_name: str, timeout: int = 0) -> Optional[str]:
        """从队列弹出消息"""
        try:
            if timeout > 0:
                result = await self.client.brpop(queue_name, timeout=timeout)
                return result[1] if result else None
            else:
                return await self.client.rpop(queue_name)
        except RedisError as e:
            logger.error(f"Redis队列弹出失败 {queue_name}: {e}")
            return None
    
    async def pop_json(self, queue_name: str, timeout: int = 0) -> Optional[dict]:
        """从队列弹出JSON消息"""
        try:
            message = await self.pop(queue_name, timeout)
            if message:
                return json.loads(message)
            return None
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"队列消息JSON解析失败 {queue_name}: {e}")
            return None
    
    async def length(self, queue_name: str) -> int:
        """获取队列长度"""
        try:
            return await self.client.llen(queue_name)
        except RedisError as e:
            logger.error(f"Redis队列长度获取失败 {queue_name}: {e}")
            return 0


class RedisPubSub:
    """Redis发布订阅操作类"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
    
    @property
    def client(self) -> Redis:
        return self.redis_manager.client
    
    async def publish(self, channel: str, message: Union[str, dict]) -> int:
        """发布消息到频道"""
        try:
            if isinstance(message, dict):
                message = json.dumps(message, ensure_ascii=False)
            
            return await self.client.publish(channel, message)
        except RedisError as e:
            logger.error(f"Redis发布消息失败 {channel}: {e}")
            return 0
    
    async def subscribe(self, *channels: str):
        """订阅频道"""
        try:
            pubsub = self.client.pubsub()
            await pubsub.subscribe(*channels)
            return pubsub
        except RedisError as e:
            logger.error(f"Redis订阅失败 {channels}: {e}")
            return None


# 全局Redis管理器实例
redis_manager = RedisManager()
redis_cache = RedisCache(redis_manager)
redis_queue = RedisQueue(redis_manager)
redis_pubsub = RedisPubSub(redis_manager)


# 便捷函数
async def init_redis() -> None:
    """初始化Redis连接"""
    await redis_manager.initialize()


async def close_redis() -> None:
    """关闭Redis连接"""
    await redis_manager.close()


async def get_redis_client() -> Redis:
    """获取Redis客户端（依赖注入用）"""
    return redis_manager.client


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_redis():
        """测试Redis连接和操作"""
        await init_redis()
        
        # 测试连接
        is_connected = await redis_manager.ping()
        print(f"Redis连接状态: {'成功' if is_connected else '失败'}")
        
        if is_connected:
            # 测试缓存操作
            await redis_cache.set("test_key", "test_value", expire=60)
            value = await redis_cache.get("test_key")
            print(f"缓存测试: {value}")
            
            # 测试JSON缓存
            test_data = {"name": "测试", "value": 123}
            await redis_cache.set_json("test_json", test_data, expire=60)
            json_value = await redis_cache.get_json("test_json")
            print(f"JSON缓存测试: {json_value}")
            
            # 清理测试数据
            await redis_cache.delete("test_key", "test_json")
        
        await close_redis()
        print("Redis测试完成")
    
    asyncio.run(test_redis())
