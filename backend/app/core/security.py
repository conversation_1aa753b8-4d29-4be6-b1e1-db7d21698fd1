"""安全配置和加固措施

实现安全头部、输入验证、内容安全策略等安全加固功能
"""

import re
import html
import json
import hashlib
import secrets
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import Request, HTTPException
from starlette.responses import Response

logger = logging.getLogger(__name__)


class SecurityConfig:
    """安全配置类"""
    
    # 密码策略
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_DIGITS = True
    PASSWORD_REQUIRE_SPECIAL = True
    
    # 文件上传安全
    ALLOWED_FILE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    BLOCKED_FILE_EXTENSIONS = {'.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar', '.php', '.asp', '.jsp'}
    
    # 输入验证
    MAX_INPUT_LENGTH = 1000
    USERNAME_PATTERN = r'^[a-zA-Z][a-zA-Z0-9_]{2,19}$'  # 必须以字母开头
    EMAIL_PATTERN = r'^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$'
    
    # 安全头部
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
    }
    
    # 限流配置
    RATE_LIMIT_ATTEMPTS = 5
    RATE_LIMIT_WINDOW = 300  # 5分钟
    
    # 会话安全
    SESSION_TIMEOUT = 3600  # 1小时
    CSRF_TOKEN_LENGTH = 32


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_password(password: str) -> Dict[str, Any]:
        """验证密码强度"""
        errors = []
        
        if len(password) < SecurityConfig.PASSWORD_MIN_LENGTH:
            errors.append(f"密码长度至少{SecurityConfig.PASSWORD_MIN_LENGTH}位")
        
        if len(password) > SecurityConfig.PASSWORD_MAX_LENGTH:
            errors.append(f"密码长度不能超过{SecurityConfig.PASSWORD_MAX_LENGTH}位")
        
        if SecurityConfig.PASSWORD_REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        
        if SecurityConfig.PASSWORD_REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        
        if SecurityConfig.PASSWORD_REQUIRE_DIGITS and not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        
        if SecurityConfig.PASSWORD_REQUIRE_SPECIAL and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含特殊字符")
        
        # 检查常见弱密码
        weak_passwords = {
            'password', '123456', 'admin', 'qwerty', 'letmein', 
            'welcome', 'monkey', '1234567890', 'password123'
        }
        
        if password.lower() in weak_passwords:
            errors.append("密码过于简单，请使用更复杂的密码")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "strength": InputValidator._calculate_password_strength(password)
        }
    
    @staticmethod
    def _calculate_password_strength(password: str) -> str:
        """计算密码强度"""
        score = 0
        
        # 长度评分
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1
        
        # 字符类型评分
        if re.search(r'[a-z]', password):
            score += 1
        if re.search(r'[A-Z]', password):
            score += 1
        if re.search(r'\d', password):
            score += 1
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        
        # 复杂性评分
        if len(set(password)) > len(password) * 0.7:  # 字符多样性
            score += 1
        
        if score <= 2:
            return "弱"
        elif score <= 4:
            return "中等"
        elif score <= 6:
            return "强"
        else:
            return "很强"
    
    @staticmethod
    def validate_username(username: str) -> Dict[str, Any]:
        """验证用户名"""
        errors = []
        
        if not re.match(SecurityConfig.USERNAME_PATTERN, username):
            errors.append("用户名只能包含字母、数字和下划线，长度3-20位")
        
        # 检查保留用户名
        reserved_usernames = {
            'admin', 'root', 'administrator', 'system', 'api', 'www', 
            'mail', 'ftp', 'test', 'guest', 'anonymous'
        }
        
        if username.lower() in reserved_usernames:
            errors.append("该用户名为系统保留，请选择其他用户名")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def validate_email(email: str) -> Dict[str, Any]:
        """验证邮箱地址"""
        errors = []

        # 基本格式检查
        if not email or '@' not in email:
            errors.append("邮箱地址格式不正确")
            return {"valid": False, "errors": errors}

        # 检查连续的点
        if '..' in email:
            errors.append("邮箱地址不能包含连续的点")

        # 检查开头和结尾
        if email.startswith('.') or email.endswith('.') or email.startswith('@') or email.endswith('@'):
            errors.append("邮箱地址格式不正确")

        # 使用更宽松的正则表达式
        basic_email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(basic_email_pattern, email):
            errors.append("邮箱地址格式不正确")

        if len(email) > 254:  # RFC 5321 限制
            errors.append("邮箱地址过长")

        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """清理用户输入"""
        if not text:
            return ""

        # HTML转义
        text = html.escape(text)

        # 移除潜在的脚本标签
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)

        # 移除JavaScript协议
        text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)

        # 移除事件处理器
        text = re.sub(r'on\w+\s*=', '', text, flags=re.IGNORECASE)

        # 移除目录遍历字符
        text = re.sub(r'\.\./', '', text)
        text = re.sub(r'\.\.\\', '', text)

        # 限制长度
        if len(text) > SecurityConfig.MAX_INPUT_LENGTH:
            text = text[:SecurityConfig.MAX_INPUT_LENGTH]

        return text.strip()
    
    @staticmethod
    def validate_file_upload(filename: str, content: bytes) -> Dict[str, Any]:
        """验证文件上传"""
        errors = []
        
        # 检查文件名
        if not filename:
            errors.append("文件名不能为空")
            return {"valid": False, "errors": errors}
        
        # 检查文件扩展名
        file_ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
        
        if file_ext in SecurityConfig.BLOCKED_FILE_EXTENSIONS:
            errors.append(f"不允许上传{file_ext}类型的文件")
        
        if file_ext not in SecurityConfig.ALLOWED_FILE_EXTENSIONS:
            errors.append(f"只允许上传以下类型的文件: {', '.join(SecurityConfig.ALLOWED_FILE_EXTENSIONS)}")
        
        # 检查文件大小
        if len(content) > SecurityConfig.MAX_FILE_SIZE:
            errors.append(f"文件大小不能超过{SecurityConfig.MAX_FILE_SIZE // (1024*1024)}MB")
        
        # 检查文件名中的危险字符
        dangerous_chars = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*']
        if any(char in filename for char in dangerous_chars):
            errors.append("文件名包含非法字符")
        
        # 检查文件内容（简单的恶意软件检测）
        if InputValidator._is_potentially_malicious(content):
            errors.append("文件内容可能包含恶意代码")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def _is_potentially_malicious(content: bytes) -> bool:
        """检查文件内容是否可能包含恶意代码"""
        # 检查PE文件头（Windows可执行文件）
        if content.startswith(b'MZ'):
            return True
        
        # 检查ELF文件头（Linux可执行文件）
        if content.startswith(b'\x7fELF'):
            return True
        
        # 检查脚本特征
        script_patterns = [
            b'<?php',
            b'<%',
            b'<script',
            b'javascript:',
            b'eval(',
            b'exec(',
            b'system(',
            b'shell_exec('
        ]
        
        content_lower = content.lower()
        for pattern in script_patterns:
            if pattern in content_lower:
                return True
        
        return False


class SecurityHeadersMiddleware:
    """安全头部中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        """添加安全头部"""
        if scope["type"] == "http":
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    headers = dict(message.get("headers", []))
                    
                    # 添加安全头部
                    for header_name, header_value in SecurityConfig.SECURITY_HEADERS.items():
                        headers[header_name.encode()] = header_value.encode()
                    
                    # 移除可能泄露信息的头部
                    headers.pop(b"server", None)
                    headers.pop(b"x-powered-by", None)
                    
                    message["headers"] = list(headers.items())
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


class CSRFProtection:
    """CSRF防护"""
    
    def __init__(self):
        self._tokens = {}  # 在生产环境中应该使用Redis
    
    def generate_token(self, user_id: str) -> str:
        """生成CSRF令牌"""
        token = secrets.token_urlsafe(SecurityConfig.CSRF_TOKEN_LENGTH)
        self._tokens[user_id] = {
            "token": token,
            "created_at": datetime.utcnow()
        }
        return token
    
    def validate_token(self, user_id: str, token: str) -> bool:
        """验证CSRF令牌"""
        if user_id not in self._tokens:
            return False
        
        stored_token_info = self._tokens[user_id]
        
        # 检查令牌是否匹配
        if stored_token_info["token"] != token:
            return False
        
        # 检查令牌是否过期
        if datetime.utcnow() - stored_token_info["created_at"] > timedelta(hours=1):
            del self._tokens[user_id]
            return False
        
        return True
    
    def cleanup_expired_tokens(self):
        """清理过期的CSRF令牌"""
        current_time = datetime.utcnow()
        expired_users = [
            user_id for user_id, token_info in self._tokens.items()
            if current_time - token_info["created_at"] > timedelta(hours=1)
        ]
        
        for user_id in expired_users:
            del self._tokens[user_id]


class SecurityAuditLogger:
    """安全审计日志"""
    
    @staticmethod
    def log_security_event(event_type: str, user_id: Optional[str], 
                          request: Request, details: Dict[str, Any] = None):
        """记录安全事件"""
        client_ip = SecurityAuditLogger._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "path": request.url.path,
            "method": request.method,
            "details": details or {}
        }
        
        logger.warning(f"SECURITY_EVENT: {json.dumps(log_data)}")
    
    @staticmethod
    def _get_client_ip(request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头部
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 使用客户端地址
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"


# 全局实例
input_validator = InputValidator()
csrf_protection = CSRFProtection()
security_audit_logger = SecurityAuditLogger()
