"""性能优化配置

包含数据库连接池、缓存配置、请求限流等性能优化设置
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable
from functools import wraps
from contextlib import asynccontextmanager
import logging

from sqlalchemy.pool import QueuePool
from sqlalchemy.engine import Engine
from redis import Redis
import aioredis

logger = logging.getLogger(__name__)


class PerformanceConfig:
    """性能配置类"""
    
    # 数据库连接池配置
    DATABASE_POOL_SIZE = 20
    DATABASE_MAX_OVERFLOW = 30
    DATABASE_POOL_TIMEOUT = 30
    DATABASE_POOL_RECYCLE = 3600
    
    # Redis连接池配置
    REDIS_POOL_SIZE = 10
    REDIS_MAX_CONNECTIONS = 50
    
    # API限流配置
    RATE_LIMIT_REQUESTS = 100
    RATE_LIMIT_WINDOW = 60  # 秒
    
    # WebSocket配置
    WEBSOCKET_MAX_CONNECTIONS = 1000
    WEBSOCKET_HEARTBEAT_INTERVAL = 30
    WEBSOCKET_MESSAGE_QUEUE_SIZE = 1000
    
    # 缓存配置
    CACHE_TTL = 300  # 5分钟
    CACHE_MAX_SIZE = 1000
    
    # 性能监控配置
    SLOW_QUERY_THRESHOLD = 1.0  # 秒
    SLOW_REQUEST_THRESHOLD = 2.0  # 秒


class DatabaseOptimizer:
    """数据库性能优化器"""
    
    @staticmethod
    def configure_connection_pool(engine: Engine) -> None:
        """配置数据库连接池"""
        if hasattr(engine.pool, 'size'):
            logger.info(f"数据库连接池配置: size={engine.pool.size()}, overflow={engine.pool.overflow()}")
    
    @staticmethod
    def get_pool_config() -> Dict[str, Any]:
        """获取连接池配置"""
        return {
            "poolclass": QueuePool,
            "pool_size": PerformanceConfig.DATABASE_POOL_SIZE,
            "max_overflow": PerformanceConfig.DATABASE_MAX_OVERFLOW,
            "pool_timeout": PerformanceConfig.DATABASE_POOL_TIMEOUT,
            "pool_recycle": PerformanceConfig.DATABASE_POOL_RECYCLE,
            "pool_pre_ping": True,  # 连接前检查
        }
    
    @staticmethod
    async def monitor_slow_queries(query: str, duration: float) -> None:
        """监控慢查询"""
        if duration > PerformanceConfig.SLOW_QUERY_THRESHOLD:
            logger.warning(f"慢查询检测: {query[:100]}... 耗时: {duration:.3f}s")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._redis_client: Optional[aioredis.Redis] = None
    
    async def initialize_redis(self, redis_url: str) -> None:
        """初始化Redis连接"""
        try:
            self._redis_client = aioredis.from_url(
                redis_url,
                max_connections=PerformanceConfig.REDIS_MAX_CONNECTIONS,
                retry_on_timeout=True
            )
            await self._redis_client.ping()
            logger.info("Redis缓存连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self._redis_client = None
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        # 优先从内存缓存获取
        if key in self._memory_cache:
            cache_item = self._memory_cache[key]
            if time.time() < cache_item["expires_at"]:
                return cache_item["value"]
            else:
                del self._memory_cache[key]
        
        # 从Redis获取
        if self._redis_client:
            try:
                value = await self._redis_client.get(key)
                if value:
                    return value.decode() if isinstance(value, bytes) else value
            except Exception as e:
                logger.error(f"Redis获取缓存失败: {e}")
        
        return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> None:
        """设置缓存值"""
        ttl = ttl or PerformanceConfig.CACHE_TTL
        expires_at = time.time() + ttl
        
        # 设置内存缓存
        if len(self._memory_cache) < PerformanceConfig.CACHE_MAX_SIZE:
            self._memory_cache[key] = {
                "value": value,
                "expires_at": expires_at
            }
        
        # 设置Redis缓存
        if self._redis_client:
            try:
                await self._redis_client.setex(key, ttl, str(value))
            except Exception as e:
                logger.error(f"Redis设置缓存失败: {e}")
    
    async def delete(self, key: str) -> None:
        """删除缓存"""
        # 删除内存缓存
        self._memory_cache.pop(key, None)
        
        # 删除Redis缓存
        if self._redis_client:
            try:
                await self._redis_client.delete(key)
            except Exception as e:
                logger.error(f"Redis删除缓存失败: {e}")
    
    def clear_memory_cache(self) -> None:
        """清理过期的内存缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, item in self._memory_cache.items()
            if current_time >= item["expires_at"]
        ]
        for key in expired_keys:
            del self._memory_cache[key]
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")


class RateLimiter:
    """请求限流器"""
    
    def __init__(self):
        self._requests: Dict[str, list] = {}
    
    def is_allowed(self, identifier: str, limit: int = None, window: int = None) -> bool:
        """检查是否允许请求"""
        limit = limit or PerformanceConfig.RATE_LIMIT_REQUESTS
        window = window or PerformanceConfig.RATE_LIMIT_WINDOW
        
        current_time = time.time()
        
        # 获取或创建请求记录
        if identifier not in self._requests:
            self._requests[identifier] = []
        
        requests = self._requests[identifier]
        
        # 清理过期请求
        cutoff_time = current_time - window
        self._requests[identifier] = [req_time for req_time in requests if req_time > cutoff_time]
        
        # 检查是否超过限制
        if len(self._requests[identifier]) >= limit:
            return False
        
        # 记录当前请求
        self._requests[identifier].append(current_time)
        return True
    
    def get_remaining_requests(self, identifier: str, limit: int = None) -> int:
        """获取剩余请求次数"""
        limit = limit or PerformanceConfig.RATE_LIMIT_REQUESTS
        current_requests = len(self._requests.get(identifier, []))
        return max(0, limit - current_requests)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self._request_times: list = []
        self._slow_requests: list = []
    
    def record_request_time(self, endpoint: str, duration: float) -> None:
        """记录请求时间"""
        self._request_times.append({
            "endpoint": endpoint,
            "duration": duration,
            "timestamp": time.time()
        })
        
        # 检测慢请求
        if duration > PerformanceConfig.SLOW_REQUEST_THRESHOLD:
            self._slow_requests.append({
                "endpoint": endpoint,
                "duration": duration,
                "timestamp": time.time()
            })
            logger.warning(f"慢请求检测: {endpoint} 耗时: {duration:.3f}s")
        
        # 保持最近1000条记录
        if len(self._request_times) > 1000:
            self._request_times = self._request_times[-1000:]
        
        if len(self._slow_requests) > 100:
            self._slow_requests = self._slow_requests[-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self._request_times:
            return {"message": "暂无性能数据"}
        
        recent_requests = [
            req for req in self._request_times
            if time.time() - req["timestamp"] < 300  # 最近5分钟
        ]
        
        if not recent_requests:
            return {"message": "最近5分钟无请求数据"}
        
        durations = [req["duration"] for req in recent_requests]
        
        return {
            "total_requests": len(recent_requests),
            "avg_response_time": sum(durations) / len(durations),
            "min_response_time": min(durations),
            "max_response_time": max(durations),
            "slow_requests_count": len([req for req in recent_requests if req["duration"] > PerformanceConfig.SLOW_REQUEST_THRESHOLD]),
            "requests_per_minute": len(recent_requests) / 5
        }


def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            endpoint = getattr(func, '__name__', 'unknown')
            performance_monitor_instance.record_request_time(endpoint, duration)
    
    return wrapper


def cache_result(ttl: int = None, key_prefix: str = ""):
    """结果缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


# 全局实例
cache_manager = CacheManager()
rate_limiter = RateLimiter()
performance_monitor_instance = PerformanceMonitor()


async def initialize_performance_components(redis_url: str = None) -> None:
    """初始化性能组件"""
    if redis_url:
        await cache_manager.initialize_redis(redis_url)
    
    logger.info("性能组件初始化完成")


async def cleanup_performance_components() -> None:
    """清理性能组件"""
    if cache_manager._redis_client:
        await cache_manager._redis_client.close()
    
    logger.info("性能组件清理完成")
