"""数据验证工具模块

提供通用的数据验证、清理和格式化功能
"""

import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import ValidationError
from fastapi import HTTPException, status


class ValidationUtils:
    """数据验证工具类"""
    
    # 常用正则表达式
    USERNAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]{3,50}$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^1[3-9]\d{9}$')  # 中国手机号
    
    @staticmethod
    def validate_username(username: str) -> str:
        """验证用户名格式"""
        if not username:
            raise ValueError("用户名不能为空")
        
        username = username.strip().lower()
        
        if not ValidationUtils.USERNAME_PATTERN.match(username):
            raise ValueError("用户名只能包含字母、数字和下划线，长度3-50字符")
        
        # 检查敏感词
        forbidden_words = ['admin', 'root', 'system', 'null', 'undefined']
        if username in forbidden_words:
            raise ValueError("用户名包含禁用词汇")
        
        return username
    
    @staticmethod
    def validate_password(password: str) -> str:
        """验证密码强度"""
        if not password:
            raise ValueError("密码不能为空")
        
        if len(password) < 8:
            raise ValueError("密码长度至少8位")
        
        if len(password) > 128:
            raise ValueError("密码长度不能超过128位")
        
        # 检查密码复杂度
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        complexity_count = sum([has_upper, has_lower, has_digit, has_special])
        if complexity_count < 2:
            raise ValueError("密码必须包含至少两种类型的字符（大写字母、小写字母、数字、特殊字符）")
        
        return password
    
    @staticmethod
    def validate_email(email: str) -> str:
        """验证邮箱格式"""
        if not email:
            raise ValueError("邮箱不能为空")
        
        email = email.strip().lower()
        
        if not ValidationUtils.EMAIL_PATTERN.match(email):
            raise ValueError("邮箱格式不正确")
        
        return email
    
    @staticmethod
    def validate_phone(phone: str) -> str:
        """验证手机号格式"""
        if not phone:
            raise ValueError("手机号不能为空")
        
        phone = phone.strip()
        
        if not ValidationUtils.PHONE_PATTERN.match(phone):
            raise ValueError("手机号格式不正确")
        
        return phone
    
    @staticmethod
    def sanitize_text(text: str, max_length: int = 1000) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 去除首尾空白
        text = text.strip()
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        # 移除危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\x08', '\x0b', '\x0c', '\x0e']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        return text
    
    @staticmethod
    def validate_pagination(page: int, per_page: int) -> tuple[int, int]:
        """验证分页参数"""
        if page < 1:
            page = 1
        
        if per_page < 1:
            per_page = 20
        elif per_page > 100:
            per_page = 100
        
        return page, per_page
    
    @staticmethod
    def validate_file_size(file_size: int, max_size: int = 10 * 1024 * 1024) -> bool:
        """验证文件大小"""
        return 0 < file_size <= max_size
    
    @staticmethod
    def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
        """验证文件扩展名"""
        if not filename:
            return False
        
        ext = filename.lower().split('.')[-1] if '.' in filename else ''
        return ext in [ext.lower() for ext in allowed_extensions]


class ResponseFormatter:
    """响应格式化工具类"""
    
    @staticmethod
    def success_response(
        data: Any = None,
        message: str = "操作成功",
        timestamp: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """格式化成功响应"""
        return {
            "success": True,
            "message": message,
            "data": data,
            "error_code": None,
            "timestamp": (timestamp or datetime.utcnow()).isoformat() + "Z"
        }
    
    @staticmethod
    def error_response(
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None,
        timestamp: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            "success": False,
            "message": message,
            "data": None,
            "error_code": error_code,
            "details": details,
            "timestamp": (timestamp or datetime.utcnow()).isoformat() + "Z"
        }
    
    @staticmethod
    def paginated_response(
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        message: str = "数据获取成功"
    ) -> Dict[str, Any]:
        """格式化分页响应"""
        pages = (total + per_page - 1) // per_page
        
        return {
            "success": True,
            "message": message,
            "data": data,
            "meta": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": pages,
                "has_next": page < pages,
                "has_prev": page > 1
            },
            "error_code": None,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    
    @staticmethod
    def validation_error_response(
        validation_error: ValidationError
    ) -> Dict[str, Any]:
        """格式化验证错误响应"""
        errors = []
        for error in validation_error.errors():
            field = ".".join(str(loc) for loc in error["loc"])
            errors.append({
                "field": field,
                "message": error["msg"],
                "value": error.get("input")
            })
        
        return {
            "success": False,
            "message": "数据验证失败",
            "error_code": "VALIDATION_ERROR",
            "errors": errors,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }


def handle_validation_error(validation_error: ValidationError) -> HTTPException:
    """处理Pydantic验证错误"""
    response = ResponseFormatter.validation_error_response(validation_error)
    raise HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail=response
    )


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == "":
            missing_fields.append(field)
    
    if missing_fields:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ResponseFormatter.error_response(
                message=f"缺少必需字段: {', '.join(missing_fields)}",
                error_code="MISSING_REQUIRED_FIELDS",
                details={"missing_fields": missing_fields}
            )
        )


def validate_id_list(ids: List[int], max_count: int = 100) -> List[int]:
    """验证ID列表"""
    if not ids:
        raise ValueError("ID列表不能为空")
    
    if len(ids) > max_count:
        raise ValueError(f"ID列表长度不能超过{max_count}")
    
    # 去重并验证
    unique_ids = list(set(ids))
    for id_val in unique_ids:
        if not isinstance(id_val, int) or id_val <= 0:
            raise ValueError(f"无效的ID: {id_val}")
    
    return unique_ids


def validate_date_range(start_date: Optional[datetime], end_date: Optional[datetime]) -> tuple[Optional[datetime], Optional[datetime]]:
    """验证日期范围"""
    if start_date and end_date:
        if start_date > end_date:
            raise ValueError("开始日期不能晚于结束日期")
        
        # 限制查询范围不超过1年
        if (end_date - start_date).days > 365:
            raise ValueError("日期范围不能超过1年")
    
    return start_date, end_date
