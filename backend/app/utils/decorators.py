"""装饰器工具模块

提供数据验证、缓存、限流等装饰器
"""

import functools
import time
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar
from datetime import datetime, timedelta

from fastapi import HTTPException, status, Depends
from pydantic import BaseModel, ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

# 简单的日志记录
import logging
logger = logging.getLogger(__name__)

from app.utils.validation import ResponseFormatter, handle_validation_error
from app.utils.serialization import SerializationUtils
from app.utils.exceptions import BaseCustomException

T = TypeVar('T', bound=BaseModel)


def validate_input(schema_class: Type[T]):
    """输入数据验证装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # 查找请求数据
                request_data = None
                for arg in args:
                    if isinstance(arg, dict):
                        request_data = arg
                        break
                
                if request_data is None:
                    for key, value in kwargs.items():
                        if isinstance(value, dict):
                            request_data = value
                            break
                
                # 验证数据
                if request_data:
                    validated_data = schema_class.model_validate(request_data)
                    # 替换原始数据
                    for i, arg in enumerate(args):
                        if isinstance(arg, dict):
                            args = list(args)
                            args[i] = validated_data.model_dump()
                            break
                    
                    for key, value in kwargs.items():
                        if isinstance(value, dict):
                            kwargs[key] = validated_data.model_dump()
                            break
                
                return await func(*args, **kwargs)
                
            except ValidationError as e:
                handle_validation_error(e)
            except Exception as e:
                logger.error(f"Input validation error: {e}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=ResponseFormatter.error_response(
                        message="输入数据验证失败",
                        error_code="INPUT_VALIDATION_ERROR"
                    )
                )
        
        return wrapper
    return decorator


def format_response(success_message: str = "操作成功"):
    """响应格式化装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                
                # 如果结果已经是格式化的响应，直接返回
                if isinstance(result, dict) and "success" in result:
                    return result
                
                # 格式化响应
                return ResponseFormatter.success_response(
                    data=result,
                    message=success_message
                )
                
            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                logger.error(f"Response formatting error: {e}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ResponseFormatter.error_response(
                        message="响应格式化失败",
                        error_code="RESPONSE_FORMAT_ERROR"
                    )
                )
        
        return wrapper
    return decorator


def serialize_output(schema_class: Type[T]):
    """输出序列化装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                
                if result is None:
                    return None
                
                # 序列化结果
                if isinstance(result, list):
                    return SerializationUtils.serialize_model_list(
                        result, schema_class
                    )
                else:
                    if isinstance(result, BaseModel):
                        return result.model_dump(exclude_none=True)
                    else:
                        # SQLAlchemy模型转换
                        schema_instance = schema_class.model_validate(result)
                        return schema_instance.model_dump(exclude_none=True)
                
            except Exception as e:
                logger.error(f"Output serialization error: {e}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ResponseFormatter.error_response(
                        message="数据序列化失败",
                        error_code="SERIALIZATION_ERROR"
                    )
                )
        
        return wrapper
    return decorator


def cache_result(ttl_seconds: int = 300, key_prefix: str = "cache"):
    """结果缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            try:
                # 尝试从缓存获取
                # 这里需要Redis连接，暂时跳过实现
                # cached_result = await redis_client.get(cache_key)
                # if cached_result:
                #     return json.loads(cached_result)
                
                # 执行函数
                result = await func(*args, **kwargs)
                
                # 缓存结果
                # await redis_client.setex(
                #     cache_key,
                #     ttl_seconds,
                #     json.dumps(result, default=str)
                # )
                
                return result
                
            except Exception as e:
                logger.warning(f"Cache operation failed: {e}")
                # 缓存失败时直接执行函数
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit(max_requests: int = 100, window_seconds: int = 60):
    """限流装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取用户标识
            user_id = None
            for arg in args:
                if hasattr(arg, 'user') and hasattr(arg.user, 'id'):
                    user_id = arg.user.id
                    break
            
            if user_id:
                # 检查限流
                rate_key = f"rate_limit:{func.__name__}:{user_id}"
                
                try:
                    # 这里需要Redis连接，暂时跳过实现
                    # current_requests = await redis_client.get(rate_key)
                    # if current_requests and int(current_requests) >= max_requests:
                    #     raise HTTPException(
                    #         status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    #         detail=ResponseFormatter.error_response(
                    #             message="请求过于频繁，请稍后再试",
                    #             error_code="RATE_LIMIT_EXCEEDED"
                    #         )
                    #     )
                    
                    # 执行函数
                    result = await func(*args, **kwargs)
                    
                    # 更新计数
                    # await redis_client.incr(rate_key)
                    # await redis_client.expire(rate_key, window_seconds)
                    
                    return result
                    
                except HTTPException:
                    raise
                except Exception as e:
                    logger.warning(f"Rate limiting failed: {e}")
                    # 限流失败时直接执行函数
                    return await func(*args, **kwargs)
            else:
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_execution(log_level: str = "info"):
    """执行日志装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                logger.log(
                    getattr(logging, log_level.upper(), logging.INFO),
                    f"Executing {func.__name__} with args={len(args)}, kwargs={len(kwargs)}"
                )
                
                result = await func(*args, **kwargs)
                
                execution_time = time.time() - start_time
                logger.log(
                    getattr(logging, log_level.upper(), logging.INFO),
                    f"Completed {func.__name__} in {execution_time:.3f}s"
                )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Failed {func.__name__} in {execution_time:.3f}s: {e}",
                    exc_info=True
                )
                raise
        
        return wrapper
    return decorator


def require_permissions(permissions: List[str]):
    """权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 查找当前用户
            current_user = None
            for arg in args:
                if hasattr(arg, 'user'):
                    current_user = arg.user
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=ResponseFormatter.error_response(
                        message="需要登录",
                        error_code="AUTHENTICATION_REQUIRED"
                    )
                )
            
            # 检查权限（这里简化实现，实际项目中需要更复杂的权限系统）
            user_permissions = getattr(current_user, 'permissions', [])
            
            for permission in permissions:
                if permission not in user_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=ResponseFormatter.error_response(
                            message="权限不足",
                            error_code="INSUFFICIENT_PERMISSIONS",
                            details={"required_permission": permission}
                        )
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def handle_database_errors(func: Callable) -> Callable:
    """数据库错误处理装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except BaseCustomException:
            # 重新抛出自定义异常（包括ConflictError等业务逻辑异常）
            raise
        except Exception as e:
            error_message = str(e).lower()

            # 只处理真正的数据库错误
            if "unique constraint" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=ResponseFormatter.error_response(
                        message="数据已存在",
                        error_code="DUPLICATE_DATA"
                    )
                )
            elif "foreign key constraint" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=ResponseFormatter.error_response(
                        message="关联数据不存在",
                        error_code="FOREIGN_KEY_ERROR"
                    )
                )
            elif "not found" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=ResponseFormatter.error_response(
                        message="数据不存在",
                        error_code="DATA_NOT_FOUND"
                    )
                )
            else:
                logger.error(f"Database error: {e}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ResponseFormatter.error_response(
                        message="数据库操作失败",
                        error_code="DATABASE_ERROR"
                    )
                )

    return wrapper
