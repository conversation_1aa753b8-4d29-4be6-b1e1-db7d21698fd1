"""日志配置和管理模块

提供统一的日志配置、格式化和管理功能。
支持文件日志、控制台日志和结构化日志记录。
"""

import logging
import logging.handlers
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import contextmanager

from app.core.config import settings


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        if hasattr(record, 'ip_address'):
            log_data["ip_address"] = record.ip_address
        if hasattr(record, 'user_agent'):
            log_data["user_agent"] = record.user_agent
            
        return json.dumps(log_data, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """彩色控制台格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为彩色输出"""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建日志消息
        log_message = (
            f"{color}[{timestamp}] {record.levelname:8s}{reset} "
            f"{record.name}:{record.lineno} - {record.getMessage()}"
        )
        
        # 添加异常信息
        if record.exc_info:
            log_message += f"\n{self.formatException(record.exc_info)}"
            
        return log_message


class LoggerManager:
    """日志管理器"""
    
    _loggers: Dict[str, logging.Logger] = {}
    _configured = False
    
    @classmethod
    def setup_logging(cls) -> None:
        """设置全局日志配置"""
        if cls._configured:
            return
            
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 根日志器配置
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG if settings.debug else logging.INFO)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG if settings.debug else logging.INFO)
        console_handler.setFormatter(ColoredFormatter())
        root_logger.addHandler(console_handler)
        
        # 文件处理器 - 应用日志
        app_file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "app.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_file_handler.setLevel(logging.INFO)
        app_file_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(app_file_handler)
        
        # 错误日志文件处理器
        error_file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "error.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(error_file_handler)
        
        # 访问日志文件处理器
        access_file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "access.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        access_file_handler.setLevel(logging.INFO)
        access_file_handler.setFormatter(JSONFormatter())
        
        # 创建访问日志器
        access_logger = logging.getLogger("access")
        access_logger.setLevel(logging.INFO)
        access_logger.addHandler(access_file_handler)
        access_logger.propagate = False
        
        # 配置第三方库日志级别
        logging.getLogger("uvicorn").setLevel(logging.INFO)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
        logging.getLogger("asyncpg").setLevel(logging.WARNING)
        
        cls._configured = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """获取日志器"""
        if not cls._configured:
            cls.setup_logging()
            
        if name not in cls._loggers:
            cls._loggers[name] = logging.getLogger(name)
            
        return cls._loggers[name]


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = LoggerManager.get_logger("access")
    
    def log_request(
        self,
        method: str,
        url: str,
        status_code: int,
        response_time: float,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None
    ) -> None:
        """记录HTTP请求日志"""
        extra = {
            "user_id": user_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "request_size": request_size,
            "response_size": response_size
        }
        
        message = (
            f"{method} {url} - {status_code} - "
            f"{response_time:.3f}s"
        )
        
        if status_code >= 400:
            self.logger.error(message, extra=extra)
        else:
            self.logger.info(message, extra=extra)


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self):
        self.logger = LoggerManager.get_logger("performance")
    
    @contextmanager
    def log_execution_time(self, operation: str, **kwargs):
        """记录操作执行时间"""
        start_time = datetime.utcnow()
        try:
            yield
        finally:
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            extra = kwargs
            extra.update({
                "operation": operation,
                "duration": duration,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            })
            
            if duration > 1.0:  # 超过1秒的操作记录为警告
                self.logger.warning(f"慢操作: {operation} 耗时 {duration:.3f}s", extra=extra)
            else:
                self.logger.info(f"操作: {operation} 耗时 {duration:.3f}s", extra=extra)


class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self):
        self.logger = LoggerManager.get_logger("security")
    
    def log_login_attempt(
        self,
        username: str,
        success: bool,
        ip_address: str,
        user_agent: str,
        reason: Optional[str] = None
    ) -> None:
        """记录登录尝试"""
        extra = {
            "username": username,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "event_type": "login_attempt"
        }
        
        if success:
            self.logger.info(f"用户 {username} 登录成功", extra=extra)
        else:
            extra["failure_reason"] = reason
            self.logger.warning(f"用户 {username} 登录失败: {reason}", extra=extra)
    
    def log_permission_denied(
        self,
        user_id: int,
        resource: str,
        action: str,
        ip_address: str
    ) -> None:
        """记录权限拒绝"""
        extra = {
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "ip_address": ip_address,
            "event_type": "permission_denied"
        }
        
        self.logger.warning(
            f"用户 {user_id} 尝试访问 {resource}:{action} 被拒绝",
            extra=extra
        )
    
    def log_suspicious_activity(
        self,
        description: str,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        **kwargs
    ) -> None:
        """记录可疑活动"""
        extra = {
            "user_id": user_id,
            "ip_address": ip_address,
            "event_type": "suspicious_activity"
        }
        extra.update(kwargs)
        
        self.logger.error(f"可疑活动: {description}", extra=extra)


# 全局日志器实例
logger_manager = LoggerManager()
request_logger = RequestLogger()
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()


# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return LoggerManager.get_logger(name)


def setup_logging() -> None:
    """设置日志配置"""
    LoggerManager.setup_logging()


# 初始化日志配置
setup_logging()
