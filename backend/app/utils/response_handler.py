"""API响应处理器

提供统一的API响应处理和格式化功能
"""

from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from datetime import datetime
from fastapi import HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.utils.validation import ResponseFormatter
from app.utils.serialization import SerializationUtils, ModelConverter

# 简单的日志记录
import logging
logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


class APIResponseHandler:
    """API响应处理器"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        status_code: int = status.HTTP_200_OK
    ) -> JSONResponse:
        """返回成功响应"""
        response_data = ResponseFormatter.success_response(
            data=data,
            message=message
        )
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "创建成功"
    ) -> JSONResponse:
        """返回创建成功响应"""
        return APIResponseHandler.success(
            data=data,
            message=message,
            status_code=status.HTTP_201_CREATED
        )
    
    @staticmethod
    def no_content(message: str = "操作成功") -> JSONResponse:
        """返回无内容响应"""
        return APIResponseHandler.success(
            data=None,
            message=message,
            status_code=status.HTTP_204_NO_CONTENT
        )
    
    @staticmethod
    def paginated(
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        message: str = "数据获取成功",
        schema_class: Optional[Type[T]] = None
    ) -> JSONResponse:
        """返回分页响应"""
        # 序列化数据
        if schema_class and data:
            serialized_data = SerializationUtils.serialize_model_list(
                data, schema_class
            )
        else:
            serialized_data = SerializationUtils.to_dict(data)
        
        response_data = ResponseFormatter.paginated_response(
            data=serialized_data,
            page=page,
            per_page=per_page,
            total=total,
            message=message
        )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=response_data
        )
    
    @staticmethod
    def error(
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        status_code: int = status.HTTP_400_BAD_REQUEST,
        details: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """返回错误响应"""
        response_data = ResponseFormatter.error_response(
            message=message,
            error_code=error_code,
            details=details
        )
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    @staticmethod
    def not_found(
        message: str = "资源不存在",
        error_code: str = "RESOURCE_NOT_FOUND"
    ) -> JSONResponse:
        """返回404响应"""
        return APIResponseHandler.error(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def forbidden(
        message: str = "权限不足",
        error_code: str = "PERMISSION_DENIED"
    ) -> JSONResponse:
        """返回403响应"""
        return APIResponseHandler.error(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_403_FORBIDDEN
        )
    
    @staticmethod
    def unauthorized(
        message: str = "需要登录",
        error_code: str = "AUTHENTICATION_REQUIRED"
    ) -> JSONResponse:
        """返回401响应"""
        return APIResponseHandler.error(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def conflict(
        message: str = "数据冲突",
        error_code: str = "DATA_CONFLICT"
    ) -> JSONResponse:
        """返回409响应"""
        return APIResponseHandler.error(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_409_CONFLICT
        )
    
    @staticmethod
    def validation_error(
        message: str = "数据验证失败",
        errors: Optional[List[Dict[str, Any]]] = None
    ) -> JSONResponse:
        """返回验证错误响应"""
        response_data = ResponseFormatter.error_response(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"errors": errors} if errors else None
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response_data
        )


class ModelResponseHandler:
    """模型响应处理器"""
    
    @staticmethod
    def single_model(
        model: Any,
        schema_class: Type[T],
        message: str = "数据获取成功"
    ) -> JSONResponse:
        """返回单个模型响应"""
        if model is None:
            return APIResponseHandler.not_found()
        
        try:
            # 转换为Pydantic模型
            if isinstance(model, BaseModel):
                data = model.model_dump(exclude_none=True)
            else:
                schema_instance = ModelConverter.sqlalchemy_to_pydantic(
                    model, schema_class
                )
                data = schema_instance.model_dump(exclude_none=True)
            
            return APIResponseHandler.success(data=data, message=message)
            
        except Exception as e:
            logger.error(f"Model serialization error: {e}", exc_info=True)
            return APIResponseHandler.error(
                message="数据序列化失败",
                error_code="SERIALIZATION_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @staticmethod
    def model_list(
        models: List[Any],
        schema_class: Type[T],
        page: Optional[int] = None,
        per_page: Optional[int] = None,
        total: Optional[int] = None,
        message: str = "数据获取成功"
    ) -> JSONResponse:
        """返回模型列表响应"""
        try:
            # 如果提供了分页信息，返回分页响应
            if page is not None and per_page is not None and total is not None:
                return APIResponseHandler.paginated(
                    data=models,
                    page=page,
                    per_page=per_page,
                    total=total,
                    message=message,
                    schema_class=schema_class
                )
            
            # 序列化模型列表
            serialized_data = SerializationUtils.serialize_model_list(
                models, schema_class
            )
            
            return APIResponseHandler.success(
                data=serialized_data,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Model list serialization error: {e}", exc_info=True)
            return APIResponseHandler.error(
                message="数据序列化失败",
                error_code="SERIALIZATION_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @staticmethod
    def created_model(
        model: Any,
        schema_class: Type[T],
        message: str = "创建成功"
    ) -> JSONResponse:
        """返回创建模型响应"""
        try:
            # 转换为Pydantic模型
            if isinstance(model, BaseModel):
                data = model.model_dump(exclude_none=True)
            else:
                schema_instance = ModelConverter.sqlalchemy_to_pydantic(
                    model, schema_class
                )
                data = schema_instance.model_dump(exclude_none=True)
            
            return APIResponseHandler.created(data=data, message=message)
            
        except Exception as e:
            logger.error(f"Created model serialization error: {e}", exc_info=True)
            return APIResponseHandler.error(
                message="数据序列化失败",
                error_code="SERIALIZATION_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FileResponseHandler:
    """文件响应处理器"""
    
    @staticmethod
    def upload_success(
        file_info: Dict[str, Any],
        message: str = "文件上传成功"
    ) -> JSONResponse:
        """文件上传成功响应"""
        return APIResponseHandler.created(
            data=file_info,
            message=message
        )
    
    @staticmethod
    def upload_error(
        message: str,
        error_code: str = "UPLOAD_ERROR"
    ) -> JSONResponse:
        """文件上传错误响应"""
        return APIResponseHandler.error(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    @staticmethod
    def file_too_large(max_size: int) -> JSONResponse:
        """文件过大错误响应"""
        max_size_mb = max_size / (1024 * 1024)
        return APIResponseHandler.error(
            message=f"文件大小不能超过{max_size_mb:.1f}MB",
            error_code="FILE_TOO_LARGE",
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
        )
    
    @staticmethod
    def file_type_not_allowed(allowed_types: List[str]) -> JSONResponse:
        """文件类型不允许错误响应"""
        return APIResponseHandler.error(
            message=f"文件类型不被允许，支持的类型：{', '.join(allowed_types)}",
            error_code="FILE_TYPE_NOT_ALLOWED",
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    @staticmethod
    def file_not_found() -> JSONResponse:
        """文件不存在响应"""
        return APIResponseHandler.not_found(
            message="文件不存在",
            error_code="FILE_NOT_FOUND"
        )


# 便捷函数
def success_response(data: Any = None, message: str = "操作成功") -> JSONResponse:
    """便捷的成功响应函数"""
    return APIResponseHandler.success(data=data, message=message)


def error_response(
    message: str,
    error_code: str = "UNKNOWN_ERROR",
    status_code: int = status.HTTP_400_BAD_REQUEST
) -> JSONResponse:
    """便捷的错误响应函数"""
    return APIResponseHandler.error(
        message=message,
        error_code=error_code,
        status_code=status_code
    )
