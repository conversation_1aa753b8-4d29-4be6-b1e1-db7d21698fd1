"""异常处理模块

定义自定义异常类和全局异常处理器。
提供统一的错误响应格式和异常日志记录。
"""

import logging
import traceback
from typing import Dict, Any, Optional, Union
from datetime import datetime

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
from redis.exceptions import RedisError

from app.utils.logging import get_logger

logger = get_logger(__name__)


class BaseCustomException(Exception):
    """自定义异常基类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(BaseCustomException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details or {}
        )
        if field:
            self.details["field"] = field


class AuthenticationError(BaseCustomException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details or {}
        )


class AuthorizationError(BaseCustomException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=status.HTTP_403_FORBIDDEN,
            details=details or {}
        )


class NotFoundError(BaseCustomException):
    """资源不存在异常"""
    
    def __init__(self, resource: str, resource_id: Union[str, int] = None):
        message = f"{resource}不存在"
        if resource_id:
            message += f" (ID: {resource_id})"
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND_ERROR",
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource": resource, "resource_id": resource_id}
        )


class ConflictError(BaseCustomException):
    """资源冲突异常"""
    
    def __init__(self, message: str, resource: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="CONFLICT_ERROR",
            status_code=status.HTTP_409_CONFLICT,
            details=details or {}
        )
        if resource:
            self.details["resource"] = resource


class RateLimitError(BaseCustomException):
    """限流异常"""
    
    def __init__(self, message: str = "请求过于频繁", retry_after: int = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )
        if retry_after:
            self.details["retry_after"] = retry_after


class DatabaseError(BaseCustomException):
    """数据库异常"""
    
    def __init__(self, message: str = "数据库操作失败", original_error: Exception = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        if original_error:
            self.details["original_error"] = str(original_error)


class ExternalServiceError(BaseCustomException):
    """外部服务异常"""
    
    def __init__(self, service: str, message: str = None, original_error: Exception = None):
        message = message or f"{service}服务不可用"
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details={"service": service}
        )
        if original_error:
            self.details["original_error"] = str(original_error)


class ExceptionHandler:
    """异常处理器"""
    
    @staticmethod
    def create_error_response(
        error_code: str,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """创建错误响应"""
        error_data = {
            "error": {
                "code": error_code,
                "message": message,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        }
        
        if details:
            error_data["error"]["details"] = details
            
        if request_id:
            error_data["error"]["request_id"] = request_id
            
        return JSONResponse(
            status_code=status_code,
            content=error_data
        )
    
    @staticmethod
    async def custom_exception_handler(request: Request, exc: BaseCustomException) -> JSONResponse:
        """自定义异常处理器"""
        request_id = getattr(request.state, 'request_id', None)
        
        # 记录异常日志
        logger.error(
            f"自定义异常: {exc.error_code} - {exc.message}",
            extra={
                "error_code": exc.error_code,
                "status_code": exc.status_code,
                "details": exc.details,
                "request_id": request_id,
                "url": str(request.url),
                "method": request.method
            },
            exc_info=True
        )
        
        return ExceptionHandler.create_error_response(
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
            details=exc.details,
            request_id=request_id
        )
    
    @staticmethod
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """HTTP异常处理器"""
        request_id = getattr(request.state, 'request_id', None)
        
        logger.warning(
            f"HTTP异常: {exc.status_code} - {exc.detail}",
            extra={
                "status_code": exc.status_code,
                "request_id": request_id,
                "url": str(request.url),
                "method": request.method
            }
        )
        
        return ExceptionHandler.create_error_response(
            error_code="HTTP_ERROR",
            message=str(exc.detail),
            status_code=exc.status_code,
            request_id=request_id
        )
    
    @staticmethod
    async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """数据验证异常处理器"""
        request_id = getattr(request.state, 'request_id', None)
        
        # 格式化验证错误
        errors = []
        for error in exc.errors():
            field = ".".join(str(loc) for loc in error["loc"])
            errors.append({
                "field": field,
                "message": error["msg"],
                "type": error["type"]
            })
        
        logger.warning(
            f"数据验证失败: {len(errors)}个错误",
            extra={
                "validation_errors": errors,
                "request_id": request_id,
                "url": str(request.url),
                "method": request.method
            }
        )
        
        return ExceptionHandler.create_error_response(
            error_code="VALIDATION_ERROR",
            message="数据验证失败",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"errors": errors},
            request_id=request_id
        )
    
    @staticmethod
    async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """通用异常处理器"""
        request_id = getattr(request.state, 'request_id', None)
        
        # 记录详细的异常信息
        logger.error(
            f"未处理的异常: {type(exc).__name__} - {str(exc)}",
            extra={
                "exception_type": type(exc).__name__,
                "request_id": request_id,
                "url": str(request.url),
                "method": request.method,
                "traceback": traceback.format_exc()
            },
            exc_info=True
        )
        
        # 根据异常类型返回不同的错误码
        if isinstance(exc, SQLAlchemyError):
            error_code = "DATABASE_ERROR"
            message = "数据库操作失败"
        elif isinstance(exc, RedisError):
            error_code = "CACHE_ERROR"
            message = "缓存服务不可用"
        else:
            error_code = "INTERNAL_SERVER_ERROR"
            message = "服务器内部错误"
        
        return ExceptionHandler.create_error_response(
            error_code=error_code,
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            request_id=request_id
        )


# 便捷函数
def log_exception(exc: Exception, context: str = "", **kwargs) -> None:
    """记录异常日志"""
    logger.error(
        f"异常发生 [{context}]: {type(exc).__name__} - {str(exc)}",
        extra=kwargs,
        exc_info=True
    )


def raise_not_found(resource: str, resource_id: Union[str, int] = None) -> None:
    """抛出资源不存在异常"""
    raise NotFoundError(resource, resource_id)


def raise_validation_error(message: str, field: str = None, **details) -> None:
    """抛出数据验证异常"""
    raise ValidationError(message, field, details)


def raise_authentication_error(message: str = "认证失败", **details) -> None:
    """抛出认证异常"""
    raise AuthenticationError(message, details)


def raise_authorization_error(message: str = "权限不足", **details) -> None:
    """抛出授权异常"""
    raise AuthorizationError(message, details)
