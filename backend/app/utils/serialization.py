"""序列化工具模块

提供数据序列化、反序列化和格式转换功能
"""

import json
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from datetime import datetime, date
from decimal import Decimal
from pydantic import BaseModel
from sqlalchemy.orm import DeclarativeBase

T = TypeVar('T', bound=BaseModel)


class SerializationUtils:
    """序列化工具类"""
    
    @staticmethod
    def serialize_datetime(dt: datetime) -> str:
        """序列化datetime对象"""
        if dt is None:
            return None
        return dt.isoformat() + "Z"
    
    @staticmethod
    def serialize_date(d: date) -> str:
        """序列化date对象"""
        if d is None:
            return None
        return d.isoformat()
    
    @staticmethod
    def serialize_decimal(decimal_val: Decimal) -> float:
        """序列化Decimal对象"""
        if decimal_val is None:
            return None
        return float(decimal_val)
    
    @staticmethod
    def to_dict(obj: Any, exclude_none: bool = True) -> Dict[str, Any]:
        """将对象转换为字典"""
        if obj is None:
            return None
        
        if isinstance(obj, BaseModel):
            return obj.model_dump(exclude_none=exclude_none)
        
        if isinstance(obj, DeclarativeBase):
            # SQLAlchemy模型
            result = {}
            for column in obj.__table__.columns:
                value = getattr(obj, column.name)
                if exclude_none and value is None:
                    continue
                
                # 处理特殊类型
                if isinstance(value, datetime):
                    value = SerializationUtils.serialize_datetime(value)
                elif isinstance(value, date):
                    value = SerializationUtils.serialize_date(value)
                elif isinstance(value, Decimal):
                    value = SerializationUtils.serialize_decimal(value)
                
                result[column.name] = value
            
            return result
        
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                if exclude_none and value is None:
                    continue
                result[key] = SerializationUtils.to_dict(value, exclude_none)
            return result
        
        if isinstance(obj, (list, tuple)):
            return [SerializationUtils.to_dict(item, exclude_none) for item in obj]
        
        return obj
    
    @staticmethod
    def to_json(obj: Any, exclude_none: bool = True, indent: Optional[int] = None) -> str:
        """将对象转换为JSON字符串"""
        data = SerializationUtils.to_dict(obj, exclude_none)
        return json.dumps(data, ensure_ascii=False, indent=indent)
    
    @staticmethod
    def from_json(json_str: str, model_class: Type[T]) -> T:
        """从JSON字符串创建Pydantic模型"""
        try:
            data = json.loads(json_str)
            return model_class.model_validate(data)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {e}")
        except Exception as e:
            raise ValueError(f"数据验证失败: {e}")
    
    @staticmethod
    def serialize_model_list(
        models: List[Any],
        schema_class: Type[T],
        exclude_none: bool = True
    ) -> List[Dict[str, Any]]:
        """序列化模型列表"""
        result = []
        for model in models:
            if isinstance(model, BaseModel):
                result.append(model.model_dump(exclude_none=exclude_none))
            else:
                # SQLAlchemy模型转换为Pydantic
                schema_instance = schema_class.model_validate(model)
                result.append(schema_instance.model_dump(exclude_none=exclude_none))
        
        return result
    
    @staticmethod
    def clean_dict(data: Dict[str, Any], remove_empty: bool = True) -> Dict[str, Any]:
        """清理字典数据"""
        if not isinstance(data, dict):
            return data
        
        cleaned = {}
        for key, value in data.items():
            # 递归清理嵌套字典
            if isinstance(value, dict):
                value = SerializationUtils.clean_dict(value, remove_empty)
            elif isinstance(value, list):
                value = [
                    SerializationUtils.clean_dict(item, remove_empty) 
                    if isinstance(item, dict) else item 
                    for item in value
                ]
            
            # 移除空值
            if remove_empty:
                if value is None or value == "" or value == []:
                    continue
            
            cleaned[key] = value
        
        return cleaned


class ModelConverter:
    """模型转换工具类"""
    
    @staticmethod
    def sqlalchemy_to_pydantic(
        sqlalchemy_obj: Any,
        pydantic_class: Type[T],
        exclude_fields: Optional[List[str]] = None
    ) -> T:
        """将SQLAlchemy模型转换为Pydantic模型"""
        if sqlalchemy_obj is None:
            return None
        
        # 获取模型数据
        data = {}
        for column in sqlalchemy_obj.__table__.columns:
            if exclude_fields and column.name in exclude_fields:
                continue
            data[column.name] = getattr(sqlalchemy_obj, column.name)
        
        # 处理关系字段
        for relationship in sqlalchemy_obj.__mapper__.relationships:
            if exclude_fields and relationship.key in exclude_fields:
                continue
            
            related_obj = getattr(sqlalchemy_obj, relationship.key)
            if related_obj is not None:
                data[relationship.key] = related_obj
        
        return pydantic_class.model_validate(data)
    
    @staticmethod
    def pydantic_to_dict(
        pydantic_obj: BaseModel,
        exclude_none: bool = True,
        exclude_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """将Pydantic模型转换为字典"""
        exclude_set = set(exclude_fields) if exclude_fields else set()
        return pydantic_obj.model_dump(
            exclude_none=exclude_none,
            exclude=exclude_set
        )
    
    @staticmethod
    def batch_convert(
        objects: List[Any],
        target_class: Type[T],
        exclude_fields: Optional[List[str]] = None
    ) -> List[T]:
        """批量转换对象"""
        result = []
        for obj in objects:
            if isinstance(obj, target_class):
                result.append(obj)
            else:
                converted = ModelConverter.sqlalchemy_to_pydantic(
                    obj, target_class, exclude_fields
                )
                result.append(converted)
        
        return result


class DataNormalizer:
    """数据标准化工具类"""
    
    @staticmethod
    def normalize_phone(phone: str) -> str:
        """标准化手机号格式"""
        if not phone:
            return ""
        
        # 移除所有非数字字符
        phone = re.sub(r'\D', '', phone)
        
        # 中国手机号处理
        if phone.startswith('86') and len(phone) == 13:
            phone = phone[2:]
        elif phone.startswith('+86') and len(phone) == 14:
            phone = phone[3:]
        
        return phone
    
    @staticmethod
    def normalize_username(username: str) -> str:
        """标准化用户名"""
        if not username:
            return ""
        
        return username.strip().lower()
    
    @staticmethod
    def normalize_email(email: str) -> str:
        """标准化邮箱"""
        if not email:
            return ""
        
        return email.strip().lower()
    
    @staticmethod
    def normalize_text(text: str) -> str:
        """标准化文本"""
        if not text:
            return ""
        
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    @staticmethod
    def normalize_filename(filename: str) -> str:
        """标准化文件名"""
        if not filename:
            return ""
        
        # 移除危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            max_name_length = 255 - len(ext) - 1 if ext else 255
            filename = name[:max_name_length] + ('.' + ext if ext else '')
        
        return filename
