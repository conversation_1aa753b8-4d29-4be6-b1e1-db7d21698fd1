"""安全工具模块

提供密码哈希、验证、JWT令牌管理和其他安全相关功能。
使用bcrypt进行密码哈希，使用python-jose进行JWT处理。
"""

import secrets
import string
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
import re

from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi import HTTPException, status

from app.core.config import settings
from app.core.redis import redis_cache

# 获取日志器
logger = logging.getLogger(__name__)

# 密码上下文配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT算法
ALGORITHM = "HS256"


class PasswordValidator:
    """密码强度验证器"""
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """验证密码强度
        
        Args:
            password: 待验证的密码
            
        Returns:
            包含验证结果的字典
        """
        result = {
            "is_valid": True,
            "errors": [],
            "strength_score": 0,
            "suggestions": []
        }
        
        # 基本长度检查
        if len(password) < 8:
            result["is_valid"] = False
            result["errors"].append("密码长度至少8位")
        elif len(password) >= 8:
            result["strength_score"] += 1
            
        if len(password) >= 12:
            result["strength_score"] += 1
            
        # 包含小写字母
        if re.search(r'[a-z]', password):
            result["strength_score"] += 1
        else:
            result["errors"].append("密码应包含小写字母")
            result["suggestions"].append("添加小写字母")
            
        # 包含大写字母
        if re.search(r'[A-Z]', password):
            result["strength_score"] += 1
        else:
            result["suggestions"].append("添加大写字母")
            
        # 包含数字
        if re.search(r'\d', password):
            result["strength_score"] += 1
        else:
            result["errors"].append("密码应包含数字")
            result["suggestions"].append("添加数字")
            
        # 包含特殊字符
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result["strength_score"] += 1
        else:
            result["suggestions"].append("添加特殊字符")
            
        # 检查常见弱密码
        weak_patterns = [
            r'123456',
            r'password',
            r'qwerty',
            r'abc123',
            r'admin',
            r'root'
        ]
        
        for pattern in weak_patterns:
            if re.search(pattern, password.lower()):
                result["is_valid"] = False
                result["errors"].append("密码包含常见弱密码模式")
                result["strength_score"] = max(0, result["strength_score"] - 2)
                break
                
        # 检查重复字符
        if re.search(r'(.)\1{2,}', password):
            result["suggestions"].append("避免连续重复字符")
            result["strength_score"] = max(0, result["strength_score"] - 1)
            
        # 最终验证
        if result["strength_score"] < 3:
            result["is_valid"] = False
            
        return result


class PasswordManager:
    """密码管理器"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """哈希密码
        
        Args:
            password: 明文密码
            
        Returns:
            哈希后的密码
        """
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码
        
        Args:
            plain_password: 明文密码
            hashed_password: 哈希密码
            
        Returns:
            验证结果
        """
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码
        
        Args:
            length: 密码长度
            
        Returns:
            随机密码
        """
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))


class TokenManager:
    """JWT令牌管理器"""
    
    @staticmethod
    def create_access_token(
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌
        
        Args:
            data: 要编码的数据
            expires_delta: 过期时间增量
            
        Returns:
            JWT令牌
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.jwt_access_token_expire_minutes)
            
        to_encode.update({"exp": expire, "type": "access"})
        
        return jwt.encode(to_encode, settings.jwt_secret_key, algorithm=ALGORITHM)
    
    @staticmethod
    def create_refresh_token(
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建刷新令牌
        
        Args:
            data: 要编码的数据
            expires_delta: 过期时间增量
            
        Returns:
            JWT刷新令牌
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=settings.jwt_refresh_token_expire_days)
            
        to_encode.update({"exp": expire, "type": "refresh"})
        
        return jwt.encode(to_encode, settings.jwt_secret_key, algorithm=ALGORITHM)
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
        """验证令牌
        
        Args:
            token: JWT令牌
            token_type: 令牌类型 (access/refresh)
            
        Returns:
            解码后的数据
            
        Raises:
            HTTPException: 令牌无效时抛出异常
        """
        try:
            payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[ALGORITHM])
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"无效的令牌类型，期望: {token_type}"
                )
                
            return payload
            
        except JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效或已过期"
            ) from e
    
    @staticmethod
    def get_user_id_from_token(token: str) -> int:
        """从令牌中获取用户ID
        
        Args:
            token: JWT令牌
            
        Returns:
            用户ID
        """
        payload = TokenManager.verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌中缺少用户信息"
            )
            
        try:
            return int(user_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌中用户ID格式无效"
            )

    @staticmethod
    async def blacklist_token(token: str, expire_time: Optional[int] = None) -> bool:
        """将令牌加入黑名单

        Args:
            token: 要加入黑名单的令牌
            expire_time: 黑名单过期时间（秒），默认为令牌剩余有效期

        Returns:
            操作是否成功
        """
        try:
            # 解码令牌获取过期时间
            payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[ALGORITHM])
            exp = payload.get("exp")

            if exp:
                # 计算令牌剩余有效期
                current_time = datetime.utcnow().timestamp()
                remaining_time = int(exp - current_time)

                if remaining_time > 0:
                    # 使用令牌的JTI或整个令牌作为键
                    jti = payload.get("jti", token)
                    blacklist_key = f"blacklist:token:{jti}"

                    # 设置黑名单，过期时间为令牌剩余有效期
                    expire_time = expire_time or remaining_time
                    return await redis_cache.set(blacklist_key, "1", expire=expire_time)

            return False

        except JWTError:
            # 令牌无效，无需加入黑名单
            return False
        except Exception as e:
            logger.error(f"令牌黑名单操作失败: {e}")
            return False

    @staticmethod
    async def is_token_blacklisted(token: str) -> bool:
        """检查令牌是否在黑名单中

        Args:
            token: 要检查的令牌

        Returns:
            是否在黑名单中
        """
        try:
            # 解码令牌获取JTI
            payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[ALGORITHM])
            jti = payload.get("jti", token)
            blacklist_key = f"blacklist:token:{jti}"

            # 检查黑名单
            result = await redis_cache.get(blacklist_key)
            return result is not None

        except JWTError:
            # 令牌无效，视为在黑名单中
            return True
        except Exception as e:
            logger.error(f"检查令牌黑名单失败: {e}")
            # 出错时为安全起见，视为在黑名单中
            return True

    @staticmethod
    async def verify_token_with_blacklist(token: str, token_type: str = "access") -> Dict[str, Any]:
        """验证令牌并检查黑名单

        Args:
            token: JWT令牌
            token_type: 令牌类型 (access/refresh)

        Returns:
            解码后的数据

        Raises:
            HTTPException: 令牌无效或在黑名单中时抛出异常
        """
        # 首先验证令牌格式和签名
        payload = TokenManager.verify_token(token, token_type)

        # 检查是否在黑名单中
        if await TokenManager.is_token_blacklisted(token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已失效"
            )

        return payload

    @staticmethod
    async def refresh_access_token(refresh_token: str) -> Dict[str, str]:
        """使用刷新令牌获取新的访问令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            包含新访问令牌的字典

        Raises:
            HTTPException: 刷新令牌无效时抛出异常
        """
        # 验证刷新令牌
        payload = await TokenManager.verify_token_with_blacklist(refresh_token, "refresh")

        # 创建新的访问令牌
        user_data = {
            "sub": payload["sub"],
            "username": payload.get("username")
        }

        new_access_token = TokenManager.create_access_token(user_data)

        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }

    @staticmethod
    async def logout_user(access_token: str, refresh_token: Optional[str] = None) -> bool:
        """用户登出，将令牌加入黑名单

        Args:
            access_token: 访问令牌
            refresh_token: 刷新令牌（可选）

        Returns:
            操作是否成功
        """
        success = True

        # 将访问令牌加入黑名单
        if not await TokenManager.blacklist_token(access_token):
            success = False

        # 将刷新令牌加入黑名单
        if refresh_token and not await TokenManager.blacklist_token(refresh_token):
            success = False

        return success


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全令牌
        
        Args:
            length: 令牌长度
            
        Returns:
            安全令牌
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_verification_code(length: int = 6) -> str:
        """生成验证码
        
        Args:
            length: 验证码长度
            
        Returns:
            数字验证码
        """
        return ''.join(secrets.choice(string.digits) for _ in range(length))
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """清理输入文本

        Args:
            text: 输入文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        import html
        import re

        # HTML实体编码
        cleaned_text = html.escape(text)

        # 移除危险的协议
        dangerous_protocols = ['javascript:', 'data:', 'vbscript:', 'file:']
        for protocol in dangerous_protocols:
            cleaned_text = re.sub(f'{protocol}.*?(?=\\s|$)', '', cleaned_text, flags=re.IGNORECASE)

        # 移除事件处理器
        event_handlers = [
            'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
            'onfocus', 'onblur', 'onchange', 'onsubmit', 'onkeydown',
            'onkeyup', 'onkeypress'
        ]
        for handler in event_handlers:
            cleaned_text = re.sub(f'{handler}\\s*=.*?(?=\\s|>|$)', '', cleaned_text, flags=re.IGNORECASE)

        return cleaned_text.strip()
    
    @staticmethod
    def is_safe_redirect_url(url: str, allowed_hosts: list = None) -> bool:
        """检查重定向URL是否安全
        
        Args:
            url: 重定向URL
            allowed_hosts: 允许的主机列表
            
        Returns:
            是否安全
        """
        if not url:
            return False
            
        # 检查是否为相对URL
        if url.startswith('/') and not url.startswith('//'):
            return True
            
        # 检查是否在允许的主机列表中
        if allowed_hosts:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc in allowed_hosts
            
        return False


# 便捷函数
def hash_password(password: str) -> str:
    """哈希密码"""
    return PasswordManager.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return PasswordManager.verify_password(plain_password, hashed_password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    return TokenManager.create_access_token(data, expires_delta)


def create_refresh_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建刷新令牌"""
    return TokenManager.create_refresh_token(data, expires_delta)


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """验证令牌"""
    return TokenManager.verify_token(token, token_type)
