"""文件处理工具

提供文件上传、验证、存储和管理功能
"""

import os
import uuid
import hashlib
import mimetypes
from datetime import datetime
from typing import Optional, Tuple, List
from pathlib import Path
from PIL import Image
import aiofiles

from app.core.config import get_settings
from app.utils.exceptions import ValidationError
from app.utils.logging import get_logger

logger = get_logger(__name__)
settings = get_settings()


class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        self.upload_dir = Path(settings.upload_dir)
        self.max_file_size = settings.max_file_size
        self.allowed_extensions = settings.allowed_extensions
        
        # 确保上传目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.avatar_dir = self.upload_dir / "avatars"
        self.chat_files_dir = self.upload_dir / "chat_files"
        self.documents_dir = self.upload_dir / "documents"
        self.images_dir = self.upload_dir / "images"
        self.thumbnails_dir = self.upload_dir / "thumbnails"
        
        for directory in [self.avatar_dir, self.chat_files_dir, self.documents_dir, 
                         self.images_dir, self.thumbnails_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def validate_file(self, filename: str, file_size: int, mime_type: str) -> None:
        """验证文件
        
        Args:
            filename: 文件名
            file_size: 文件大小
            mime_type: MIME类型
            
        Raises:
            ValidationError: 文件验证失败
        """
        # 检查文件扩展名
        extension = os.path.splitext(filename)[1].lower().lstrip('.')
        if extension not in [ext.lower() for ext in self.allowed_extensions]:
            raise ValidationError(
                f"不支持的文件类型: {extension}。支持的类型: {', '.join(self.allowed_extensions)}",
                field="file"
            )
        
        # 检查文件大小
        if file_size > self.max_file_size:
            max_size_mb = self.max_file_size / (1024 * 1024)
            raise ValidationError(
                f"文件大小超过限制。最大允许: {max_size_mb:.1f}MB",
                field="file"
            )
        
        # 检查MIME类型
        expected_mime = mimetypes.guess_type(filename)[0]
        if expected_mime and not mime_type.startswith(expected_mime.split('/')[0]):
            logger.warning(f"MIME类型不匹配: 期望 {expected_mime}, 实际 {mime_type}")
    
    def generate_unique_filename(self, original_filename: str, purpose: str) -> str:
        """生成唯一文件名
        
        Args:
            original_filename: 原始文件名
            purpose: 上传目的
            
        Returns:
            唯一文件名
        """
        # 获取文件扩展名
        extension = os.path.splitext(original_filename)[1].lower()
        
        # 生成唯一标识符
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        # 构建文件名
        return f"{purpose}_{timestamp}_{unique_id}{extension}"
    
    def get_storage_path(self, filename: str, purpose: str) -> Path:
        """获取存储路径
        
        Args:
            filename: 文件名
            purpose: 上传目的
            
        Returns:
            存储路径
        """
        if purpose == "avatar":
            return self.avatar_dir / filename
        elif purpose == "chat_file":
            return self.chat_files_dir / filename
        elif purpose in ["document", "attachment"]:
            return self.documents_dir / filename
        else:
            return self.upload_dir / filename
    
    def get_file_url(self, file_path: str) -> str:
        """获取文件访问URL
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件URL
        """
        # 将绝对路径转换为相对路径
        relative_path = os.path.relpath(file_path, self.upload_dir)
        return f"/static/uploads/{relative_path.replace(os.sep, '/')}"
    
    async def save_file(self, file_content: bytes, filename: str, purpose: str) -> Tuple[str, str]:
        """保存文件
        
        Args:
            file_content: 文件内容
            filename: 文件名
            purpose: 上传目的
            
        Returns:
            (文件路径, 文件URL)
        """
        # 生成唯一文件名
        unique_filename = self.generate_unique_filename(filename, purpose)
        
        # 获取存储路径
        file_path = self.get_storage_path(unique_filename, purpose)
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        # 生成URL
        file_url = self.get_file_url(str(file_path))
        
        logger.info(f"文件保存成功: {filename} -> {file_path}")
        return str(file_path), file_url
    
    async def create_thumbnail(self, image_path: str, max_size: Tuple[int, int] = (200, 200)) -> Optional[str]:
        """创建图片缩略图
        
        Args:
            image_path: 图片路径
            max_size: 最大尺寸
            
        Returns:
            缩略图路径
        """
        try:
            # 检查是否为图片文件
            if not self.is_image_file(image_path):
                return None
            
            # 生成缩略图路径
            path_obj = Path(image_path)
            thumbnail_filename = f"{path_obj.stem}_thumb{path_obj.suffix}"
            thumbnail_path = self.thumbnails_dir / thumbnail_filename
            
            # 创建缩略图
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 创建缩略图
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, optimize=True, quality=85)
            
            logger.info(f"缩略图创建成功: {thumbnail_path}")
            return str(thumbnail_path)
            
        except Exception as e:
            logger.error(f"创建缩略图失败: {e}")
            return None
    
    def is_image_file(self, file_path: str) -> bool:
        """检查是否为图片文件"""
        try:
            with Image.open(file_path):
                return True
        except Exception:
            return False
    
    def get_file_hash(self, file_content: bytes) -> str:
        """计算文件哈希值"""
        return hashlib.sha256(file_content).hexdigest()
    
    async def delete_file(self, file_path: str) -> bool:
        """删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件删除成功: {file_path}")
                
                # 同时删除缩略图（如果存在）
                thumbnail_path = self.get_thumbnail_path(file_path)
                if thumbnail_path and os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
                    logger.info(f"缩略图删除成功: {thumbnail_path}")
                
                return True
            return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False
    
    def get_thumbnail_path(self, image_path: str) -> Optional[str]:
        """获取缩略图路径"""
        if not self.is_image_file(image_path):
            return None
        
        path_obj = Path(image_path)
        thumbnail_filename = f"{path_obj.stem}_thumb{path_obj.suffix}"
        return str(self.thumbnails_dir / thumbnail_filename)
    
    def get_file_info(self, file_path: str) -> dict:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime),
                "modified_at": datetime.fromtimestamp(stat.st_mtime),
                "exists": True
            }
        except Exception:
            return {"exists": False}


# 全局文件处理器实例
file_handler = FileHandler()
