"""示例API端点

展示完整的数据验证和序列化系统使用方法
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel, Field

from app.core.database import get_db_session
from app.models.user import User
from app.schemas.user import User as UserSchema, UserRegister
from app.utils.decorators import (
    validate_input, format_response, serialize_output, 
    handle_database_errors, log_execution, rate_limit
)
from app.utils.response_handler import (
    APIResponseHandler, ModelResponseHandler, success_response, error_response
)
from app.core.validation_config import ValidationLimits
from app.middleware.auth import get_current_active_user

router = APIRouter()


class ExampleRequest(BaseModel):
    """示例请求schema"""
    name: str = Field(..., min_length=1, max_length=100, description="名称")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    tags: List[str] = Field(default_factory=list, description="标签列表")


class ExampleResponse(BaseModel):
    """示例响应schema"""
    id: int = Field(..., description="ID")
    name: str = Field(..., description="名称")
    description: Optional[str] = Field(None, description="描述")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    created_at: str = Field(..., description="创建时间")


@router.get("/users", summary="获取用户列表")
@handle_database_errors
@log_execution("info")
@rate_limit(max_requests=100, window_seconds=60)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    per_page: int = Query(
        ValidationLimits.DEFAULT_PAGE_SIZE, 
        ge=1, 
        le=ValidationLimits.MAX_PAGE_SIZE,
        description="每页数量"
    ),
    search: Optional[str] = Query(None, max_length=100, description="搜索关键词"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户列表（带分页和搜索）
    
    展示完整的查询、分页、序列化流程
    """
    try:
        # 构建查询
        query = select(User).where(User.is_active == True)
        
        # 添加搜索条件
        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (User.username.ilike(search_pattern)) |
                (User.nickname.ilike(search_pattern))
            )
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
        
        result = await db.execute(query)
        users = result.scalars().all()
        
        # 使用ModelResponseHandler返回分页响应
        return ModelResponseHandler.model_list(
            models=users,
            schema_class=UserSchema,
            page=page,
            per_page=per_page,
            total=total,
            message="用户列表获取成功"
        )
        
    except Exception as e:
        return error_response(
            message="获取用户列表失败",
            error_code="USER_LIST_ERROR"
        )


@router.get("/users/{user_id}", summary="获取用户详情")
@handle_database_errors
@log_execution("info")
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户详情
    
    展示单个模型的序列化和响应处理
    """
    try:
        # 查询用户
        query = select(User).where(User.id == user_id, User.is_active == True)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        # 使用ModelResponseHandler返回单个模型响应
        return ModelResponseHandler.single_model(
            model=user,
            schema_class=UserSchema,
            message="用户详情获取成功"
        )
        
    except Exception as e:
        return error_response(
            message="获取用户详情失败",
            error_code="USER_DETAIL_ERROR"
        )


@router.post("/example", summary="示例创建端点")
@validate_input(ExampleRequest)
@handle_database_errors
@format_response("示例数据创建成功")
@log_execution("info")
async def create_example(
    request_data: ExampleRequest,
    current_user: User = Depends(get_current_active_user)
):
    """创建示例数据
    
    展示输入验证、响应格式化的完整流程
    """
    try:
        # 模拟创建逻辑
        example_data = {
            "id": 1,
            "name": request_data.name,
            "description": request_data.description,
            "tags": request_data.tags,
            "created_at": "2024-01-01T00:00:00Z"
        }
        
        # 返回创建的数据
        return example_data
        
    except Exception as e:
        return error_response(
            message="创建示例数据失败",
            error_code="EXAMPLE_CREATE_ERROR"
        )


@router.put("/example/{example_id}", summary="示例更新端点")
@validate_input(ExampleRequest)
@serialize_output(ExampleResponse)
@handle_database_errors
@format_response("示例数据更新成功")
@log_execution("info")
async def update_example(
    example_id: int,
    request_data: ExampleRequest,
    current_user: User = Depends(get_current_active_user)
):
    """更新示例数据
    
    展示输入验证、输出序列化、响应格式化的完整流程
    """
    try:
        # 模拟更新逻辑
        updated_data = {
            "id": example_id,
            "name": request_data.name,
            "description": request_data.description,
            "tags": request_data.tags,
            "created_at": "2024-01-01T00:00:00Z"
        }
        
        # 返回更新的数据（会被serialize_output装饰器处理）
        return updated_data
        
    except Exception as e:
        return error_response(
            message="更新示例数据失败",
            error_code="EXAMPLE_UPDATE_ERROR"
        )


@router.delete("/example/{example_id}", summary="示例删除端点")
@handle_database_errors
@format_response("示例数据删除成功")
@log_execution("info")
async def delete_example(
    example_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """删除示例数据
    
    展示删除操作的响应处理
    """
    try:
        # 模拟删除逻辑
        if example_id <= 0:
            return error_response(
                message="无效的ID",
                error_code="INVALID_ID"
            )
        
        # 返回None表示删除成功（会被format_response装饰器处理）
        return None
        
    except Exception as e:
        return error_response(
            message="删除示例数据失败",
            error_code="EXAMPLE_DELETE_ERROR"
        )


@router.get("/validation-demo", summary="验证演示端点")
async def validation_demo():
    """验证系统演示
    
    展示各种响应类型的使用方法
    """
    try:
        demo_data = {
            "validation_features": [
                "输入数据验证",
                "输出数据序列化", 
                "响应格式标准化",
                "错误处理统一化",
                "安全检查集成"
            ],
            "response_types": [
                "成功响应",
                "错误响应",
                "分页响应",
                "验证错误响应"
            ],
            "decorators": [
                "@validate_input - 输入验证",
                "@serialize_output - 输出序列化",
                "@format_response - 响应格式化",
                "@handle_database_errors - 数据库错误处理",
                "@log_execution - 执行日志",
                "@rate_limit - 限流控制"
            ]
        }
        
        return success_response(
            data=demo_data,
            message="验证系统演示数据"
        )
        
    except Exception as e:
        return error_response(
            message="获取演示数据失败",
            error_code="DEMO_ERROR"
        )


@router.get("/health", summary="健康检查")
async def health_check():
    """健康检查端点
    
    简单的健康检查，展示最基本的响应处理
    """
    return APIResponseHandler.success(
        data={
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "validation_system": "active",
            "serialization_system": "active"
        },
        message="系统运行正常"
    )
