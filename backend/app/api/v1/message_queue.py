"""消息队列管理API

提供消息队列状态查询、离线消息管理等功能
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from app.middleware.auth import get_current_user
from app.models.user import User
from app.schemas.response import APIResponse
from app.websocket.redis_message_queue import (
    redis_message_queue_service, 
    QueuePriority, 
    MessageQueueStatus
)
from app.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


class OfflineMessageSummary(BaseModel):
    """离线消息摘要"""
    user_id: int
    message_count: int
    last_message_time: Optional[str] = None
    oldest_message_time: Optional[str] = None


class QueueStatusResponse(BaseModel):
    """队列状态响应"""
    queue_name: str
    priority: str
    message_count: int
    last_processed: Optional[str] = None


class MessageQueueStats(BaseModel):
    """消息队列统计"""
    total_queued_messages: int
    total_offline_messages: int
    queue_status: List[QueueStatusResponse]
    processing_status: str


@router.get("/offline/summary", response_model=APIResponse)
async def get_offline_message_summary(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的离线消息摘要
    
    Returns:
        离线消息摘要信息
    """
    try:
        # 获取离线消息（不删除）
        offline_messages = await redis_message_queue_service.get_offline_messages(current_user.id)
        
        if not offline_messages:
            return APIResponse(
                success=True,
                message="无离线消息",
                data={
                    "user_id": current_user.id,
                    "message_count": 0,
                    "last_message_time": None,
                    "oldest_message_time": None
                }
            )
        
        # 分析消息时间
        message_times = []
        for msg in offline_messages:
            timestamp = msg.get("timestamp")
            if timestamp:
                message_times.append(timestamp)
        
        message_times.sort()
        
        summary = OfflineMessageSummary(
            user_id=current_user.id,
            message_count=len(offline_messages),
            last_message_time=message_times[-1] if message_times else None,
            oldest_message_time=message_times[0] if message_times else None
        )
        
        return APIResponse(
            success=True,
            message="离线消息摘要获取成功",
            data=summary.model_dump()
        )
        
    except Exception as e:
        logger.error(f"获取离线消息摘要失败: {e}")
        raise HTTPException(status_code=500, detail="获取离线消息摘要失败")


@router.post("/offline/sync", response_model=APIResponse)
async def sync_offline_messages(
    current_user: User = Depends(get_current_user)
):
    """手动同步离线消息
    
    Returns:
        同步结果
    """
    try:
        result = await redis_message_queue_service.sync_offline_messages_on_connect(current_user.id)
        
        return APIResponse(
            success=True,
            message="离线消息同步完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"手动同步离线消息失败: {e}")
        raise HTTPException(status_code=500, detail="离线消息同步失败")


@router.delete("/offline/clear", response_model=APIResponse)
async def clear_offline_messages(
    current_user: User = Depends(get_current_user)
):
    """清空当前用户的离线消息
    
    Returns:
        清理结果
    """
    try:
        # 获取消息数量
        offline_messages = await redis_message_queue_service.get_offline_messages(current_user.id)
        message_count = len(offline_messages)
        
        return APIResponse(
            success=True,
            message=f"已清空 {message_count} 条离线消息",
            data={
                "user_id": current_user.id,
                "cleared_count": message_count,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"清空离线消息失败: {e}")
        raise HTTPException(status_code=500, detail="清空离线消息失败")


@router.get("/stats", response_model=APIResponse)
async def get_queue_stats(
    current_user: User = Depends(get_current_user)
):
    """获取消息队列统计信息（管理员功能）
    
    Returns:
        队列统计信息
    """
    try:
        # 这里简化处理，实际应该检查管理员权限
        if not current_user.is_active:
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 获取队列统计（简化实现）
        stats = MessageQueueStats(
            total_queued_messages=0,
            total_offline_messages=0,
            queue_status=[],
            processing_status="running"
        )
        
        return APIResponse(
            success=True,
            message="队列统计获取成功",
            data=stats.model_dump()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取队列统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取队列统计失败")


@router.post("/enqueue", response_model=APIResponse)
async def enqueue_message(
    user_id: int,
    message_data: Dict[str, Any],
    priority: QueuePriority = QueuePriority.NORMAL,
    delay_seconds: int = 0,
    current_user: User = Depends(get_current_user)
):
    """手动将消息加入队列（测试功能）
    
    Args:
        user_id: 目标用户ID
        message_data: 消息数据
        priority: 消息优先级
        delay_seconds: 延迟秒数
        
    Returns:
        入队结果
    """
    try:
        # 这里简化处理，实际应该检查权限
        if not current_user.is_active:
            raise HTTPException(status_code=403, detail="权限不足")
        
        message_id = await redis_message_queue_service.enqueue_message(
            user_id, message_data, priority, delay_seconds
        )
        
        if message_id:
            return APIResponse(
                success=True,
                message="消息已加入队列",
                data={
                    "message_id": message_id,
                    "user_id": user_id,
                    "priority": priority.value,
                    "delay_seconds": delay_seconds
                }
            )
        else:
            raise HTTPException(status_code=500, detail="消息入队失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"消息入队失败: {e}")
        raise HTTPException(status_code=500, detail="消息入队失败")


# 导入datetime
from datetime import datetime
