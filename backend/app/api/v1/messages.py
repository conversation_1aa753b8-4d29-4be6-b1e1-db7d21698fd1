"""消息处理API

提供消息发送、历史查询、状态管理和消息撤回功能
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.core.database import get_db_session
from app.middleware.auth import get_current_user
from app.models.user import User
from app.models.chat import Chat
from app.models.chat_member import ChatMember
from app.models.message import Message
from app.models.message_status import MessageStatus
from app.schemas.message import (
    MessageCreate, MessageUpdate, Message as MessageSchema, MessageWithSender,
    MessageList, MessageSearch, MessageSearchResult, MessageType
)
from app.schemas.message_status import (
    MessageStatusUpdate, MessageStatusSummary, MessageStatusList,
    MessageReadReceipt, BulkMessageStatusUpdate
)
from app.schemas.response import APIResponse
from app.utils.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.utils.logging import get_logger
from app.websocket.message_status_sync import message_status_sync_service

logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=MessageWithSender, status_code=status.HTTP_201_CREATED)
async def send_message(
    message_data: MessageCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> MessageWithSender:
    """发送消息
    
    Args:
        message_data: 消息创建数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        发送的消息信息
        
    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限发送消息
        ValidationError: 数据验证失败
    """
    try:
        # 验证聊天存在且用户有权限
        chat_query = select(Chat).where(
            and_(
                Chat.id == message_data.chat_id,
                Chat.is_active == True
            )
        )
        
        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()
        
        if not chat:
            raise NotFoundError("聊天不存在")
        
        # 验证用户是否为聊天成员
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == message_data.chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )
        
        member_result = await db.execute(member_query)
        member = member_result.scalar_one_or_none()
        
        if not member:
            raise AuthorizationError("只有聊天成员可以发送消息")
        
        # 验证回复消息（如果有）
        reply_to_message = None
        if message_data.reply_to_id:
            reply_query = select(Message).where(
                and_(
                    Message.id == message_data.reply_to_id,
                    Message.chat_id == message_data.chat_id,
                    Message.is_deleted == False
                )
            )
            
            reply_result = await db.execute(reply_query)
            reply_to_message = reply_result.scalar_one_or_none()
            
            if not reply_to_message:
                raise ValidationError("回复的消息不存在", field="reply_to_id")
        
        # 创建消息
        new_message = Message(
            chat_id=message_data.chat_id,
            sender_id=current_user.id,
            content=message_data.content,
            message_type=message_data.type.value,
            reply_to_id=message_data.reply_to_id
        )
        
        db.add(new_message)
        await db.flush()  # 获取消息ID
        
        # 获取聊天所有活跃成员
        members_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == message_data.chat_id,
                ChatMember.is_active == True
            )
        )
        
        members_result = await db.execute(members_query)
        chat_members = members_result.scalars().all()
        
        # 为所有成员创建消息状态（发送者标记为已读，其他人标记为已发送）
        message_statuses = []
        for chat_member in chat_members:
            if chat_member.user_id == current_user.id:
                # 发送者自动标记为已读
                status_obj = MessageStatus.create_for_message(
                    new_message.id, chat_member.user_id, "read"
                )
            else:
                # 其他成员标记为已发送
                status_obj = MessageStatus.create_for_message(
                    new_message.id, chat_member.user_id, "sent"
                )
            message_statuses.append(status_obj)
        
        db.add_all(message_statuses)
        
        # 更新聊天的最后消息时间
        chat.updated_at = datetime.utcnow()
        
        await db.commit()
        
        # 重新查询消息以获取完整信息
        message_query = (
            select(Message)
            .options(
                selectinload(Message.sender),
                selectinload(Message.reply_to).selectinload(Message.sender),
                selectinload(Message.statuses)
            )
            .where(Message.id == new_message.id)
        )
        
        message_result = await db.execute(message_query)
        message_with_relations = message_result.scalar_one()
        
        # 构建响应
        response = MessageWithSender(
            id=message_with_relations.id,
            chat_id=message_with_relations.chat_id,
            sender_id=message_with_relations.sender_id,
            content=message_with_relations.content,
            type=MessageType(message_with_relations.message_type),
            reply_to_id=message_with_relations.reply_to_id,
            is_deleted=message_with_relations.is_deleted,
            created_at=message_with_relations.created_at,
            updated_at=message_with_relations.updated_at,
            sender=message_with_relations.sender,
            reply_to=message_with_relations.reply_to,
            read_count=message_with_relations.get_read_count(),
            delivered_count=message_with_relations.get_delivered_count()
        )
        
        logger.info(f"用户 {current_user.id} 在聊天 {message_data.chat_id} 中发送了消息 {new_message.id}")
        return response

    except Exception as e:
        await db.rollback()
        logger.error(f"发送消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送消息失败，请稍后重试"
        )


@router.get("/{chat_id}", response_model=MessageList)
async def get_chat_messages(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    limit: int = Query(50, ge=1, le=100, description="每页消息数量"),
    cursor: Optional[str] = Query(None, description="分页游标"),
    before_message_id: Optional[int] = Query(None, description="获取指定消息之前的消息")
) -> MessageList:
    """获取聊天消息历史

    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话
        limit: 每页消息数量
        cursor: 分页游标
        before_message_id: 获取指定消息之前的消息

    Returns:
        消息列表

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限访问
    """
    try:
        # 验证用户是否为聊天成员
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True,
                ChatMember.chat_visible == True
            )
        )

        member_result = await db.execute(member_query)
        member = member_result.scalar_one_or_none()

        if not member:
            raise AuthorizationError("无权限访问该聊天")

        # 构建查询条件
        query_conditions = [
            Message.chat_id == chat_id,
            Message.is_deleted == False
        ]

        # 处理分页
        if before_message_id:
            query_conditions.append(Message.id < before_message_id)
        elif cursor:
            try:
                cursor_id = int(cursor)
                query_conditions.append(Message.id < cursor_id)
            except ValueError:
                raise ValidationError("无效的分页游标", field="cursor")

        # 查询消息
        messages_query = (
            select(Message)
            .options(
                selectinload(Message.sender),
                selectinload(Message.reply_to).selectinload(Message.sender),
                selectinload(Message.statuses)
            )
            .where(and_(*query_conditions))
            .order_by(desc(Message.id))
            .limit(limit + 1)  # 多查询一条用于判断是否还有更多
        )

        messages_result = await db.execute(messages_query)
        messages = messages_result.scalars().all()

        # 判断是否还有更多消息
        has_more = len(messages) > limit
        if has_more:
            messages = messages[:-1]  # 移除多查询的那一条

        # 计算下一页游标
        next_cursor = None
        if has_more and messages:
            next_cursor = str(messages[-1].id)

        # 构建响应
        message_list = []
        for msg in messages:
            message_item = MessageWithSender(
                id=msg.id,
                chat_id=msg.chat_id,
                sender_id=msg.sender_id,
                content=msg.content,
                type=MessageType(msg.message_type),
                reply_to_id=msg.reply_to_id,
                is_deleted=msg.is_deleted,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
                sender=msg.sender,
                reply_to=msg.reply_to,
                read_count=msg.get_read_count(),
                delivered_count=msg.get_delivered_count()
            )
            message_list.append(message_item)

        return MessageList(
            messages=message_list,
            total=len(message_list),
            has_more=has_more,
            next_cursor=next_cursor
        )

    except Exception as e:
        logger.error(f"获取聊天消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息失败，请稍后重试"
        )


@router.put("/{message_id}/status", response_model=APIResponse)
async def update_message_status(
    message_id: int,
    status_data: MessageStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """更新消息状态（已送达/已读）

    Args:
        message_id: 消息ID
        status_data: 状态更新数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新结果

    Raises:
        NotFoundError: 消息不存在
        AuthorizationError: 无权限操作
    """
    try:
        # 查询消息并验证权限
        message_query = (
            select(Message)
            .options(selectinload(Message.chat))
            .where(
                and_(
                    Message.id == message_id,
                    Message.is_deleted == False
                )
            )
        )

        message_result = await db.execute(message_query)
        message = message_result.scalar_one_or_none()

        if not message:
            raise NotFoundError("消息不存在")

        # 验证用户是否为聊天成员
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == message.chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )

        member_result = await db.execute(member_query)
        if not member_result.scalar_one_or_none():
            raise AuthorizationError("无权限访问该消息")

        # 查询或创建消息状态
        status_query = select(MessageStatus).where(
            and_(
                MessageStatus.message_id == message_id,
                MessageStatus.user_id == current_user.id
            )
        )

        status_result = await db.execute(status_query)
        message_status = status_result.scalar_one_or_none()

        if not message_status:
            # 创建新的消息状态
            message_status = MessageStatus.create_for_message(
                message_id, current_user.id, status_data.status.value
            )
            db.add(message_status)
        else:
            # 更新现有状态
            if status_data.status.value == "delivered":
                message_status.mark_as_delivered(status_data.timestamp)
            elif status_data.status.value == "read":
                message_status.mark_as_read(status_data.timestamp)
            else:
                message_status.status = status_data.status.value
                message_status.timestamp = status_data.timestamp or datetime.utcnow()

        await db.commit()

        # 使用消息状态同步服务进行实时推送
        if status_data.status.value == "read":
            await message_status_sync_service.mark_message_as_read(
                current_user.id, message_id
            )
        elif status_data.status.value == "delivered":
            await message_status_sync_service.mark_messages_as_delivered(
                current_user.id, [message_id]
            )

        logger.info(f"用户 {current_user.id} 更新消息 {message_id} 状态为 {status_data.status.value}")
        return APIResponse(
            success=True,
            message="消息状态更新成功",
            data={"message_id": message_id, "status": status_data.status.value}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"更新消息状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新消息状态失败，请稍后重试"
        )


@router.put("/status/bulk", response_model=APIResponse)
async def bulk_update_message_status(
    status_data: BulkMessageStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """批量更新消息状态

    Args:
        status_data: 批量状态更新数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新结果
    """
    try:
        updated_count = 0

        for message_id in status_data.message_ids:
            # 查询消息状态
            status_query = select(MessageStatus).where(
                and_(
                    MessageStatus.message_id == message_id,
                    MessageStatus.user_id == current_user.id
                )
            )

            status_result = await db.execute(status_query)
            message_status = status_result.scalar_one_or_none()

            if message_status:
                if status_data.status.value == "delivered":
                    message_status.mark_as_delivered()
                elif status_data.status.value == "read":
                    message_status.mark_as_read()
                updated_count += 1

        await db.commit()

        # 使用消息状态同步服务进行实时推送
        if status_data.status.value == "read":
            await message_status_sync_service.bulk_mark_as_read(
                current_user.id, status_data.message_ids
            )
        elif status_data.status.value == "delivered":
            await message_status_sync_service.mark_messages_as_delivered(
                current_user.id, status_data.message_ids
            )

        logger.info(f"用户 {current_user.id} 批量更新了 {updated_count} 条消息状态为 {status_data.status.value}")
        return APIResponse(
            success=True,
            message=f"成功更新 {updated_count} 条消息状态",
            data={"updated_count": updated_count}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"批量更新消息状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量更新消息状态失败，请稍后重试"
        )


@router.get("/{message_id}/status/summary", response_model=APIResponse)
async def get_message_status_summary(
    message_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """获取消息状态摘要

    Args:
        message_id: 消息ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        消息状态摘要
    """
    try:
        # 验证消息是否存在且用户有权限查看
        message_query = select(Message).where(
            and_(
                Message.id == message_id,
                Message.sender_id == current_user.id
            )
        )

        message_result = await db.execute(message_query)
        message = message_result.scalar_one_or_none()

        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在或无权限查看"
            )

        # 使用消息状态同步服务获取状态摘要
        summary = await message_status_sync_service.get_message_status_summary(
            message_id, current_user.id
        )

        return APIResponse(
            success=True,
            message="获取消息状态摘要成功",
            data=summary
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息状态摘要失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息状态摘要失败，请稍后重试"
        )


@router.delete("/{message_id}", response_model=APIResponse)
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """撤回/删除消息

    Args:
        message_id: 消息ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        删除结果

    Raises:
        NotFoundError: 消息不存在
        AuthorizationError: 无权限删除
        ValidationError: 消息不能删除
    """
    try:
        # 查询消息
        message_query = select(Message).where(
            and_(
                Message.id == message_id,
                Message.is_deleted == False
            )
        )

        message_result = await db.execute(message_query)
        message = message_result.scalar_one_or_none()

        if not message:
            raise NotFoundError("消息不存在")

        # 验证权限：只有发送者可以删除消息
        if message.sender_id != current_user.id:
            raise AuthorizationError("只能删除自己发送的消息")

        # 检查消息发送时间（可选：限制撤回时间）
        time_diff = datetime.utcnow() - message.created_at
        if time_diff.total_seconds() > 24 * 3600:  # 24小时后不能撤回
            raise ValidationError("消息发送超过24小时，无法撤回")

        # 软删除消息
        message.soft_delete()

        await db.commit()

        logger.info(f"用户 {current_user.id} 删除了消息 {message_id}")
        return APIResponse(
            success=True,
            message="消息删除成功",
            data={"message_id": message_id}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"删除消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除消息失败，请稍后重试"
        )


@router.get("/{message_id}/status", response_model=MessageStatusList)
async def get_message_status(
    message_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> MessageStatusList:
    """获取消息状态详情

    Args:
        message_id: 消息ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        消息状态详情

    Raises:
        NotFoundError: 消息不存在
        AuthorizationError: 无权限查看
    """
    try:
        # 查询消息并验证权限
        message_query = select(Message).where(
            and_(
                Message.id == message_id,
                Message.is_deleted == False
            )
        )

        message_result = await db.execute(message_query)
        message = message_result.scalar_one_or_none()

        if not message:
            raise NotFoundError("消息不存在")

        # 验证权限：只有发送者可以查看详细状态
        if message.sender_id != current_user.id:
            raise AuthorizationError("只能查看自己发送消息的状态")

        # 查询消息状态
        statuses_query = (
            select(MessageStatus)
            .options(selectinload(MessageStatus.user))
            .where(MessageStatus.message_id == message_id)
        )

        statuses_result = await db.execute(statuses_query)
        statuses = statuses_result.scalars().all()

        # 统计状态
        sent_count = sum(1 for s in statuses if s.status == "sent")
        delivered_count = sum(1 for s in statuses if s.status == "delivered")
        read_count = sum(1 for s in statuses if s.status == "read")

        summary = MessageStatusSummary(
            message_id=message_id,
            sent_count=sent_count,
            delivered_count=delivered_count,
            read_count=read_count,
            total_recipients=len(statuses)
        )

        return MessageStatusList(
            statuses=statuses,
            summary=summary
        )

    except Exception as e:
        logger.error(f"获取消息状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息状态失败，请稍后重试"
        )


@router.post("/search", response_model=MessageSearchResult)
async def search_messages(
    search_data: MessageSearch,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    limit: int = Query(20, ge=1, le=50, description="搜索结果数量")
) -> MessageSearchResult:
    """搜索消息

    Args:
        search_data: 搜索条件
        current_user: 当前用户
        db: 数据库会话
        limit: 搜索结果数量

    Returns:
        搜索结果

    Raises:
        AuthorizationError: 无权限搜索
    """
    try:
        # 构建基础查询条件
        query_conditions = [
            Message.is_deleted == False,
            Message.content.ilike(f"%{search_data.query}%")
        ]

        # 如果指定了聊天ID，验证用户权限
        if search_data.chat_id:
            member_query = select(ChatMember).where(
                and_(
                    ChatMember.chat_id == search_data.chat_id,
                    ChatMember.user_id == current_user.id,
                    ChatMember.is_active == True
                )
            )

            member_result = await db.execute(member_query)
            if not member_result.scalar_one_or_none():
                raise AuthorizationError("无权限搜索该聊天")

            query_conditions.append(Message.chat_id == search_data.chat_id)
        else:
            # 如果没有指定聊天ID，只搜索用户有权限的聊天
            user_chats_query = select(ChatMember.chat_id).where(
                and_(
                    ChatMember.user_id == current_user.id,
                    ChatMember.is_active == True
                )
            )

            user_chats_result = await db.execute(user_chats_query)
            user_chat_ids = [row[0] for row in user_chats_result.fetchall()]

            if user_chat_ids:
                query_conditions.append(Message.chat_id.in_(user_chat_ids))
            else:
                # 用户没有任何聊天权限
                return MessageSearchResult(
                    messages=[],
                    total=0,
                    query=search_data.query
                )

        # 添加其他搜索条件
        if search_data.sender_id:
            query_conditions.append(Message.sender_id == search_data.sender_id)

        if search_data.message_type:
            query_conditions.append(Message.message_type == search_data.message_type.value)

        if search_data.start_date:
            query_conditions.append(Message.created_at >= search_data.start_date)

        if search_data.end_date:
            query_conditions.append(Message.created_at <= search_data.end_date)

        # 执行搜索
        search_query = (
            select(Message)
            .options(
                selectinload(Message.sender),
                selectinload(Message.reply_to).selectinload(Message.sender),
                selectinload(Message.statuses)
            )
            .where(and_(*query_conditions))
            .order_by(desc(Message.created_at))
            .limit(limit)
        )

        search_result = await db.execute(search_query)
        messages = search_result.scalars().all()

        # 构建响应
        message_list = []
        for msg in messages:
            message_item = MessageWithSender(
                id=msg.id,
                chat_id=msg.chat_id,
                sender_id=msg.sender_id,
                content=msg.content,
                type=MessageType(msg.message_type),
                reply_to_id=msg.reply_to_id,
                is_deleted=msg.is_deleted,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
                sender=msg.sender,
                reply_to=msg.reply_to,
                read_count=msg.get_read_count(),
                delivered_count=msg.get_delivered_count()
            )
            message_list.append(message_item)

        logger.info(f"用户 {current_user.id} 搜索消息: '{search_data.query}', 找到 {len(message_list)} 条结果")
        return MessageSearchResult(
            messages=message_list,
            total=len(message_list),
            query=search_data.query
        )

    except Exception as e:
        logger.error(f"搜索消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索消息失败，请稍后重试"
        )
