"""认证API路由

提供用户注册、登录、登出、令牌刷新等认证相关的API端点。
"""

import logging
from datetime import timedelta
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db_session
from app.models.user import User
from app.schemas.auth import (
    UserRegisterRequest, UserRegisterResponse,
    UserLoginRequest, UserLoginResponse,
    TokenRefreshRequest, TokenRefreshResponse,
    UserProfileResponse, UserUpdateRequest,
    PasswordChangeRequest
)
from app.utils.security import (
    PasswordValidator, PasswordManager, TokenManager,
    create_access_token, create_refresh_token
)
from app.utils.exceptions import (
    ValidationError, AuthenticationError, ConflictError, BaseCustomException,
    raise_validation_error, raise_authentication_error
)
from app.middleware.auth import (
    get_current_active_user, register_rate_limiter, login_rate_limiter
)
from app.utils.logging import get_logger, security_logger
from app.utils.decorators import validate_input, format_response, serialize_output, handle_database_errors
from app.utils.response_handler import APIResponseHandler, ModelResponseHandler

logger = get_logger(__name__)
router = APIRouter()


@router.post("/register", response_model=UserRegisterResponse, status_code=status.HTTP_201_CREATED)
@handle_database_errors
async def register_user(
    request: Request,
    user_data: UserRegisterRequest,
    db: AsyncSession = Depends(get_db_session)
) -> UserRegisterResponse:
    """用户注册
    
    Args:
        request: 请求对象
        user_data: 用户注册数据
        db: 数据库会话
        
    Returns:
        注册响应
        
    Raises:
        ValidationError: 数据验证失败
        ConflictError: 用户名已存在
    """
    # 应用注册限流
    client_ip = request.client.host if request.client else "unknown"
    if not await register_rate_limiter.check_rate_limit(client_ip, request):
        from app.utils.exceptions import RateLimitError
        raise RateLimitError("注册过于频繁，请稍后再试", retry_after=3600)
    
    # 验证密码强度
    password_validation = PasswordValidator.validate_password_strength(user_data.password)
    if not password_validation["is_valid"]:
        raise_validation_error(
            message="密码强度不足",
            field="password",
            errors=password_validation["errors"],
            suggestions=password_validation["suggestions"]
        )
    
    # 检查用户名是否已存在
    existing_user_query = select(User).where(User.username == user_data.username)
    existing_user_result = await db.execute(existing_user_query)
    existing_user = existing_user_result.scalar_one_or_none()
    
    if existing_user:
        raise ConflictError("用户名已存在", resource="username")
    
    # 检查邮箱是否已存在（如果提供）
    if user_data.email:
        existing_email_query = select(User).where(User.email == user_data.email)
        existing_email_result = await db.execute(existing_email_query)
        existing_email = existing_email_result.scalar_one_or_none()
        
        if existing_email:
            raise ConflictError("邮箱已被使用", resource="email")
    
    try:
        # 创建新用户
        hashed_password = PasswordManager.hash_password(user_data.password)
        
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            nickname=user_data.nickname or user_data.username,
            is_active=True,
            is_superuser=False
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # 记录注册日志
        logger.info(
            f"用户注册成功: {new_user.username} (ID: {new_user.id})",
            extra={
                "user_id": new_user.id,
                "username": new_user.username,
                "ip_address": client_ip,
                "event_type": "user_register"
            }
        )
        
        # 创建访问令牌
        token_data = {"sub": str(new_user.id), "username": new_user.username}
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        return UserRegisterResponse(
            message="注册成功",
            user=UserProfileResponse(
                id=new_user.id,
                username=new_user.username,
                email=new_user.email,
                nickname=new_user.nickname,
                avatar_url=new_user.avatar_url,
                is_active=new_user.is_active,
                created_at=new_user.created_at,
                updated_at=new_user.updated_at
            ),
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"用户注册失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=UserLoginResponse)
async def login_user(
    request: Request,
    login_data: UserLoginRequest,
    db: AsyncSession = Depends(get_db_session)
) -> UserLoginResponse:
    """用户登录
    
    Args:
        request: 请求对象
        login_data: 登录数据
        db: 数据库会话
        
    Returns:
        登录响应
        
    Raises:
        AuthenticationError: 认证失败
    """
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")
    
    # 应用登录限流
    rate_limit_key = f"{login_data.username}:{client_ip}"
    if not await login_rate_limiter.check_rate_limit(rate_limit_key, request):
        security_logger.log_login_attempt(
            username=login_data.username,
            success=False,
            ip_address=client_ip,
            user_agent=user_agent,
            reason="登录过于频繁"
        )
        from app.utils.exceptions import RateLimitError
        raise RateLimitError("登录尝试过于频繁，请5分钟后再试", retry_after=300)
    
    try:
        # 查询用户
        user_query = select(User).where(User.username == login_data.username)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()
        
        # 验证用户和密码
        if not user or not PasswordManager.verify_password(login_data.password, user.hashed_password):
            security_logger.log_login_attempt(
                username=login_data.username,
                success=False,
                ip_address=client_ip,
                user_agent=user_agent,
                reason="用户名或密码错误"
            )
            raise_authentication_error("用户名或密码错误")
        
        # 检查用户状态
        if not user.is_active:
            security_logger.log_login_attempt(
                username=login_data.username,
                success=False,
                ip_address=client_ip,
                user_agent=user_agent,
                reason="账户已被禁用"
            )
            raise_authentication_error("账户已被禁用")
        
        # 更新最后登录时间
        from datetime import datetime
        user.last_login_at = datetime.utcnow()
        await db.commit()
        
        # 创建令牌
        token_data = {"sub": str(user.id), "username": user.username}
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        # 记录成功登录
        security_logger.log_login_attempt(
            username=login_data.username,
            success=True,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        logger.info(
            f"用户登录成功: {user.username} (ID: {user.id})",
            extra={
                "user_id": user.id,
                "username": user.username,
                "ip_address": client_ip,
                "user_agent": user_agent,
                "event_type": "user_login"
            }
        )
        
        return UserLoginResponse(
            message="登录成功",
            user=UserProfileResponse(
                id=user.id,
                username=user.username,
                email=user.email,
                nickname=user.nickname,
                avatar_url=user.avatar_url,
                is_active=user.is_active,
                created_at=user.created_at,
                updated_at=user.updated_at
            ),
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
        
    except HTTPException:
        raise
    except BaseCustomException:
        # 重新抛出自定义异常（包括AuthenticationError等）
        raise
    except Exception as e:
        logger.error(f"登录处理失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=TokenRefreshResponse)
async def refresh_token(
    refresh_data: TokenRefreshRequest
) -> TokenRefreshResponse:
    """刷新访问令牌
    
    Args:
        refresh_data: 刷新令牌数据
        
    Returns:
        新的访问令牌
        
    Raises:
        AuthenticationError: 刷新令牌无效
    """
    try:
        # 刷新令牌
        new_tokens = await TokenManager.refresh_access_token(refresh_data.refresh_token)
        
        return TokenRefreshResponse(
            message="令牌刷新成功",
            access_token=new_tokens["access_token"],
            token_type=new_tokens["token_type"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise_authentication_error("令牌刷新失败")


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, str]:
    """用户登出
    
    Args:
        request: 请求对象
        current_user: 当前用户
        
    Returns:
        登出响应
    """
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            access_token = auth_header[7:]  # 移除 "Bearer " 前缀
            
            # 将令牌加入黑名单
            await TokenManager.blacklist_token(access_token)
        
        # 记录登出日志
        logger.info(
            f"用户登出: {current_user.username} (ID: {current_user.id})",
            extra={
                "user_id": current_user.id,
                "username": current_user.username,
                "ip_address": request.client.host if request.client else "unknown",
                "event_type": "user_logout"
            }
        )
        
        return {"message": "登出成功"}
        
    except Exception as e:
        logger.error(f"登出处理失败: {e}")
        return {"message": "登出成功"}  # 即使失败也返回成功，避免客户端困惑


@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> UserProfileResponse:
    """获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        用户信息
    """
    return UserProfileResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )


@router.put("/me", response_model=UserProfileResponse)
async def update_current_user_profile(
    update_data: UserUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db_session)
) -> UserProfileResponse:
    """更新当前用户信息

    Args:
        update_data: 更新数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新后的用户信息

    Raises:
        ConflictError: 邮箱已被使用
    """
    try:
        # 检查邮箱是否已被其他用户使用
        if update_data.email and update_data.email != current_user.email:
            existing_email_query = select(User).where(
                User.email == update_data.email,
                User.id != current_user.id
            )
            existing_email_result = await db.execute(existing_email_query)
            existing_email = existing_email_result.scalar_one_or_none()

            if existing_email:
                raise ConflictError("邮箱已被其他用户使用", resource="email")

        # 更新用户信息
        if update_data.nickname is not None:
            current_user.nickname = update_data.nickname

        if update_data.email is not None:
            current_user.email = update_data.email

        # 更新时间戳
        from datetime import datetime
        current_user.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(current_user)

        # 记录更新日志
        logger.info(
            f"用户信息更新: {current_user.username} (ID: {current_user.id})",
            extra={
                "user_id": current_user.id,
                "username": current_user.username,
                "updated_fields": {
                    "nickname": update_data.nickname,
                    "email": update_data.email
                },
                "event_type": "user_profile_update"
            }
        )

        return UserProfileResponse(
            id=current_user.id,
            username=current_user.username,
            email=current_user.email,
            nickname=current_user.nickname,
            avatar_url=current_user.avatar_url,
            is_active=current_user.is_active,
            created_at=current_user.created_at,
            updated_at=current_user.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"用户信息更新失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新失败，请稍后重试"
        )


@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """修改密码

    Args:
        password_data: 密码修改数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        修改结果

    Raises:
        AuthenticationError: 当前密码错误
        ValidationError: 新密码强度不足
    """
    try:
        # 验证当前密码
        if not PasswordManager.verify_password(password_data.current_password, current_user.hashed_password):
            raise_authentication_error("当前密码错误")

        # 验证新密码强度
        password_validation = PasswordValidator.validate_password_strength(password_data.new_password)
        if not password_validation["is_valid"]:
            raise_validation_error(
                message="新密码强度不足",
                field="new_password",
                errors=password_validation["errors"],
                suggestions=password_validation["suggestions"]
            )

        # 更新密码
        new_hashed_password = PasswordManager.hash_password(password_data.new_password)
        current_user.hashed_password = new_hashed_password

        # 更新时间戳
        from datetime import datetime
        current_user.updated_at = datetime.utcnow()

        await db.commit()

        # 记录密码修改日志
        logger.info(
            f"用户密码修改: {current_user.username} (ID: {current_user.id})",
            extra={
                "user_id": current_user.id,
                "username": current_user.username,
                "event_type": "password_change"
            }
        )

        return {"message": "密码修改成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"密码修改失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试"
        )
