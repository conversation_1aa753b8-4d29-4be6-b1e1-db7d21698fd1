"""API v1 模块

提供版本1的API路由和端点。
"""

from fastapi import APIRouter

from .auth import router as auth_router
from .friends import router as friends_router
from .chats import router as chats_router
from .messages import router as messages_router
from .files import router as files_router
from .websocket import router as websocket_router
from .message_queue import router as message_queue_router
from .example import router as example_router

# 创建v1路由器
api_router = APIRouter(prefix="/v1")

# 注册子路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(friends_router, prefix="/friends", tags=["好友管理"])
api_router.include_router(chats_router, prefix="/chats", tags=["聊天管理"])
api_router.include_router(messages_router, prefix="/messages", tags=["消息管理"])
api_router.include_router(files_router, prefix="/files", tags=["文件管理"])
api_router.include_router(websocket_router, prefix="/websocket", tags=["WebSocket"])
api_router.include_router(message_queue_router, prefix="/message-queue", tags=["消息队列"])
api_router.include_router(example_router, prefix="/example", tags=["示例API"])

__all__ = ["api_router"]
