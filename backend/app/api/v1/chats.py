"""聊天管理API

提供聊天创建、列表查看、成员管理和设置功能
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from app.core.database import get_db_session
from app.middleware.auth import get_current_user
from app.models.user import User
from app.models.chat import Chat
from app.models.chat_member import ChatMember
from app.models.friendship import Friendship
from app.schemas.chat import (
    ChatCreate, ChatUpdate, Chat as ChatSchema, ChatWithMembers, 
    ChatSummary, ChatType
)
from app.schemas.chat_member import (
    ChatMemberCreate, ChatMemberUpdate, ChatMember as ChatMemberSchema,
    ChatMemberWithUser, ChatMemberAction, ChatMemberResponse
)
from app.schemas.response import APIResponse
from app.utils.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=ChatWithMembers, status_code=status.HTTP_201_CREATED)
async def create_chat(
    chat_data: ChatCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> ChatWithMembers:
    """创建聊天
    
    Args:
        chat_data: 聊天创建数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        创建的聊天信息
        
    Raises:
        ValidationError: 数据验证失败
        AuthorizationError: 权限不足
    """
    try:
        # 验证聊天类型和成员
        if chat_data.type == ChatType.PRIVATE:
            if not chat_data.member_ids or len(chat_data.member_ids) != 1:
                raise ValidationError("私聊必须指定一个对方用户", field="member_ids")
            
            # 检查是否已存在私聊
            other_user_id = chat_data.member_ids[0]
            existing_private_chat = await db.execute(
                select(Chat)
                .join(ChatMember, Chat.id == ChatMember.chat_id)
                .where(
                    and_(
                        Chat.chat_type == "private",
                        Chat.is_active == True,
                        ChatMember.user_id.in_([current_user.id, other_user_id])
                    )
                )
                .group_by(Chat.id)
                .having(func.count(ChatMember.user_id) == 2)
            )
            
            if existing_private_chat.scalar_one_or_none():
                raise ValidationError("与该用户的私聊已存在", field="member_ids")
                
            # 验证是否为好友关系
            friendship_query = select(Friendship).where(
                and_(
                    or_(
                        and_(Friendship.user_id == current_user.id, Friendship.friend_id == other_user_id),
                        and_(Friendship.user_id == other_user_id, Friendship.friend_id == current_user.id)
                    ),
                    Friendship.status == "accepted"
                )
            )
            friendship = await db.execute(friendship_query)
            if not friendship.scalar_one_or_none():
                raise AuthorizationError("只能与好友创建私聊")
        
        elif chat_data.type == ChatType.GROUP:
            if not chat_data.name or len(chat_data.name.strip()) == 0:
                raise ValidationError("群聊必须有名称", field="name")
            
            if chat_data.member_ids and len(chat_data.member_ids) > 100:
                raise ValidationError("群聊成员不能超过100人", field="member_ids")
        
        # 创建聊天
        new_chat = Chat(
            name=chat_data.name,
            description=chat_data.description,
            chat_type=chat_data.type.value,
            avatar_url=chat_data.avatar_url,
            creator_id=current_user.id,
            is_active=True
        )
        
        db.add(new_chat)
        await db.flush()  # 获取chat ID
        
        # 添加创建者为管理员
        creator_member = ChatMember(
            chat_id=new_chat.id,
            user_id=current_user.id,
            role="admin",
            is_active=True,
            chat_visible=True,
            chat_deleted=False
        )
        db.add(creator_member)
        
        # 添加其他成员
        if chat_data.member_ids:
            for member_id in chat_data.member_ids:
                if member_id != current_user.id:  # 避免重复添加创建者
                    # 验证用户存在
                    user_query = select(User).where(User.id == member_id)
                    user_result = await db.execute(user_query)
                    if not user_result.scalar_one_or_none():
                        raise ValidationError(f"用户ID {member_id} 不存在", field="member_ids")
                    
                    member = ChatMember(
                        chat_id=new_chat.id,
                        user_id=member_id,
                        role="member",
                        is_active=True,
                        chat_visible=True,
                        chat_deleted=False
                    )
                    db.add(member)
        
        await db.commit()
        
        # 重新查询包含成员信息的聊天
        chat_query = select(Chat).options(
            selectinload(Chat.members).selectinload(ChatMember.user),
            selectinload(Chat.creator)
        ).where(Chat.id == new_chat.id)
        
        result = await db.execute(chat_query)
        created_chat = result.scalar_one()
        
        # 记录日志
        logger.info(
            f"聊天创建成功: {created_chat.name or created_chat.id} (ID: {created_chat.id})",
            extra={
                "user_id": current_user.id,
                "chat_id": created_chat.id,
                "chat_type": created_chat.chat_type,
                "member_count": len(created_chat.members),
                "event_type": "chat_create"
            }
        )
        
        # 构建响应
        members_info = []
        for member in created_chat.members:
            if member.is_active:
                members_info.append({
                    "id": member.id,
                    "user": {
                        "id": member.user.id,
                        "username": member.user.username,
                        "nickname": member.user.nickname,
                        "avatar_url": member.user.avatar_url
                    },
                    "role": member.role,
                    "joined_at": member.joined_at,
                    "last_read_at": member.last_read_at,
                    "is_active": member.is_active
                })
        
        return ChatWithMembers(
            id=created_chat.id,
            name=created_chat.name,
            description=created_chat.description,
            avatar_url=created_chat.avatar_url,
            type=ChatType(created_chat.chat_type),
            creator_id=created_chat.creator_id,
            is_active=created_chat.is_active,
            created_at=created_chat.created_at,
            updated_at=created_chat.updated_at,
            members=members_info,
            creator={
                "id": created_chat.creator.id,
                "username": created_chat.creator.username,
                "nickname": created_chat.creator.nickname,
                "avatar_url": created_chat.creator.avatar_url
            },
            member_count=len(members_info)
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"创建聊天失败: {str(e)}", extra={
            "user_id": current_user.id,
            "error": str(e),
            "event_type": "chat_create_error"
        })
        raise


@router.get("/", response_model=List[ChatSummary])
async def get_chat_list(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
) -> List[ChatSummary]:
    """获取聊天列表
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        limit: 返回数量限制
        offset: 偏移量
        
    Returns:
        聊天列表
    """
    try:
        # 查询用户参与的聊天
        query = (
            select(Chat)
            .join(ChatMember, Chat.id == ChatMember.chat_id)
            .where(
                and_(
                    ChatMember.user_id == current_user.id,
                    ChatMember.is_active == True,
                    ChatMember.chat_visible == True,
                    ChatMember.chat_deleted == False,
                    Chat.is_active == True
                )
            )
            .options(
                selectinload(Chat.members).selectinload(ChatMember.user),
                selectinload(Chat.messages)
            )
            .order_by(Chat.updated_at.desc())
            .offset(offset)
            .limit(limit)
        )
        
        result = await db.execute(query)
        chats = result.scalars().all()
        
        chat_summaries = []
        for chat in chats:
            # 获取活跃成员数量
            active_members = [m for m in chat.members if m.is_active]
            member_count = len(active_members)
            
            # 获取最后一条消息
            last_message = None
            if chat.messages:
                latest_msg = max(chat.messages, key=lambda m: m.created_at)
                if not latest_msg.is_deleted:
                    last_message = {
                        "id": latest_msg.id,
                        "content": latest_msg.content,
                        "sender": {
                            "id": latest_msg.sender.id,
                            "username": latest_msg.sender.username,
                            "nickname": latest_msg.sender.nickname,
                            "avatar_url": latest_msg.sender.avatar_url
                        },
                        "created_at": latest_msg.created_at
                    }
            
            # TODO: 计算未读消息数量（需要消息状态表）
            unread_count = 0
            
            chat_summaries.append(ChatSummary(
                id=chat.id,
                name=chat.name,
                type=ChatType(chat.chat_type),
                avatar_url=chat.avatar_url,
                member_count=member_count,
                last_message=last_message,
                unread_count=unread_count,
                updated_at=chat.updated_at
            ))
        
        logger.info(
            f"获取聊天列表成功: {len(chat_summaries)} 个聊天",
            extra={
                "user_id": current_user.id,
                "chat_count": len(chat_summaries),
                "event_type": "chat_list"
            }
        )
        
        return chat_summaries
        
    except Exception as e:
        logger.error(f"获取聊天列表失败: {str(e)}", extra={
            "user_id": current_user.id,
            "error": str(e),
            "event_type": "chat_list_error"
        })
        raise


@router.get("/{chat_id}", response_model=ChatWithMembers)
async def get_chat_detail(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> ChatWithMembers:
    """获取聊天详情
    
    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        聊天详情
        
    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限访问
    """
    try:
        # 查询聊天并验证权限
        chat_query = (
            select(Chat)
            .join(ChatMember, Chat.id == ChatMember.chat_id)
            .where(
                and_(
                    Chat.id == chat_id,
                    ChatMember.user_id == current_user.id,
                    ChatMember.is_active == True,
                    Chat.is_active == True
                )
            )
            .options(
                selectinload(Chat.members).selectinload(ChatMember.user),
                selectinload(Chat.creator)
            )
        )
        
        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()
        
        if not chat:
            raise NotFoundError("聊天不存在或无权限访问")
        
        # 构建成员信息
        members_info = []
        for member in chat.members:
            if member.is_active:
                members_info.append({
                    "id": member.id,
                    "user": {
                        "id": member.user.id,
                        "username": member.user.username,
                        "nickname": member.user.nickname,
                        "avatar_url": member.user.avatar_url
                    },
                    "role": member.role,
                    "joined_at": member.joined_at,
                    "last_read_at": member.last_read_at,
                    "is_active": member.is_active
                })
        
        return ChatWithMembers(
            id=chat.id,
            name=chat.name,
            description=chat.description,
            avatar_url=chat.avatar_url,
            type=ChatType(chat.chat_type),
            creator_id=chat.creator_id,
            is_active=chat.is_active,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
            members=members_info,
            creator={
                "id": chat.creator.id,
                "username": chat.creator.username,
                "nickname": chat.creator.nickname,
                "avatar_url": chat.creator.avatar_url
            },
            member_count=len(members_info)
        )
        
    except Exception as e:
        logger.error(f"获取聊天详情失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "error": str(e),
            "event_type": "chat_detail_error"
        })
        raise


@router.put("/{chat_id}", response_model=ChatSchema)
async def update_chat(
    chat_id: int,
    chat_data: ChatUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> ChatSchema:
    """更新聊天信息

    Args:
        chat_id: 聊天ID
        chat_data: 更新数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新后的聊天信息

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限修改
    """
    try:
        # 查询聊天并验证权限
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 验证用户是否为聊天管理员
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True,
                ChatMember.role == "admin"
            )
        )

        member_result = await db.execute(member_query)
        member = member_result.scalar_one_or_none()

        if not member:
            raise AuthorizationError("只有管理员可以修改聊天信息")

        # 更新聊天信息
        update_data = chat_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(chat, field, value)

        chat.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(chat)

        logger.info(
            f"聊天信息更新成功: {chat.name or chat.id} (ID: {chat.id})",
            extra={
                "user_id": current_user.id,
                "chat_id": chat.id,
                "updated_fields": list(update_data.keys()),
                "event_type": "chat_update"
            }
        )

        return ChatSchema(
            id=chat.id,
            name=chat.name,
            description=chat.description,
            avatar_url=chat.avatar_url,
            type=ChatType(chat.chat_type),
            creator_id=chat.creator_id,
            is_active=chat.is_active,
            created_at=chat.created_at,
            updated_at=chat.updated_at
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"更新聊天信息失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "error": str(e),
            "event_type": "chat_update_error"
        })
        raise


@router.delete("/{chat_id}", response_model=APIResponse)
async def delete_chat(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """删除聊天

    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        删除结果

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限删除
    """
    try:
        # 查询聊天并验证权限
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 验证用户是否为聊天创建者
        if chat.creator_id != current_user.id:
            raise AuthorizationError("只有创建者可以删除聊天")

        # 软删除聊天
        chat.is_active = False
        chat.updated_at = datetime.utcnow()

        await db.commit()

        logger.info(
            f"聊天删除成功: {chat.name or chat.id} (ID: {chat.id})",
            extra={
                "user_id": current_user.id,
                "chat_id": chat.id,
                "event_type": "chat_delete"
            }
        )

        return APIResponse(
            success=True,
            message="聊天删除成功",
            data={"chat_id": chat_id}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"删除聊天失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "error": str(e),
            "event_type": "chat_delete_error"
        })
        raise


@router.get("/{chat_id}/members", response_model=List[ChatMemberWithUser])
async def get_chat_members(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> List[ChatMemberWithUser]:
    """获取聊天成员列表

    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        聊天成员列表

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限访问
    """
    try:
        # 验证用户是否为聊天成员
        member_check_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )

        member_check_result = await db.execute(member_check_query)
        if not member_check_result.scalar_one_or_none():
            raise AuthorizationError("无权限访问该聊天")

        # 查询聊天成员
        members_query = (
            select(ChatMember)
            .options(selectinload(ChatMember.user))
            .where(
                and_(
                    ChatMember.chat_id == chat_id,
                    ChatMember.is_active == True
                )
            )
            .order_by(ChatMember.joined_at)
        )

        result = await db.execute(members_query)
        members = result.scalars().all()

        members_info = []
        for member in members:
            members_info.append(ChatMemberWithUser(
                id=member.id,
                chat_id=member.chat_id,
                user_id=member.user_id,
                role=member.role,
                joined_at=member.joined_at,
                last_read_at=member.last_read_at,
                is_active=member.is_active,
                chat_visible=member.chat_visible,
                chat_deleted=member.chat_deleted,
                user={
                    "id": member.user.id,
                    "username": member.user.username,
                    "nickname": member.user.nickname,
                    "avatar_url": member.user.avatar_url
                }
            ))

        logger.info(
            f"获取聊天成员成功: 聊天{chat_id}，{len(members_info)}个成员",
            extra={
                "user_id": current_user.id,
                "chat_id": chat_id,
                "member_count": len(members_info),
                "event_type": "chat_members_list"
            }
        )

        return members_info

    except Exception as e:
        logger.error(f"获取聊天成员失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "error": str(e),
            "event_type": "chat_members_error"
        })
        raise


@router.post("/{chat_id}/members", response_model=ChatMemberResponse)
async def add_chat_member(
    chat_id: int,
    member_data: ChatMemberCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> ChatMemberResponse:
    """添加聊天成员

    Args:
        chat_id: 聊天ID
        member_data: 成员数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        添加结果

    Raises:
        NotFoundError: 聊天或用户不存在
        AuthorizationError: 无权限操作
        ValidationError: 数据验证失败
    """
    try:
        # 验证聊天存在且用户有权限
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 验证当前用户是否为管理员
        admin_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True,
                ChatMember.role == "admin"
            )
        )

        admin_result = await db.execute(admin_query)
        if not admin_result.scalar_one_or_none():
            raise AuthorizationError("只有管理员可以添加成员")

        # 验证目标用户存在
        user_query = select(User).where(User.id == member_data.user_id)
        user_result = await db.execute(user_query)
        target_user = user_result.scalar_one_or_none()

        if not target_user:
            raise NotFoundError("目标用户不存在")

        # 检查用户是否已是成员
        existing_member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == member_data.user_id
            )
        )

        existing_result = await db.execute(existing_member_query)
        existing_member = existing_result.scalar_one_or_none()

        if existing_member:
            if existing_member.is_active:
                raise ValidationError("用户已是聊天成员", field="user_id")
            else:
                # 重新激活成员
                existing_member.is_active = True
                existing_member.chat_visible = True
                existing_member.chat_deleted = False
                existing_member.joined_at = datetime.utcnow()

                await db.commit()
                await db.refresh(existing_member)

                return ChatMemberResponse(
                    success=True,
                    message="成员重新加入成功",
                    member=ChatMemberSchema(
                        id=existing_member.id,
                        chat_id=existing_member.chat_id,
                        user_id=existing_member.user_id,
                        role=existing_member.role,
                        joined_at=existing_member.joined_at,
                        last_read_at=existing_member.last_read_at,
                        is_active=existing_member.is_active,
                        chat_visible=existing_member.chat_visible,
                        chat_deleted=existing_member.chat_deleted
                    )
                )

        # 创建新成员
        new_member = ChatMember(
            chat_id=chat_id,
            user_id=member_data.user_id,
            role=member_data.role or "member",
            is_active=True,
            chat_visible=True,
            chat_deleted=False
        )

        db.add(new_member)
        await db.commit()
        await db.refresh(new_member)

        logger.info(
            f"聊天成员添加成功: 用户{member_data.user_id}加入聊天{chat_id}",
            extra={
                "user_id": current_user.id,
                "chat_id": chat_id,
                "new_member_id": member_data.user_id,
                "role": new_member.role,
                "event_type": "chat_member_add"
            }
        )

        return ChatMemberResponse(
            success=True,
            message="成员添加成功",
            member=ChatMemberSchema(
                id=new_member.id,
                chat_id=new_member.chat_id,
                user_id=new_member.user_id,
                role=new_member.role,
                joined_at=new_member.joined_at,
                last_read_at=new_member.last_read_at,
                is_active=new_member.is_active,
                chat_visible=new_member.chat_visible,
                chat_deleted=new_member.chat_deleted
            )
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"添加聊天成员失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "target_user_id": member_data.user_id,
            "error": str(e),
            "event_type": "chat_member_add_error"
        })
        raise


@router.delete("/{chat_id}/members/{user_id}", response_model=APIResponse)
async def remove_chat_member(
    chat_id: int,
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """移除聊天成员

    Args:
        chat_id: 聊天ID
        user_id: 要移除的用户ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        移除结果

    Raises:
        NotFoundError: 聊天或成员不存在
        AuthorizationError: 无权限操作
    """
    try:
        # 验证聊天存在
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 验证当前用户权限
        current_member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )

        current_member_result = await db.execute(current_member_query)
        current_member = current_member_result.scalar_one_or_none()

        if not current_member:
            raise AuthorizationError("无权限访问该聊天")

        # 查询目标成员
        target_member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == user_id,
                ChatMember.is_active == True
            )
        )

        target_member_result = await db.execute(target_member_query)
        target_member = target_member_result.scalar_one_or_none()

        if not target_member:
            raise NotFoundError("成员不存在")

        # 权限检查
        if current_member.role != "admin" and current_user.id != user_id:
            raise AuthorizationError("只有管理员可以移除其他成员")

        if user_id == chat.creator_id and current_user.id != user_id:
            raise AuthorizationError("不能移除聊天创建者")

        # 移除成员
        target_member.is_active = False
        target_member.chat_visible = False

        await db.commit()

        action_type = "离开聊天" if current_user.id == user_id else "移除成员"

        logger.info(
            f"{action_type}成功: 用户{user_id}从聊天{chat_id}",
            extra={
                "user_id": current_user.id,
                "chat_id": chat_id,
                "target_user_id": user_id,
                "action": action_type,
                "event_type": "chat_member_remove"
            }
        )

        return APIResponse(
            success=True,
            message=f"{action_type}成功",
            data={"chat_id": chat_id, "user_id": user_id}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"移除聊天成员失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "target_user_id": user_id,
            "error": str(e),
            "event_type": "chat_member_remove_error"
        })
        raise


@router.post("/{chat_id}/leave", response_model=APIResponse)
async def leave_chat(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """离开聊天

    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        离开结果

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 创建者不能离开群聊
    """
    try:
        # 验证聊天存在
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 查询用户成员身份
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )

        member_result = await db.execute(member_query)
        member = member_result.scalar_one_or_none()

        if not member:
            raise NotFoundError("您不是该聊天的成员")

        # 群聊创建者不能离开（需要先转让管理权）
        if chat.chat_type == "group" and chat.creator_id == current_user.id:
            # 检查是否还有其他管理员
            other_admins_query = select(ChatMember).where(
                and_(
                    ChatMember.chat_id == chat_id,
                    ChatMember.user_id != current_user.id,
                    ChatMember.role == "admin",
                    ChatMember.is_active == True
                )
            )

            other_admins_result = await db.execute(other_admins_query)
            if not other_admins_result.scalar_one_or_none():
                raise AuthorizationError("群聊创建者离开前需要指定其他管理员")

        # 离开聊天
        member.is_active = False
        member.chat_visible = False

        await db.commit()

        logger.info(
            f"用户离开聊天成功: 用户{current_user.id}离开聊天{chat_id}",
            extra={
                "user_id": current_user.id,
                "chat_id": chat_id,
                "event_type": "chat_leave"
            }
        )

        return APIResponse(
            success=True,
            message="离开聊天成功",
            data={"chat_id": chat_id}
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"离开聊天失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "error": str(e),
            "event_type": "chat_leave_error"
        })
        raise


@router.put("/{chat_id}/members/{user_id}", response_model=ChatMemberResponse)
async def update_member_role(
    chat_id: int,
    user_id: int,
    member_data: ChatMemberUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> ChatMemberResponse:
    """更新成员角色

    Args:
        chat_id: 聊天ID
        user_id: 成员用户ID
        member_data: 更新数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新结果

    Raises:
        NotFoundError: 聊天或成员不存在
        AuthorizationError: 无权限操作
    """
    try:
        # 验证聊天存在
        chat_query = select(Chat).where(
            and_(
                Chat.id == chat_id,
                Chat.is_active == True
            )
        )

        result = await db.execute(chat_query)
        chat = result.scalar_one_or_none()

        if not chat:
            raise NotFoundError("聊天不存在")

        # 验证当前用户是否为管理员
        current_member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True,
                ChatMember.role == "admin"
            )
        )

        current_member_result = await db.execute(current_member_query)
        if not current_member_result.scalar_one_or_none():
            raise AuthorizationError("只有管理员可以修改成员角色")

        # 查询目标成员
        target_member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == user_id,
                ChatMember.is_active == True
            )
        )

        target_member_result = await db.execute(target_member_query)
        target_member = target_member_result.scalar_one_or_none()

        if not target_member:
            raise NotFoundError("成员不存在")

        # 不能修改创建者角色
        if user_id == chat.creator_id and member_data.role == "member":
            raise AuthorizationError("不能将创建者降级为普通成员")

        # 更新成员角色
        old_role = target_member.role
        target_member.role = member_data.role

        await db.commit()
        await db.refresh(target_member)

        logger.info(
            f"成员角色更新成功: 用户{user_id}在聊天{chat_id}从{old_role}变为{member_data.role}",
            extra={
                "user_id": current_user.id,
                "chat_id": chat_id,
                "target_user_id": user_id,
                "old_role": old_role,
                "new_role": member_data.role,
                "event_type": "chat_member_role_update"
            }
        )

        return ChatMemberResponse(
            success=True,
            message="成员角色更新成功",
            member=ChatMemberSchema(
                id=target_member.id,
                chat_id=target_member.chat_id,
                user_id=target_member.user_id,
                role=target_member.role,
                joined_at=target_member.joined_at,
                last_read_at=target_member.last_read_at,
                is_active=target_member.is_active,
                chat_visible=target_member.chat_visible,
                chat_deleted=target_member.chat_deleted
            )
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"更新成员角色失败: {str(e)}", extra={
            "user_id": current_user.id,
            "chat_id": chat_id,
            "target_user_id": user_id,
            "error": str(e),
            "event_type": "chat_member_role_update_error"
        })
        raise
