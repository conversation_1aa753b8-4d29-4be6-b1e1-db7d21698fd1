"""文件上传API

提供头像上传、文件分享和媒体资源管理功能
"""

import os
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from app.core.database import get_db_session
from app.middleware.auth import get_current_user
from app.models.user import User
from app.models.chat import Chat
from app.models.chat_member import ChatMember
from app.models.file import File as FileModel
from app.schemas.file import (
    FileUploadRequest, FileInfo, FileUploadResponse, FileListResponse,
    AvatarUploadResponse, FileDeleteRequest, FileSearchRequest,
    FileStatistics, FileType, UploadPurpose, ChatFileList
)
from app.schemas.response import APIResponse
from app.utils.file_handler import file_handler
from app.utils.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/upload", response_model=FileUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(...),
    purpose: UploadPurpose = Form(...),
    chat_id: Optional[int] = Form(None),
    description: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> FileUploadResponse:
    """上传文件
    
    Args:
        file: 上传的文件
        purpose: 上传目的
        chat_id: 聊天ID（聊天文件时必需）
        description: 文件描述
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        上传结果
        
    Raises:
        ValidationError: 文件验证失败
        AuthorizationError: 无权限上传
    """
    try:
        # 验证文件
        if not file.filename:
            raise ValidationError("文件名不能为空", field="file")
        
        # 读取文件内容
        file_content = await file.read()
        file_size = len(file_content)
        
        # 验证文件
        file_handler.validate_file(file.filename, file_size, file.content_type or "")
        
        # 如果是聊天文件，验证聊天权限
        if purpose == UploadPurpose.CHAT_FILE:
            if not chat_id:
                raise ValidationError("聊天文件必须指定聊天ID", field="chat_id")
            
            # 验证用户是否为聊天成员
            member_query = select(ChatMember).where(
                and_(
                    ChatMember.chat_id == chat_id,
                    ChatMember.user_id == current_user.id,
                    ChatMember.is_active == True
                )
            )
            
            member_result = await db.execute(member_query)
            if not member_result.scalar_one_or_none():
                raise AuthorizationError("只有聊天成员可以上传文件")
        
        # 保存文件
        file_path, file_url = await file_handler.save_file(
            file_content, file.filename, purpose.value
        )
        
        # 确定文件类型
        file_type = FileModel.get_file_type_from_mime(file.content_type or "")
        if purpose == UploadPurpose.AVATAR:
            file_type = "avatar"
        
        # 创建文件记录
        new_file = FileModel(
            filename=file.filename,
            file_path=file_path,
            file_url=file_url,
            file_type=file_type,
            file_size=file_size,
            mime_type=file.content_type or "application/octet-stream",
            uploader_id=current_user.id,
            purpose=purpose.value,
            chat_id=chat_id,
            description=description
        )
        
        db.add(new_file)
        await db.flush()
        
        # 如果是图片，创建缩略图
        if file_type == "image" or purpose == UploadPurpose.AVATAR:
            thumbnail_path = await file_handler.create_thumbnail(file_path)
            if thumbnail_path:
                logger.info(f"为文件 {new_file.id} 创建了缩略图")
        
        # 如果是头像，更新用户头像URL
        if purpose == UploadPurpose.AVATAR:
            current_user.avatar_url = file_url
            current_user.updated_at = datetime.utcnow()
        
        await db.commit()
        
        # 构建响应
        file_info = FileInfo(
            id=new_file.id,
            filename=new_file.filename,
            file_path=new_file.file_path,
            file_url=new_file.file_url,
            file_type=FileType(new_file.file_type),
            file_size=new_file.file_size,
            mime_type=new_file.mime_type,
            uploader_id=new_file.uploader_id,
            purpose=UploadPurpose(new_file.purpose),
            chat_id=new_file.chat_id,
            description=new_file.description,
            is_deleted=new_file.is_deleted,
            created_at=new_file.created_at,
            updated_at=new_file.updated_at
        )
        
        logger.info(f"用户 {current_user.id} 上传文件成功: {file.filename} (ID: {new_file.id})")
        return FileUploadResponse(
            success=True,
            message="文件上传成功",
            file=file_info
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败，请稍后重试"
        )


@router.post("/avatar", response_model=AvatarUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> AvatarUploadResponse:
    """上传用户头像
    
    Args:
        file: 头像文件
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        头像上传结果
    """
    try:
        # 验证是否为图片文件
        if not file.content_type or not file.content_type.startswith("image/"):
            raise ValidationError("头像必须是图片文件", field="file")
        
        # 读取文件内容
        file_content = await file.read()
        file_size = len(file_content)
        
        # 验证文件大小（头像限制更小）
        max_avatar_size = 2 * 1024 * 1024  # 2MB
        if file_size > max_avatar_size:
            raise ValidationError("头像文件大小不能超过2MB", field="file")
        
        # 保存文件
        file_path, file_url = await file_handler.save_file(
            file_content, file.filename, "avatar"
        )
        
        # 创建文件记录
        new_file = FileModel(
            filename=file.filename,
            file_path=file_path,
            file_url=file_url,
            file_type="avatar",
            file_size=file_size,
            mime_type=file.content_type,
            uploader_id=current_user.id,
            purpose="avatar"
        )
        
        db.add(new_file)
        
        # 更新用户头像URL
        current_user.avatar_url = file_url
        current_user.updated_at = datetime.utcnow()
        
        # 创建缩略图
        await file_handler.create_thumbnail(file_path)
        
        await db.commit()
        
        logger.info(f"用户 {current_user.id} 更新头像成功: {file.filename}")
        return AvatarUploadResponse(
            success=True,
            message="头像上传成功",
            avatar_url=file_url
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"头像上传失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="头像上传失败，请稍后重试"
        )


@router.get("/", response_model=FileListResponse)
async def get_user_files(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    file_type: Optional[FileType] = Query(None, description="文件类型过滤"),
    purpose: Optional[UploadPurpose] = Query(None, description="上传目的过滤"),
    limit: int = Query(20, ge=1, le=100, description="每页文件数量"),
    offset: int = Query(0, ge=0, description="偏移量")
) -> FileListResponse:
    """获取用户文件列表

    Args:
        current_user: 当前用户
        db: 数据库会话
        file_type: 文件类型过滤
        purpose: 上传目的过滤
        limit: 每页文件数量
        offset: 偏移量

    Returns:
        文件列表
    """
    try:
        # 构建查询条件
        query_conditions = [
            FileModel.uploader_id == current_user.id,
            FileModel.is_deleted == False
        ]

        if file_type:
            query_conditions.append(FileModel.file_type == file_type.value)

        if purpose:
            query_conditions.append(FileModel.purpose == purpose.value)

        # 查询文件
        files_query = (
            select(FileModel)
            .where(and_(*query_conditions))
            .order_by(desc(FileModel.created_at))
            .offset(offset)
            .limit(limit + 1)  # 多查询一条用于判断是否还有更多
        )

        files_result = await db.execute(files_query)
        files = files_result.scalars().all()

        # 判断是否还有更多
        has_more = len(files) > limit
        if has_more:
            files = files[:-1]

        # 查询总数
        count_query = select(func.count(FileModel.id)).where(and_(*query_conditions))
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 构建响应
        file_list = []
        for file_obj in files:
            file_info = FileInfo(
                id=file_obj.id,
                filename=file_obj.filename,
                file_path=file_obj.file_path,
                file_url=file_obj.file_url,
                file_type=FileType(file_obj.file_type),
                file_size=file_obj.file_size,
                mime_type=file_obj.mime_type,
                uploader_id=file_obj.uploader_id,
                purpose=UploadPurpose(file_obj.purpose),
                chat_id=file_obj.chat_id,
                description=file_obj.description,
                is_deleted=file_obj.is_deleted,
                created_at=file_obj.created_at,
                updated_at=file_obj.updated_at
            )
            file_list.append(file_info)

        return FileListResponse(
            files=file_list,
            total=total,
            has_more=has_more
        )

    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件列表失败，请稍后重试"
        )


@router.get("/chat/{chat_id}", response_model=ChatFileList)
async def get_chat_files(
    chat_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    file_type: Optional[FileType] = Query(None, description="文件类型过滤"),
    limit: int = Query(50, ge=1, le=100, description="每页文件数量"),
    offset: int = Query(0, ge=0, description="偏移量")
) -> ChatFileList:
    """获取聊天文件列表

    Args:
        chat_id: 聊天ID
        current_user: 当前用户
        db: 数据库会话
        file_type: 文件类型过滤
        limit: 每页文件数量
        offset: 偏移量

    Returns:
        聊天文件列表

    Raises:
        NotFoundError: 聊天不存在
        AuthorizationError: 无权限访问
    """
    try:
        # 验证用户是否为聊天成员
        member_query = select(ChatMember).where(
            and_(
                ChatMember.chat_id == chat_id,
                ChatMember.user_id == current_user.id,
                ChatMember.is_active == True
            )
        )

        member_result = await db.execute(member_query)
        if not member_result.scalar_one_or_none():
            raise AuthorizationError("无权限访问该聊天的文件")

        # 构建查询条件
        query_conditions = [
            FileModel.chat_id == chat_id,
            FileModel.is_deleted == False
        ]

        if file_type:
            query_conditions.append(FileModel.file_type == file_type.value)

        # 查询文件
        files_query = (
            select(FileModel)
            .where(and_(*query_conditions))
            .order_by(desc(FileModel.created_at))
            .offset(offset)
            .limit(limit)
        )

        files_result = await db.execute(files_query)
        files = files_result.scalars().all()

        # 查询总数和总大小
        count_query = select(func.count(FileModel.id)).where(and_(*query_conditions))
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        size_query = select(func.sum(FileModel.file_size)).where(and_(*query_conditions))
        size_result = await db.execute(size_query)
        total_size = size_result.scalar() or 0

        # 构建响应
        file_list = []
        for file_obj in files:
            file_info = FileInfo(
                id=file_obj.id,
                filename=file_obj.filename,
                file_path=file_obj.file_path,
                file_url=file_obj.file_url,
                file_type=FileType(file_obj.file_type),
                file_size=file_obj.file_size,
                mime_type=file_obj.mime_type,
                uploader_id=file_obj.uploader_id,
                purpose=UploadPurpose(file_obj.purpose),
                chat_id=file_obj.chat_id,
                description=file_obj.description,
                is_deleted=file_obj.is_deleted,
                created_at=file_obj.created_at,
                updated_at=file_obj.updated_at
            )
            file_list.append(file_info)

        return ChatFileList(
            chat_id=chat_id,
            files=file_list,
            total=total,
            total_size=total_size
        )

    except Exception as e:
        logger.error(f"获取聊天文件列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天文件列表失败，请稍后重试"
        )


@router.delete("/{file_id}", response_model=APIResponse)
async def delete_file(
    file_id: int,
    permanent: bool = Query(False, description="是否永久删除"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> APIResponse:
    """删除文件

    Args:
        file_id: 文件ID
        permanent: 是否永久删除
        current_user: 当前用户
        db: 数据库会话

    Returns:
        删除结果

    Raises:
        NotFoundError: 文件不存在
        AuthorizationError: 无权限删除
    """
    try:
        # 查询文件
        file_query = select(FileModel).where(FileModel.id == file_id)
        file_result = await db.execute(file_query)
        file_obj = file_result.scalar_one_or_none()

        if not file_obj:
            raise NotFoundError("文件不存在")

        # 验证权限（只有文件所有者可以删除）
        if file_obj.uploader_id != current_user.id:
            raise AuthorizationError("只能删除自己上传的文件")

        if permanent:
            # 永久删除：删除物理文件和数据库记录
            await file_handler.delete_file(file_obj.file_path)
            await db.delete(file_obj)
            message = "文件已永久删除"
        else:
            # 软删除：只标记为已删除
            file_obj.soft_delete()
            message = "文件已删除"

        await db.commit()

        logger.info(f"用户 {current_user.id} 删除文件: {file_obj.filename} (ID: {file_id}, permanent: {permanent})")
        return APIResponse(
            success=True,
            message=message
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除文件失败，请稍后重试"
        )


@router.get("/{file_id}", response_model=FileInfo)
async def get_file_info(
    file_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> FileInfo:
    """获取文件信息

    Args:
        file_id: 文件ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        文件信息

    Raises:
        NotFoundError: 文件不存在
        AuthorizationError: 无权限访问
    """
    try:
        # 查询文件
        file_query = select(FileModel).where(
            and_(
                FileModel.id == file_id,
                FileModel.is_deleted == False
            )
        )
        file_result = await db.execute(file_query)
        file_obj = file_result.scalar_one_or_none()

        if not file_obj:
            raise NotFoundError("文件不存在")

        # 验证访问权限
        if not file_obj.can_be_accessed_by(current_user.id):
            # 如果是聊天文件，需要验证聊天成员身份
            if file_obj.chat_id and file_obj.purpose == "chat_file":
                member_query = select(ChatMember).where(
                    and_(
                        ChatMember.chat_id == file_obj.chat_id,
                        ChatMember.user_id == current_user.id,
                        ChatMember.is_active == True
                    )
                )
                member_result = await db.execute(member_query)
                if not member_result.scalar_one_or_none():
                    raise AuthorizationError("无权限访问该文件")
            else:
                raise AuthorizationError("无权限访问该文件")

        # 构建响应
        return FileInfo(
            id=file_obj.id,
            filename=file_obj.filename,
            file_path=file_obj.file_path,
            file_url=file_obj.file_url,
            file_type=FileType(file_obj.file_type),
            file_size=file_obj.file_size,
            mime_type=file_obj.mime_type,
            uploader_id=file_obj.uploader_id,
            purpose=UploadPurpose(file_obj.purpose),
            chat_id=file_obj.chat_id,
            description=file_obj.description,
            is_deleted=file_obj.is_deleted,
            created_at=file_obj.created_at,
            updated_at=file_obj.updated_at
        )

    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件信息失败，请稍后重试"
        )


@router.post("/search", response_model=FileListResponse)
async def search_files(
    search_request: FileSearchRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    limit: int = Query(20, ge=1, le=100, description="每页文件数量"),
    offset: int = Query(0, ge=0, description="偏移量")
) -> FileListResponse:
    """搜索文件

    Args:
        search_request: 搜索请求
        current_user: 当前用户
        db: 数据库会话
        limit: 每页文件数量
        offset: 偏移量

    Returns:
        搜索结果
    """
    try:
        # 基础查询条件
        query_conditions = [
            FileModel.is_deleted == False
        ]

        # 权限过滤：只能搜索自己的文件或有权限访问的聊天文件
        accessible_conditions = [
            FileModel.uploader_id == current_user.id,  # 自己的文件
            and_(  # 或者是有权限的聊天文件
                FileModel.purpose == "chat_file",
                FileModel.chat_id.in_(
                    select(ChatMember.chat_id).where(
                        and_(
                            ChatMember.user_id == current_user.id,
                            ChatMember.is_active == True
                        )
                    )
                )
            ),
            FileModel.purpose == "avatar"  # 或者是头像文件（公开）
        ]

        from sqlalchemy import or_
        query_conditions.append(or_(*accessible_conditions))

        # 添加搜索条件
        if search_request.query:
            query_conditions.append(
                or_(
                    FileModel.filename.ilike(f"%{search_request.query}%"),
                    FileModel.description.ilike(f"%{search_request.query}%")
                )
            )

        if search_request.file_type:
            query_conditions.append(FileModel.file_type == search_request.file_type.value)

        if search_request.purpose:
            query_conditions.append(FileModel.purpose == search_request.purpose.value)

        if search_request.chat_id:
            query_conditions.append(FileModel.chat_id == search_request.chat_id)

        if search_request.uploader_id:
            query_conditions.append(FileModel.uploader_id == search_request.uploader_id)

        if search_request.start_date:
            query_conditions.append(FileModel.created_at >= search_request.start_date)

        if search_request.end_date:
            query_conditions.append(FileModel.created_at <= search_request.end_date)

        if search_request.min_size:
            query_conditions.append(FileModel.file_size >= search_request.min_size)

        if search_request.max_size:
            query_conditions.append(FileModel.file_size <= search_request.max_size)

        # 查询文件
        files_query = (
            select(FileModel)
            .where(and_(*query_conditions))
            .order_by(desc(FileModel.created_at))
            .offset(offset)
            .limit(limit + 1)
        )

        files_result = await db.execute(files_query)
        files = files_result.scalars().all()

        # 判断是否还有更多
        has_more = len(files) > limit
        if has_more:
            files = files[:-1]

        # 查询总数
        count_query = select(func.count(FileModel.id)).where(and_(*query_conditions))
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 构建响应
        file_list = []
        for file_obj in files:
            file_info = FileInfo(
                id=file_obj.id,
                filename=file_obj.filename,
                file_path=file_obj.file_path,
                file_url=file_obj.file_url,
                file_type=FileType(file_obj.file_type),
                file_size=file_obj.file_size,
                mime_type=file_obj.mime_type,
                uploader_id=file_obj.uploader_id,
                purpose=UploadPurpose(file_obj.purpose),
                chat_id=file_obj.chat_id,
                description=file_obj.description,
                is_deleted=file_obj.is_deleted,
                created_at=file_obj.created_at,
                updated_at=file_obj.updated_at
            )
            file_list.append(file_info)

        return FileListResponse(
            files=file_list,
            total=total,
            has_more=has_more
        )

    except Exception as e:
        logger.error(f"搜索文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索文件失败，请稍后重试"
        )


@router.get("/statistics/user", response_model=FileStatistics)
async def get_user_file_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> FileStatistics:
    """获取用户文件统计信息

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        文件统计信息
    """
    try:
        # 查询用户文件统计
        base_query = select(FileModel).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False
            )
        )

        # 总文件数和总大小
        count_query = select(func.count(FileModel.id)).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False
            )
        )
        count_result = await db.execute(count_query)
        total_files = count_result.scalar()

        size_query = select(func.sum(FileModel.file_size)).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False
            )
        )
        size_result = await db.execute(size_query)
        total_size = size_result.scalar() or 0

        # 按类型统计
        type_query = select(
            FileModel.file_type,
            func.count(FileModel.id).label('count'),
            func.sum(FileModel.file_size).label('size')
        ).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False
            )
        ).group_by(FileModel.file_type)

        type_result = await db.execute(type_query)
        by_type = {}
        for row in type_result:
            by_type[row.file_type] = {
                "count": row.count,
                "size": row.size or 0
            }

        # 按目的统计
        purpose_query = select(
            FileModel.purpose,
            func.count(FileModel.id).label('count'),
            func.sum(FileModel.file_size).label('size')
        ).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False
            )
        ).group_by(FileModel.purpose)

        purpose_result = await db.execute(purpose_query)
        by_purpose = {}
        for row in purpose_result:
            by_purpose[row.purpose] = {
                "count": row.count,
                "size": row.size or 0
            }

        # 最近7天上传数量
        from datetime import timedelta
        recent_date = datetime.utcnow() - timedelta(days=7)
        recent_query = select(func.count(FileModel.id)).where(
            and_(
                FileModel.uploader_id == current_user.id,
                FileModel.is_deleted == False,
                FileModel.created_at >= recent_date
            )
        )
        recent_result = await db.execute(recent_query)
        recent_uploads = recent_result.scalar()

        return FileStatistics(
            total_files=total_files,
            total_size=total_size,
            by_type=by_type,
            by_purpose=by_purpose,
            recent_uploads=recent_uploads
        )

    except Exception as e:
        logger.error(f"获取文件统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件统计失败，请稍后重试"
        )
