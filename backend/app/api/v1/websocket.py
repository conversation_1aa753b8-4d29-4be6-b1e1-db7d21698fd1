"""WebSocket API端点

提供WebSocket连接和实时通信功能
"""

import asyncio
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException, status
from fastapi.responses import JSONResponse
import logging

from app.websocket import (
    connection_manager, event_handler, WebSocketUtils,
    MessageValidator, connection_tracker, message_push_service
)
from app.websocket.status_sync import status_sync_service
from app.schemas.websocket import (
    WebSocketMessage, ConnectionStats, BroadcastRequest,
    create_websocket_message, MessageType, OnlineStatus
)
from app.websocket.auth import WebSocketAuthenticator, session_manager
from app.utils.response_handler import APIResponseHandler, success_response, error_response
from app.utils.decorators import handle_database_errors, log_execution

logger = logging.getLogger(__name__)
router = APIRouter()


async def get_websocket_user(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="JWT访问令牌")
) -> Optional[int]:
    """从WebSocket连接获取用户信息"""
    try:
        if not token:
            await WebSocketAuthenticator.close_websocket_with_auth_error(
                websocket, 4001, "Missing authentication token"
            )
            return None

        # 使用WebSocket认证器验证用户
        user = await WebSocketAuthenticator.get_websocket_user_with_db(websocket, token)
        if not user:
            await WebSocketAuthenticator.close_websocket_with_auth_error(
                websocket, 4001, "Invalid authentication token"
            )
            return None

        return user.id

    except Exception as e:
        logger.error(f"WebSocket authentication failed: {e}")
        await WebSocketAuthenticator.close_websocket_with_auth_error(
            websocket, 4001, "Authentication failed"
        )
        return None


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="JWT访问令牌")
):
    """WebSocket连接端点
    
    建立WebSocket连接，处理实时消息通信
    """
    connection_id = None
    user_id = None
    
    try:
        # 认证用户
        user_id = await get_websocket_user(websocket, token)
        if not user_id:
            return
        
        # 获取完整用户对象用于会话管理
        user = await WebSocketAuthenticator.get_websocket_user_with_db(websocket, token)
        if not user:
            return

        # 建立连接
        connection_id = await connection_manager.connect(
            websocket=websocket,
            user_id=user_id,
            user_agent=websocket.headers.get("user-agent"),
            ip_address=websocket.client.host if websocket.client else None
        )

        # 创建用户会话
        session_info = await session_manager.create_session(user, connection_id, websocket)

        # 记录连接事件
        connection_tracker.log_connection_event(
            "connect", user_id, connection_id, {
                "username": user.username,
                "is_admin": user.is_superuser
            }
        )
        
        # 发送连接成功消息
        welcome_message = create_websocket_message(
            MessageType.CONNECT,
            {
                "connection_id": connection_id,
                "user_id": user_id,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        )
        await WebSocketUtils.safe_send_json(websocket, welcome_message.model_dump())
        
        # 消息处理循环
        while True:
            try:
                # 接收消息
                data = await WebSocketUtils.safe_receive_json(websocket)
                if data is None:
                    break
                
                # 解析消息
                message = WebSocketUtils.parse_websocket_message(data)
                if not message:
                    continue
                
                # 更新会话活动时间
                await session_manager.update_session_activity(user_id)

                # 处理消息
                await event_handler.handle_message(websocket, connection_id, message)
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Message processing error: {e}")
                # 发送错误消息但不断开连接
                error_msg = WebSocketUtils.create_error_message(
                    "MESSAGE_PROCESSING_ERROR",
                    "消息处理失败"
                )
                await WebSocketUtils.safe_send_json(websocket, error_msg.model_dump())
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
    finally:
        # 清理连接
        if connection_id:
            await connection_manager.disconnect(connection_id)

            # 移除用户会话
            if user_id:
                await session_manager.remove_session(user_id)

            # 记录断开事件
            if user_id:
                connection_tracker.log_connection_event(
                    "disconnect", user_id, connection_id
                )


@router.get("/stats", summary="获取连接统计")
@handle_database_errors
@log_execution("info")
async def get_connection_stats() -> JSONResponse:
    """获取WebSocket连接统计信息"""
    try:
        stats = connection_manager.get_connection_stats()
        
        return success_response(
            data=stats.model_dump(),
            message="连接统计获取成功"
        )
        
    except Exception as e:
        logger.error(f"Failed to get connection stats: {e}")
        return error_response(
            message="获取连接统计失败",
            error_code="STATS_ERROR"
        )


@router.get("/online-users", summary="获取在线用户")
@handle_database_errors
@log_execution("info")
async def get_online_users() -> JSONResponse:
    """获取在线用户列表"""
    try:
        online_users = connection_manager.get_online_users()
        
        return success_response(
            data={"online_users": online_users, "count": len(online_users)},
            message="在线用户获取成功"
        )
        
    except Exception as e:
        logger.error(f"Failed to get online users: {e}")
        return error_response(
            message="获取在线用户失败",
            error_code="ONLINE_USERS_ERROR"
        )


@router.post("/broadcast", summary="广播消息")
@handle_database_errors
@log_execution("info")
async def broadcast_message(
    broadcast_request: BroadcastRequest
) -> JSONResponse:
    """广播消息给指定用户或所有用户
    
    管理员功能，用于发送系统通知
    """
    try:
        message = broadcast_request.message
        
        # 验证消息
        if not MessageValidator.validate_message_data(message):
            return error_response(
                message="消息格式无效",
                error_code="INVALID_MESSAGE"
            )
        
        success_count = 0
        
        if broadcast_request.target_users:
            # 发送给指定用户
            for user_id in broadcast_request.target_users:
                if await connection_manager.send_personal_message(user_id, message):
                    success_count += 1
        elif broadcast_request.chat_id:
            # 发送给聊天室成员
            success_count = await connection_manager.send_to_chat(
                broadcast_request.chat_id,
                message
            )
        else:
            # 广播给所有用户
            exclude_users = set(broadcast_request.exclude_users or [])
            success_count = await connection_manager.broadcast_to_all(
                message,
                exclude_user_ids=exclude_users
            )
        
        return success_response(
            data={"recipients": success_count},
            message="消息广播成功"
        )
        
    except Exception as e:
        logger.error(f"Failed to broadcast message: {e}")
        return error_response(
            message="消息广播失败",
            error_code="BROADCAST_ERROR"
        )


@router.get("/connection-events", summary="获取连接事件")
@handle_database_errors
@log_execution("info")
async def get_connection_events(
    limit: int = Query(100, ge=1, le=1000, description="事件数量限制")
) -> JSONResponse:
    """获取最近的连接事件"""
    try:
        events = connection_tracker.get_recent_events(limit)
        
        return success_response(
            data={"events": events, "count": len(events)},
            message="连接事件获取成功"
        )
        
    except Exception as e:
        logger.error(f"Failed to get connection events: {e}")
        return error_response(
            message="获取连接事件失败",
            error_code="CONNECTION_EVENTS_ERROR"
        )


@router.get("/user/{user_id}/connections", summary="获取用户连接")
@handle_database_errors
@log_execution("info")
async def get_user_connections(user_id: int) -> JSONResponse:
    """获取指定用户的连接信息"""
    try:
        connections = connection_manager.get_user_connections(user_id)
        is_online = connection_manager.is_user_online(user_id)
        
        connection_details = []
        for conn_id in connections:
            conn_info = connection_manager.connection_info.get(conn_id)
            if conn_info:
                connection_details.append(conn_info.model_dump())
        
        return success_response(
            data={
                "user_id": user_id,
                "is_online": is_online,
                "connections": connection_details,
                "connection_count": len(connections)
            },
            message="用户连接信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"Failed to get user connections: {e}")
        return error_response(
            message="获取用户连接失败",
            error_code="USER_CONNECTIONS_ERROR"
        )


@router.get("/user/{user_id}/offline-messages", summary="获取离线消息")
@handle_database_errors
@log_execution("info")
async def get_offline_messages(user_id: int) -> JSONResponse:
    """获取用户的离线消息"""
    try:
        # 获取离线消息
        offline_messages = await message_push_service.get_offline_messages(user_id)

        return success_response(
            data={
                "user_id": user_id,
                "messages": offline_messages,
                "message_count": len(offline_messages)
            },
            message="离线消息获取成功"
        )

    except Exception as e:
        logger.error(f"Failed to get offline messages: {e}")
        return error_response(
            message="获取离线消息失败",
            error_code="OFFLINE_MESSAGES_ERROR"
        )


@router.post("/push/private", summary="推送私聊消息")
@handle_database_errors
@log_execution("info")
async def push_private_message_api(
    sender_id: int,
    receiver_id: int,
    content: str,
    message_type: str = "text"
) -> JSONResponse:
    """API方式推送私聊消息"""
    try:
        result = await message_push_service.push_private_message(
            sender_id=sender_id,
            receiver_id=receiver_id,
            content=content,
            message_type=message_type
        )

        if "error" in result:
            return error_response(
                message="推送私聊消息失败",
                error_code="PRIVATE_MESSAGE_PUSH_ERROR",
                details=result
            )

        return success_response(
            data=result,
            message="私聊消息推送成功"
        )

    except Exception as e:
        logger.error(f"Failed to push private message: {e}")
        return error_response(
            message="推送私聊消息失败",
            error_code="PRIVATE_MESSAGE_PUSH_ERROR"
        )


@router.post("/push/system", summary="推送系统通知")
@handle_database_errors
@log_execution("info")
async def push_system_notification_api(
    title: str,
    content: str,
    target_users: Optional[list[int]] = None,
    level: str = "info"
) -> JSONResponse:
    """API方式推送系统通知"""
    try:
        result = await message_push_service.push_system_notification(
            title=title,
            content=content,
            target_users=target_users,
            level=level
        )

        if "error" in result:
            return error_response(
                message="推送系统通知失败",
                error_code="SYSTEM_NOTIFICATION_PUSH_ERROR",
                details=result
            )

        return success_response(
            data=result,
            message="系统通知推送成功"
        )

    except Exception as e:
        logger.error(f"Failed to push system notification: {e}")
        return error_response(
            message="推送系统通知失败",
            error_code="SYSTEM_NOTIFICATION_PUSH_ERROR"
        )


@router.get("/status/online-users", summary="获取在线用户状态")
@handle_database_errors
@log_execution("info")
async def get_online_users_status() -> JSONResponse:
    """获取所有在线用户的状态信息"""
    try:
        online_users = status_sync_service.get_online_users_status()

        return success_response(
            data={
                "online_users": online_users,
                "total_count": len(online_users),
                "timestamp": datetime.utcnow().isoformat()
            },
            message="在线用户状态获取成功"
        )

    except Exception as e:
        logger.error(f"Failed to get online users status: {e}")
        return error_response(
            message="获取在线用户状态失败",
            error_code="ONLINE_USERS_STATUS_ERROR"
        )


@router.get("/status/user/{user_id}", summary="获取用户状态")
@handle_database_errors
@log_execution("info")
async def get_user_status(user_id: int) -> JSONResponse:
    """获取指定用户的状态信息"""
    try:
        user_status = status_sync_service.get_user_status(user_id)

        if not user_status:
            return error_response(
                message="用户状态不存在",
                error_code="USER_STATUS_NOT_FOUND"
            )

        return success_response(
            data=user_status,
            message="用户状态获取成功"
        )

    except Exception as e:
        logger.error(f"Failed to get user status: {e}")
        return error_response(
            message="获取用户状态失败",
            error_code="USER_STATUS_ERROR"
        )


@router.post("/status/user/{user_id}", summary="更新用户状态")
@handle_database_errors
@log_execution("info")
async def update_user_status_api(
    user_id: int,
    status: str,
    custom_message: Optional[str] = None
) -> JSONResponse:
    """API方式更新用户状态"""
    try:
        # 验证状态值
        try:
            online_status = OnlineStatus(status)
        except ValueError:
            return error_response(
                message="无效的状态值",
                error_code="INVALID_STATUS_VALUE",
                details={"valid_statuses": [s.value for s in OnlineStatus]}
            )

        result = await status_sync_service.update_user_status(
            user_id=user_id,
            status=online_status,
            custom_message=custom_message
        )

        if "error" in result:
            return error_response(
                message="更新用户状态失败",
                error_code="USER_STATUS_UPDATE_ERROR",
                details=result
            )

        return success_response(
            data=result,
            message="用户状态更新成功"
        )

    except Exception as e:
        logger.error(f"Failed to update user status: {e}")
        return error_response(
            message="更新用户状态失败",
            error_code="USER_STATUS_UPDATE_ERROR"
        )
