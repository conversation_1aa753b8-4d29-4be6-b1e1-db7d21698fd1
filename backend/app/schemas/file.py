"""文件上传相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from app.core.validation_config import ValidationLimits, ValidationRules, FileTypeConfig
from app.utils.validation import ValidationUtils


class FileType(str, Enum):
    """文件类型枚举"""
    IMAGE = "image"
    DOCUMENT = "document"
    AUDIO = "audio"
    VIDEO = "video"
    AVATAR = "avatar"
    OTHER = "other"


class UploadPurpose(str, Enum):
    """上传目的枚举"""
    AVATAR = "avatar"
    CHAT_FILE = "chat_file"
    PROFILE = "profile"
    ATTACHMENT = "attachment"


class FileUploadRequest(BaseModel):
    """文件上传请求"""
    purpose: UploadPurpose = Field(..., description="上传目的")
    chat_id: Optional[int] = Field(None, description="聊天ID（聊天文件时必需）")
    description: Optional[str] = Field(
        None,
        max_length=ValidationLimits.FILE_DESCRIPTION_MAX_LENGTH,
        description="文件描述"
    )

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        """验证文件描述"""
        if v is not None:
            v = v.strip()
            if ValidationRules.contains_xss_patterns(v):
                raise ValueError("文件描述包含危险字符")
        return v

    @field_validator('chat_id')
    @classmethod
    def validate_chat_id(cls, v):
        """验证聊天ID"""
        if v is not None and v <= 0:
            raise ValueError("无效的聊天ID")
        return v


class FileInfo(BaseModel):
    """文件信息"""
    id: int = Field(..., description="文件ID")
    filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件存储路径")
    file_url: str = Field(..., description="文件访问URL")
    file_type: FileType = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小（字节）")
    mime_type: str = Field(..., description="MIME类型")
    uploader_id: int = Field(..., description="上传者ID")
    purpose: UploadPurpose = Field(..., description="上传目的")
    chat_id: Optional[int] = Field(None, description="关联聊天ID")
    description: Optional[str] = Field(None, description="文件描述")
    is_deleted: bool = Field(False, description="是否已删除")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """文件上传响应"""
    success: bool = Field(..., description="上传是否成功")
    message: str = Field(..., description="响应消息")
    file: Optional[FileInfo] = Field(None, description="文件信息")


class FileListResponse(BaseModel):
    """文件列表响应"""
    files: List[FileInfo] = Field(..., description="文件列表")
    total: int = Field(..., description="总数")
    has_more: bool = Field(False, description="是否还有更多")


class AvatarUploadResponse(BaseModel):
    """头像上传响应"""
    success: bool = Field(..., description="上传是否成功")
    message: str = Field(..., description="响应消息")
    avatar_url: Optional[str] = Field(None, description="头像URL")


class FileValidationError(BaseModel):
    """文件验证错误"""
    field: str = Field(..., description="错误字段")
    message: str = Field(..., description="错误消息")
    code: str = Field(..., description="错误代码")


class FileDeleteRequest(BaseModel):
    """文件删除请求"""
    file_ids: List[int] = Field(..., description="要删除的文件ID列表")
    permanent: bool = Field(False, description="是否永久删除")


class FileSearchRequest(BaseModel):
    """文件搜索请求"""
    query: Optional[str] = Field(None, description="搜索关键词")
    file_type: Optional[FileType] = Field(None, description="文件类型")
    purpose: Optional[UploadPurpose] = Field(None, description="上传目的")
    chat_id: Optional[int] = Field(None, description="聊天ID")
    uploader_id: Optional[int] = Field(None, description="上传者ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    min_size: Optional[int] = Field(None, description="最小文件大小")
    max_size: Optional[int] = Field(None, description="最大文件大小")


class FileStatistics(BaseModel):
    """文件统计信息"""
    total_files: int = Field(0, description="总文件数")
    total_size: int = Field(0, description="总大小（字节）")
    by_type: dict = Field(default_factory=dict, description="按类型统计")
    by_purpose: dict = Field(default_factory=dict, description="按目的统计")
    recent_uploads: int = Field(0, description="最近上传数量")


class FileSecurityCheck(BaseModel):
    """文件安全检查结果"""
    is_safe: bool = Field(..., description="是否安全")
    risk_level: str = Field(..., description="风险等级")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    scan_timestamp: datetime = Field(..., description="扫描时间")


class FileMetadata(BaseModel):
    """文件元数据"""
    width: Optional[int] = Field(None, description="图片宽度")
    height: Optional[int] = Field(None, description="图片高度")
    duration: Optional[float] = Field(None, description="音视频时长（秒）")
    bitrate: Optional[int] = Field(None, description="比特率")
    format_info: Optional[dict] = Field(None, description="格式信息")


class FileWithMetadata(FileInfo):
    """包含元数据的文件信息"""
    metadata: Optional[FileMetadata] = Field(None, description="文件元数据")
    security_check: Optional[FileSecurityCheck] = Field(None, description="安全检查结果")


class ChatFileList(BaseModel):
    """聊天文件列表"""
    chat_id: int = Field(..., description="聊天ID")
    files: List[FileInfo] = Field(..., description="文件列表")
    total: int = Field(..., description="总数")
    total_size: int = Field(0, description="总大小（字节）")


class FileShareRequest(BaseModel):
    """文件分享请求"""
    file_id: int = Field(..., description="文件ID")
    chat_ids: List[int] = Field(..., description="目标聊天ID列表")
    message: Optional[str] = Field(None, max_length=500, description="分享消息")


class FileShareResponse(BaseModel):
    """文件分享响应"""
    success: bool = Field(..., description="分享是否成功")
    message: str = Field(..., description="响应消息")
    shared_count: int = Field(0, description="成功分享的聊天数量")
    failed_chats: List[int] = Field(default_factory=list, description="分享失败的聊天ID")
