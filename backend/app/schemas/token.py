"""Token schemas for authentication."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class Token(BaseModel):
    """Token response schema."""
    
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int  # seconds
    expires_at: datetime


class TokenData(BaseModel):
    """Token payload data schema."""
    
    user_id: int
    username: str
    exp: datetime
    iat: datetime
    token_type: str = "access"  # access or refresh


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""
    
    refresh_token: str


class TokenResponse(BaseModel):
    """Complete token response with user info."""
    
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int
    expires_at: datetime
    user: dict  # User information