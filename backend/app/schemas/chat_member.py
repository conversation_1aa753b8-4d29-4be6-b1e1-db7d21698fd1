"""聊天成员相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from enum import Enum

from .user import User


class ChatMemberRole(str, Enum):
    """聊天成员角色枚举"""
    ADMIN = "admin"
    MEMBER = "member"


class ChatMemberBase(BaseModel):
    """聊天成员基础字段"""
    role: ChatMemberRole = Field(ChatMemberRole.MEMBER, description="成员角色")


class ChatMemberCreate(ChatMemberBase):
    """创建聊天成员"""
    user_id: int = Field(..., description="用户ID")
    chat_id: int = Field(..., description="聊天ID")


class ChatMemberUpdate(BaseModel):
    """更新聊天成员"""
    role: Optional[ChatMemberRole] = Field(None, description="成员角色")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    is_visible: Optional[bool] = Field(None, description="聊天是否可见")
    is_deleted: Optional[bool] = Field(None, description="是否删除聊天")


class ChatMemberInDB(ChatMemberBase):
    """数据库中的聊天成员数据"""
    id: int
    chat_id: int
    user_id: int
    joined_at: datetime
    last_read_at: Optional[datetime]
    is_active: bool
    is_visible: bool
    is_deleted: bool
    
    class Config:
        from_attributes = True


class ChatMember(ChatMemberInDB):
    """返回给客户端的聊天成员数据"""
    pass


class ChatMemberWithUser(ChatMember):
    """包含用户信息的聊天成员"""
    user: User = Field(..., description="用户信息")


class ChatMemberWithChat(ChatMember):
    """包含聊天信息的聊天成员"""
    chat: 'ChatInfo' = Field(..., description="聊天信息")


class ChatInfo(BaseModel):
    """聊天基本信息"""
    id: int
    name: Optional[str]
    type: str
    avatar_url: Optional[str]
    
    class Config:
        from_attributes = True


class ChatMemberAction(BaseModel):
    """聊天成员操作"""
    action: str = Field(..., description="操作类型: add, remove, promote, demote")
    user_ids: list[int] = Field(..., description="目标用户ID列表")


class ChatMemberResponse(BaseModel):
    """聊天成员操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    member: Optional[ChatMember] = Field(None, description="聊天成员数据")


class ChatMemberList(BaseModel):
    """聊天成员列表"""
    members: list[ChatMemberWithUser] = Field(..., description="成员列表")
    total: int = Field(..., description="总数")
    admin_count: int = Field(..., description="管理员数量")
    member_count: int = Field(..., description="普通成员数量")


# 解决循环导入问题
ChatMemberWithChat.model_rebuild()