"""聊天相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from .user import User


class ChatType(str, Enum):
    """聊天类型枚举"""
    PRIVATE = "private"
    GROUP = "group"


class ChatBase(BaseModel):
    """聊天基础字段"""
    name: Optional[str] = Field(None, max_length=100, description="聊天名称")
    description: Optional[str] = Field(None, max_length=500, description="聊天描述")
    avatar_url: Optional[str] = Field(None, description="聊天头像URL")


class ChatCreate(ChatBase):
    """创建聊天"""
    type: ChatType = Field(..., description="聊天类型")
    member_ids: Optional[List[int]] = Field(None, description="初始成员ID列表")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v, info):
        chat_type = info.data.get('type') if info.data else None
        if chat_type == ChatType.GROUP and (not v or len(v.strip()) == 0):
            raise ValueError('群聊必须有名称')
        return v


class ChatUpdate(BaseModel):
    """更新聊天信息"""
    name: Optional[str] = Field(None, max_length=100, description="聊天名称")
    description: Optional[str] = Field(None, max_length=500, description="聊天描述")
    avatar_url: Optional[str] = Field(None, description="聊天头像URL")
    is_active: Optional[bool] = Field(None, description="是否活跃")


class ChatInDB(ChatBase):
    """数据库中的聊天数据"""
    id: int
    type: ChatType
    creator_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Chat(ChatInDB):
    """返回给客户端的聊天数据"""
    pass


class ChatWithMembers(Chat):
    """包含成员信息的聊天"""
    members: List['ChatMemberInfo'] = Field(..., description="聊天成员列表")
    creator: User = Field(..., description="创建者信息")
    member_count: int = Field(..., description="成员数量")


class ChatSummary(BaseModel):
    """聊天摘要信息"""
    id: int
    name: Optional[str]
    type: ChatType
    avatar_url: Optional[str]
    member_count: int
    last_message: Optional['MessageSummary'] = Field(None, description="最后一条消息")
    unread_count: int = Field(0, description="未读消息数")
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ChatMemberRole(str, Enum):
    """聊天成员角色枚举"""
    ADMIN = "admin"
    MEMBER = "member"


class ChatMemberInfo(BaseModel):
    """聊天成员信息"""
    id: int
    user: User = Field(..., description="用户信息")
    role: ChatMemberRole = Field(..., description="成员角色")
    joined_at: datetime = Field(..., description="加入时间")
    last_read_at: Optional[datetime] = Field(None, description="最后阅读时间")
    is_active: bool = Field(..., description="是否活跃")
    
    class Config:
        from_attributes = True


class MessageSummary(BaseModel):
    """消息摘要"""
    id: int
    content: str
    sender: User
    created_at: datetime
    
    class Config:
        from_attributes = True


class ChatResponse(BaseModel):
    """聊天操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    chat: Optional[Chat] = Field(None, description="聊天数据")


# 解决循环导入问题
ChatWithMembers.model_rebuild()
ChatSummary.model_rebuild()