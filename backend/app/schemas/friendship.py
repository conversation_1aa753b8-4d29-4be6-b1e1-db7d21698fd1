"""好友关系相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from .user import User


class FriendshipStatus(str, Enum):
    """好友关系状态枚举"""
    PENDING = "pending"
    ACCEPTED = "accepted"
    BLOCKED = "blocked"


class FriendshipBase(BaseModel):
    """好友关系基础字段"""
    request_message: Optional[str] = Field(None, max_length=500, description="好友请求消息")


class FriendshipCreate(FriendshipBase):
    """创建好友关系"""
    friend_id: int = Field(..., description="好友用户ID")
    
    @field_validator('request_message')
    @classmethod
    def validate_request_message(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v


class FriendshipUpdate(BaseModel):
    """更新好友关系"""
    status: FriendshipStatus = Field(..., description="好友关系状态")


class FriendshipInDB(FriendshipBase):
    """数据库中的好友关系数据"""
    id: int
    user_id: int
    friend_id: int
    status: FriendshipStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Friendship(FriendshipInDB):
    """返回给客户端的好友关系数据"""
    pass


class FriendshipWithUser(Friendship):
    """包含用户信息的好友关系"""
    user: User = Field(..., description="发起好友请求的用户")
    friend: User = Field(..., description="被请求的好友用户")


class FriendRequest(BaseModel):
    """好友请求"""
    id: int
    user: User = Field(..., description="发起请求的用户")
    request_message: Optional[str] = Field(None, description="请求消息")
    created_at: datetime = Field(..., description="请求时间")
    
    class Config:
        from_attributes = True


class FriendList(BaseModel):
    """好友列表项"""
    id: int
    friend: User = Field(..., description="好友用户信息")
    created_at: datetime = Field(..., description="成为好友的时间")
    
    class Config:
        from_attributes = True


class FriendshipResponse(BaseModel):
    """好友关系操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    friendship: Optional[Friendship] = Field(None, description="好友关系数据")


class BlockedUser(BaseModel):
    """被屏蔽的用户"""
    id: int
    user: User = Field(..., description="被屏蔽的用户信息")
    blocked_at: datetime = Field(..., description="屏蔽时间")
    
    class Config:
        from_attributes = True