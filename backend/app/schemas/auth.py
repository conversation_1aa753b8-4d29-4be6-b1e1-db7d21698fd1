"""认证相关的数据模式

定义用户注册、登录、令牌等认证相关的请求和响应模式。
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, EmailStr, field_validator
import re


class UserRegisterRequest(BaseModel):
    """用户注册请求模式"""
    
    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="用户名，3-50个字符"
    )
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="密码，至少8个字符"
    )
    email: Optional[EmailStr] = Field(
        None,
        description="邮箱地址（可选）"
    )
    nickname: Optional[str] = Field(
        None,
        max_length=100,
        description="昵称（可选）"
    )
    
    @field_validator('username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        """验证用户名格式"""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v
    
    @field_validator('nickname')
    @classmethod
    def validate_nickname(cls, v: Optional[str]) -> Optional[str]:
        """验证昵称格式"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
        return v


class UserLoginRequest(BaseModel):
    """用户登录请求模式"""
    
    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="用户名"
    )
    password: str = Field(
        ...,
        min_length=1,
        max_length=128,
        description="密码"
    )


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求模式"""
    
    refresh_token: str = Field(
        ...,
        description="刷新令牌"
    )


class UserProfileResponse(BaseModel):
    """用户信息响应模式"""
    
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: Optional[str] = Field(None, description="邮箱地址")
    nickname: str = Field(..., description="昵称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    is_active: bool = Field(..., description="是否活跃")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class UserRegisterResponse(BaseModel):
    """用户注册响应模式"""
    
    message: str = Field(..., description="响应消息")
    user: UserProfileResponse = Field(..., description="用户信息")
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")


class UserLoginResponse(BaseModel):
    """用户登录响应模式"""
    
    message: str = Field(..., description="响应消息")
    user: UserProfileResponse = Field(..., description="用户信息")
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应模式"""
    
    message: str = Field(..., description="响应消息")
    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")


class PasswordChangeRequest(BaseModel):
    """密码修改请求模式"""
    
    current_password: str = Field(
        ...,
        min_length=1,
        max_length=128,
        description="当前密码"
    )
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="新密码，至少8个字符"
    )


class UserUpdateRequest(BaseModel):
    """用户信息更新请求模式"""
    
    nickname: Optional[str] = Field(
        None,
        max_length=100,
        description="昵称"
    )
    email: Optional[EmailStr] = Field(
        None,
        description="邮箱地址"
    )
    
    @field_validator('nickname')
    @classmethod
    def validate_nickname(cls, v: Optional[str]) -> Optional[str]:
        """验证昵称格式"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
        return v


class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""
    
    email: EmailStr = Field(
        ...,
        description="邮箱地址"
    )


class PasswordResetConfirmRequest(BaseModel):
    """密码重置确认请求模式"""
    
    token: str = Field(
        ...,
        description="重置令牌"
    )
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="新密码，至少8个字符"
    )
