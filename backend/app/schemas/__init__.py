"""Pydantic schemas package."""

from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserInDB,
    User,
    User<PERSON>ogin,
    UserRegister,
)
from .friendship import (
    FriendshipBase,
    FriendshipCreate,
    FriendshipUpdate,
    Friendship,
    FriendRequest,
)
from .chat import (
    Chat<PERSON><PERSON>,
    Chat<PERSON><PERSON>,
    Chat<PERSON><PERSON><PERSON>,
    Chat,
    ChatWithMembers,
)
from .chat_member import (
    ChatMemberBase,
    ChatMemberCreate,
    ChatMemberUpdate,
    ChatMember,
)
from .message import (
    MessageBase,
    MessageCreate,
    MessageUpdate,
    Message,
)
from .message_status import (
    MessageStatusBase,
    MessageStatusCreate,
    MessageStatusUpdate,
    MessageStatus,
)
from .file import (
    FileType,
    UploadPurpose,
    FileUploadRequest,
    FileInfo,
    FileUploadResponse,
    FileListResponse,
    AvatarUploadResponse,
    FileDeleteRequest,
    FileSearchRequest,
    FileStatistics,
    FileSecurityCheck,
    FileMetadata,
    FileWithMetadata,
    ChatFileList,
    FileShareRequest,
    FileShareResponse,
)
from .token import Token, TokenData
from .response import APIResponse, PaginatedResponse

__all__ = [
    # User schemas
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserInDB",
    "User",
    "UserLogin",
    "UserRegister",
    # Friendship schemas
    "FriendshipBase",
    "FriendshipCreate",
    "FriendshipUpdate",
    "Friendship",
    "FriendRequest",
    # Chat schemas
    "ChatBase",
    "ChatCreate",
    "ChatUpdate",
    "Chat",
    "ChatWithMembers",
    # Chat member schemas
    "ChatMemberBase",
    "ChatMemberCreate",
    "ChatMemberUpdate",
    "ChatMember",
    # Message schemas
    "MessageBase",
    "MessageCreate",
    "MessageUpdate",
    "Message",
    # Message status schemas
    "MessageStatusBase",
    "MessageStatusCreate",
    "MessageStatusUpdate",
    "MessageStatus",
    # File schemas
    "FileType",
    "UploadPurpose",
    "FileUploadRequest",
    "FileInfo",
    "FileUploadResponse",
    "FileListResponse",
    "AvatarUploadResponse",
    "FileDeleteRequest",
    "FileSearchRequest",
    "FileStatistics",
    "FileSecurityCheck",
    "FileMetadata",
    "FileWithMetadata",
    "ChatFileList",
    "FileShareRequest",
    "FileShareResponse",
    # Auth schemas
    "Token",
    "TokenData",
    # Response schemas
    "APIResponse",
    "PaginatedResponse",
]