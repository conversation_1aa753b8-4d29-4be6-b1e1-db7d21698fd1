"""WebSocket相关的 Pydantic schemas"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class MessageType(str, Enum):
    """消息类型枚举"""
    # 连接管理
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    HEARTBEAT = "heartbeat"
    
    # 聊天消息
    CHAT_MESSAGE = "chat_message"
    PRIVATE_MESSAGE = "private_message"
    MESSAGE_STATUS = "message_status"
    TYPING_STATUS = "typing_status"
    
    # 用户状态
    USER_ONLINE = "user_online"
    USER_OFFLINE = "user_offline"
    USER_STATUS = "user_status"
    
    # 好友相关
    FRIEND_REQUEST = "friend_request"
    FRIEND_ACCEPTED = "friend_accepted"
    FRIEND_REMOVED = "friend_removed"
    
    # 聊天相关
    CHAT_CREATED = "chat_created"
    CHAT_UPDATED = "chat_updated"
    CHAT_MEMBER_ADDED = "chat_member_added"
    CHAT_MEMBER_REMOVED = "chat_member_removed"
    
    # 系统消息
    SYSTEM_NOTIFICATION = "system_notification"
    BADGE_COUNT = "badge_count"
    ERROR = "error"


class OnlineStatus(str, Enum):
    """在线状态枚举"""
    ONLINE = "online"
    AWAY = "away"
    BUSY = "busy"
    INVISIBLE = "invisible"
    OFFLINE = "offline"


class WebSocketMessage(BaseModel):
    """WebSocket消息基础结构"""
    type: MessageType = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(default_factory=dict, description="消息数据")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    message_id: Optional[str] = Field(None, description="消息ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "type": "chat_message",
                "data": {
                    "chat_id": 1,
                    "content": "Hello, World!",
                    "sender_id": 123
                },
                "timestamp": "2024-01-01T00:00:00Z",
                "message_id": "msg_123456"
            }
        }


class HeartbeatMessage(BaseModel):
    """心跳消息"""
    type: MessageType = Field(default=MessageType.HEARTBEAT, description="消息类型")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    
    class Config:
        json_schema_extra = {
            "example": {
                "type": "heartbeat",
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }


class ChatMessageData(BaseModel):
    """聊天消息数据"""
    chat_id: int = Field(..., description="聊天ID")
    content: str = Field(..., max_length=2000, description="消息内容")
    sender_id: int = Field(..., description="发送者ID")
    message_type: str = Field(default="text", description="消息类型")
    reply_to_id: Optional[int] = Field(None, description="回复消息ID")
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        """验证消息内容"""
        if not v or not v.strip():
            raise ValueError("消息内容不能为空")
        return v.strip()


class TypingStatusData(BaseModel):
    """输入状态数据"""
    chat_id: int = Field(..., description="聊天ID")
    user_id: int = Field(..., description="用户ID")
    is_typing: bool = Field(..., description="是否正在输入")


class UserStatusData(BaseModel):
    """用户状态数据"""
    user_id: int = Field(..., description="用户ID")
    status: OnlineStatus = Field(..., description="在线状态")
    last_seen: Optional[datetime] = Field(None, description="最后在线时间")
    custom_message: Optional[str] = Field(None, description="自定义状态消息")


class MessageStatusData(BaseModel):
    """消息状态数据"""
    message_id: int = Field(..., description="消息ID")
    chat_id: int = Field(..., description="聊天ID")
    status: str = Field(..., description="消息状态")  # sent, delivered, read
    user_id: int = Field(..., description="用户ID")


class SystemNotificationData(BaseModel):
    """系统通知数据"""
    title: str = Field(..., max_length=100, description="通知标题")
    content: str = Field(..., max_length=500, description="通知内容")
    level: str = Field(default="info", description="通知级别")  # info, warning, error
    action_url: Optional[str] = Field(None, description="操作链接")


class ErrorData(BaseModel):
    """错误数据"""
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class BadgeCountData(BaseModel):
    """徽标数量数据"""
    user_id: int = Field(..., description="用户ID")
    unread_count: int = Field(..., description="未读消息数量")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")


class ConnectionInfo(BaseModel):
    """连接信息"""
    user_id: int = Field(..., description="用户ID")
    connection_id: str = Field(..., description="连接ID")
    connected_at: datetime = Field(default_factory=datetime.utcnow, description="连接时间")
    last_heartbeat: datetime = Field(default_factory=datetime.utcnow, description="最后心跳时间")
    user_agent: Optional[str] = Field(None, description="用户代理")
    ip_address: Optional[str] = Field(None, description="IP地址")


class ConnectionStats(BaseModel):
    """连接统计"""
    total_connections: int = Field(..., description="总连接数")
    active_users: int = Field(..., description="活跃用户数")
    connections_by_status: Dict[str, int] = Field(default_factory=dict, description="按状态分组的连接数")
    uptime_seconds: float = Field(..., description="运行时间（秒）")


class BroadcastRequest(BaseModel):
    """广播请求"""
    message: WebSocketMessage = Field(..., description="要广播的消息")
    target_users: Optional[List[int]] = Field(None, description="目标用户ID列表，为空则广播给所有用户")
    exclude_users: Optional[List[int]] = Field(None, description="排除的用户ID列表")
    chat_id: Optional[int] = Field(None, description="聊天ID，仅发送给聊天成员")


class WebSocketResponse(BaseModel):
    """WebSocket响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")


# 消息类型到数据类的映射
MESSAGE_DATA_MAPPING = {
    MessageType.CHAT_MESSAGE: ChatMessageData,
    MessageType.TYPING_STATUS: TypingStatusData,
    MessageType.USER_STATUS: UserStatusData,
    MessageType.MESSAGE_STATUS: MessageStatusData,
    MessageType.SYSTEM_NOTIFICATION: SystemNotificationData,
    MessageType.BADGE_COUNT: BadgeCountData,
    MessageType.ERROR: ErrorData,
    MessageType.HEARTBEAT: HeartbeatMessage,
}


def create_websocket_message(
    message_type: MessageType,
    data: Any = None,
    message_id: Optional[str] = None
) -> WebSocketMessage:
    """创建WebSocket消息"""
    return WebSocketMessage(
        type=message_type,
        data=data or {},
        timestamp=datetime.utcnow(),
        message_id=message_id
    )


def validate_message_data(message_type: MessageType, data: Dict[str, Any]) -> Any:
    """验证消息数据"""
    data_class = MESSAGE_DATA_MAPPING.get(message_type)
    if data_class:
        return data_class.model_validate(data)
    return data


# 导出所有schemas
__all__ = [
    'MessageType',
    'OnlineStatus', 
    'WebSocketMessage',
    'HeartbeatMessage',
    'ChatMessageData',
    'TypingStatusData',
    'UserStatusData',
    'MessageStatusData',
    'SystemNotificationData',
    'ErrorData',
    'ConnectionInfo',
    'ConnectionStats',
    'BroadcastRequest',
    'WebSocketResponse',
    'MESSAGE_DATA_MAPPING',
    'create_websocket_message',
    'validate_message_data'
]
