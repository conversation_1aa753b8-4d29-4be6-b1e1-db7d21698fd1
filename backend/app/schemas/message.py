"""消息相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from .user import User


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"


class MessageBase(BaseModel):
    """消息基础字段"""
    content: str = Field(..., max_length=10000, description="消息内容")
    type: MessageType = Field(MessageType.TEXT, description="消息类型")
    reply_to_id: Optional[int] = Field(None, description="回复的消息ID")


class MessageCreate(MessageBase):
    """创建消息"""
    chat_id: int = Field(..., description="聊天ID")
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('消息内容不能为空')
        return v.strip()


class MessageUpdate(BaseModel):
    """更新消息"""
    content: Optional[str] = Field(None, max_length=10000, description="消息内容")
    is_deleted: Optional[bool] = Field(None, description="是否删除")


class MessageInDB(MessageBase):
    """数据库中的消息数据"""
    id: int
    chat_id: int
    sender_id: int
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Message(MessageInDB):
    """返回给客户端的消息数据"""
    pass


class MessageWithSender(Message):
    """包含发送者信息的消息"""
    sender: User = Field(..., description="发送者信息")
    reply_to: Optional['MessageSummary'] = Field(None, description="回复的消息")
    read_count: int = Field(0, description="已读人数")
    delivered_count: int = Field(0, description="已送达人数")


class MessageSummary(BaseModel):
    """消息摘要"""
    id: int
    content: str
    type: MessageType
    sender: User
    created_at: datetime
    
    class Config:
        from_attributes = True


class MessageList(BaseModel):
    """消息列表"""
    messages: List[MessageWithSender] = Field(..., description="消息列表")
    total: int = Field(..., description="总数")
    has_more: bool = Field(..., description="是否还有更多")
    next_cursor: Optional[str] = Field(None, description="下一页游标")


class MessageResponse(BaseModel):
    """消息操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Message] = Field(None, description="消息数据")


class MessageSearch(BaseModel):
    """消息搜索"""
    query: str = Field(..., min_length=1, max_length=100, description="搜索关键词")
    chat_id: Optional[int] = Field(None, description="限制在特定聊天中搜索")
    sender_id: Optional[int] = Field(None, description="限制特定发送者")
    message_type: Optional[MessageType] = Field(None, description="消息类型")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")


class MessageSearchResult(BaseModel):
    """消息搜索结果"""
    messages: List[MessageWithSender] = Field(..., description="搜索到的消息")
    total: int = Field(..., description="总数")
    query: str = Field(..., description="搜索关键词")


class MessageReaction(BaseModel):
    """消息反应"""
    message_id: int = Field(..., description="消息ID")
    user_id: int = Field(..., description="用户ID")
    emoji: str = Field(..., max_length=10, description="表情符号")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class MessageWithReactions(MessageWithSender):
    """包含反应的消息"""
    reactions: List[MessageReaction] = Field([], description="消息反应列表")


# 解决循环导入问题
MessageWithSender.model_rebuild()