"""Response schemas for API responses."""

from typing import Any, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

T = TypeVar('T')


class APIResponse(BaseModel, Generic[T]):
    """Generic API response schema."""

    success: bool = True
    message: str = "Success"
    data: Optional[T] = None
    error_code: Optional[str] = None
    timestamp: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Operation completed successfully",
                "data": {},
                "error_code": None,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }


class ErrorResponse(BaseModel):
    """Error response schema."""
    
    success: bool = False
    message: str
    error_code: str
    details: Optional[dict] = None
    timestamp: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": False,
                "message": "Validation error",
                "error_code": "VALIDATION_ERROR",
                "details": {"field": "username", "error": "Username is required"},
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }


class PaginationMeta(BaseModel):
    """Pagination metadata schema."""
    
    page: int = Field(..., ge=1, description="Current page number")
    per_page: int = Field(..., ge=1, le=100, description="Items per page")
    total: int = Field(..., ge=0, description="Total number of items")
    pages: int = Field(..., ge=0, description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "per_page": 20,
                "total": 100,
                "pages": 5,
                "has_next": True,
                "has_prev": False
            }
        }


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response schema."""

    success: bool = True
    message: str = "Success"
    data: List[T] = []
    meta: PaginationMeta
    error_code: Optional[str] = None
    timestamp: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Data retrieved successfully",
                "data": [],
                "meta": {
                    "page": 1,
                    "per_page": 20,
                    "total": 100,
                    "pages": 5,
                    "has_next": True,
                    "has_prev": False
                },
                "error_code": None,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }


class SuccessResponse(BaseModel):
    """Simple success response schema."""
    
    success: bool = True
    message: str = "Operation completed successfully"
    timestamp: Optional[str] = None


class ValidationErrorDetail(BaseModel):
    """Validation error detail schema."""
    
    field: str
    message: str
    value: Any = None


class ValidationErrorResponse(BaseModel):
    """Validation error response schema."""
    
    success: bool = False
    message: str = "Validation failed"
    error_code: str = "VALIDATION_ERROR"
    errors: List[ValidationErrorDetail]
    timestamp: Optional[str] = None