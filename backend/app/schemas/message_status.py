"""消息状态相关的 Pydantic schemas"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum

from .user import User


class MessageStatusType(str, Enum):
    """消息状态类型枚举"""
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"


class MessageStatusBase(BaseModel):
    """消息状态基础字段"""
    status: MessageStatusType = Field(..., description="消息状态")
    timestamp: datetime = Field(..., description="状态时间戳")


class MessageStatusCreate(MessageStatusBase):
    """创建消息状态"""
    message_id: int = Field(..., description="消息ID")
    user_id: int = Field(..., description="用户ID")


class MessageStatusUpdate(BaseModel):
    """更新消息状态"""
    status: MessageStatusType = Field(..., description="消息状态")
    timestamp: Optional[datetime] = Field(None, description="状态时间戳")


class MessageStatusInDB(MessageStatusBase):
    """数据库中的消息状态数据"""
    message_id: int
    user_id: int
    
    class Config:
        from_attributes = True


class MessageStatus(MessageStatusInDB):
    """返回给客户端的消息状态数据"""
    pass


class MessageStatusWithUser(MessageStatus):
    """包含用户信息的消息状态"""
    user: User = Field(..., description="用户信息")


class MessageStatusSummary(BaseModel):
    """消息状态摘要"""
    message_id: int = Field(..., description="消息ID")
    sent_count: int = Field(0, description="已发送数量")
    delivered_count: int = Field(0, description="已送达数量")
    read_count: int = Field(0, description="已读数量")
    total_recipients: int = Field(0, description="总接收者数量")


class MessageStatusList(BaseModel):
    """消息状态列表"""
    statuses: List[MessageStatusWithUser] = Field(..., description="状态列表")
    summary: MessageStatusSummary = Field(..., description="状态摘要")


class MessageStatusResponse(BaseModel):
    """消息状态操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    status: Optional[MessageStatus] = Field(None, description="消息状态数据")


class MessageReadReceipt(BaseModel):
    """消息已读回执"""
    message_id: int = Field(..., description="消息ID")
    user: User = Field(..., description="已读用户")
    read_at: datetime = Field(..., description="已读时间")
    
    class Config:
        from_attributes = True


class MessageDeliveryReceipt(BaseModel):
    """消息送达回执"""
    message_id: int = Field(..., description="消息ID")
    user: User = Field(..., description="送达用户")
    delivered_at: datetime = Field(..., description="送达时间")
    
    class Config:
        from_attributes = True


class BulkMessageStatusUpdate(BaseModel):
    """批量更新消息状态"""
    message_ids: List[int] = Field(..., description="消息ID列表")
    status: MessageStatusType = Field(..., description="目标状态")
    user_id: Optional[int] = Field(None, description="用户ID（如果为空则为当前用户）")


class ChatUnreadCount(BaseModel):
    """聊天未读数量"""
    chat_id: int = Field(..., description="聊天ID")
    unread_count: int = Field(..., description="未读消息数量")
    last_read_message_id: Optional[int] = Field(None, description="最后已读消息ID")
    last_message_id: Optional[int] = Field(None, description="最新消息ID")


class UserUnreadSummary(BaseModel):
    """用户未读摘要"""
    total_unread: int = Field(..., description="总未读数量")
    chat_unreads: List[ChatUnreadCount] = Field(..., description="各聊天未读数量")
    updated_at: datetime = Field(..., description="更新时间")