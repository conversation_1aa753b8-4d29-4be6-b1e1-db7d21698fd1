{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "lucide-vue-next": "^0.535.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "sass": "^1.89.2", "vue": "^3.5.17", "vue-router": "4"}, "devDependencies": {"@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-vue": "^10.4.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.6.2", "prettier-plugin-vue": "^1.1.6", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}