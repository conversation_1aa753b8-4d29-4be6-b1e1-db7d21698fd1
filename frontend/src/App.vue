<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { MessageCircle, Users } from 'lucide-vue-next'
import { useWebSocket } from '@/services/websocket'
import { useUserStore } from '@/stores/user'
import { useChatStore } from '@/stores/chat'

const route = useRoute()
const userStore = useUserStore()
const chatStore = useChatStore()
const webSocketService = useWebSocket()

// 使用chatStore的totalUnreadCount
const unreadCount = computed(() => chatStore.totalUnreadCount)

// 需要显示底部导航的页面
const showTabBar = computed(() => {
  const tabRoutes = ['/chat', '/contacts']
  return tabRoutes.includes(route.path)
})

// 监听用户登录状态，自动连接/断开WebSocket
watch(() => userStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    // 用户已登录，连接WebSocket
    console.log('用户已登录，连接WebSocket')
    webSocketService.connect()
  } else {
    // 用户未登录，断开WebSocket
    console.log('用户未登录，断开WebSocket')
    webSocketService.disconnect()
  }
})

// 组件挂载时检查登录状态并连接WebSocket
onMounted(() => {
  if (userStore.isLoggedIn) {
    webSocketService.connect()
  }
})

// 组件卸载时断开WebSocket连接
onUnmounted(() => {
  webSocketService.disconnect()
})

</script>

<template>
  <div id="app">
    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'has-bottom-nav': showTabBar }">
      <router-view v-slot="{ Component }">
        <KeepAlive :include="['ChatList', 'ContactList', 'ChatDetail']">
          <component :is="Component" />
        </KeepAlive>
      </router-view>
    </div>
    
    <!-- 底部导航栏 -->
    <nav v-if="showTabBar" class="bottom-navigation">
      <router-link to="/chat" class="nav-item" :class="{ active: $route.path === '/chat' }">
        <div class="nav-icon">
          <MessageCircle />
        </div>
        <span class="nav-label">Chats</span>
        <div v-if="unreadCount > 0" class="badge">{{ unreadCount }}</div>
      </router-link>
      <router-link to="/contacts" class="nav-item" :class="{ active: $route.path === '/contacts' }">
        <div class="nav-icon">
          <Users />
        </div>
        <span class="nav-label">Contacts</span>
      </router-link>
    </nav>
  </div>
</template>

<style scoped>
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-secondary);
  position: relative;
  overflow: hidden;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  overflow: hidden;
}

/* 有底部导航栏时为主内容添加底部间距 */
.main-content.has-bottom-nav {
  padding-bottom: var(--space-20);
}

.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--space-20);
  background: var(--color-background-primary);
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: var(--z-index-fixed);
  box-shadow: var(--shadow-lg);
}

.nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--color-text-secondary);
  padding: var(--space-3) var(--space-5);
  transition: all var(--duration-normal) var(--ease-out);
  border-radius: var(--radius-lg);
  min-width: var(--space-15);
}

.nav-item.active {
  color: var(--color-primary-500);
  background: var(--color-primary-50);
}

.nav-item:hover {
  color: var(--color-primary-500);
  transform: translateY(-2px);
}

.nav-icon {
  margin-bottom: var(--space-1);
  transition: transform var(--duration-normal) var(--ease-out);
}

.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
}

.badge {
  position: absolute;
  top: var(--space-2);
  right: var(--space-3);
  background: var(--color-error);
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  min-width: var(--space-4);
  height: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

/* 确保页面内容不被底部导航栏遮挡 */
:deep(.main-content > *) {
  height: 100%;
  overflow-y: auto;
}

/* 图标样式 */
.nav-icon svg {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease-in-out;
}

.nav-item:hover .nav-icon svg {
  width: 26px;
  height: 26px;
}

.nav-item.active .nav-icon svg {
  width: 26px;
  height: 26px;
}

</style>
