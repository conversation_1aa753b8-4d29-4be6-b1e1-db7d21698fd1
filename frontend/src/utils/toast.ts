import { createApp, type App } from 'vue'
import ToastContainer from '../components/ToastContainer.vue'
import type { ToastItem } from '../components/ToastContainer.vue'

export interface ToastProps {
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  closable?: boolean
  onClick?: () => void
  onClose?: () => void
}

class ToastManager {
  private containerApp: App | null = null
  private containerRef: any = null
  private isInitialized = false

  private initContainer() {
    if (this.isInitialized) return

    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建ToastContainer应用实例
    this.containerApp = createApp(ToastContainer)
    this.containerRef = this.containerApp.mount(container)
    this.isInitialized = true
  }

  private createToast(options: ToastProps): Promise<void> {
    return new Promise((resolve) => {
      this.initContainer()
      
      const toastItem: Omit<ToastItem, 'id'> = {
        message: options.message,
        type: options.type || 'info',
        duration: options.duration ?? 3000,
        closable: options.closable ?? true,
        onClick: options.onClick,
        onClose: () => {
          options.onClose?.()
          resolve()
        }
      }
      
      this.containerRef?.addToast(toastItem)
    })
  }

  // 显示成功提示
  success(message: string, options?: Omit<ToastProps, 'message' | 'type'>): Promise<void> {
    return this.createToast({
      message,
      type: 'success',
      ...options
    })
  }

  // 显示错误提示
  error(message: string, options?: Omit<ToastProps, 'message' | 'type'>): Promise<void> {
    return this.createToast({
      message,
      type: 'error',
      duration: 4000, // 错误消息显示时间稍长
      ...options
    })
  }

  // 显示警告提示
  warning(message: string, options?: Omit<ToastProps, 'message' | 'type'>): Promise<void> {
    return this.createToast({
      message,
      type: 'warning',
      ...options
    })
  }

  // 显示信息提示
  info(message: string, options?: Omit<ToastProps, 'message' | 'type'>): Promise<void> {
    return this.createToast({
      message,
      type: 'info',
      ...options
    })
  }

  // 显示自定义提示
  show(options: ToastProps): Promise<void> {
    return this.createToast(options)
  }

  // 清除所有Toast
  clear(): void {
    if (this.containerRef) {
      this.containerRef.clearAll()
    }
  }
}

// 创建全局实例
const toastManager = new ToastManager()

// 导出函数式API
export const toast = {
  success: (message: string, options?: Omit<ToastProps, 'message' | 'type'>) => 
    toastManager.success(message, options),
  
  error: (message: string, options?: Omit<ToastProps, 'message' | 'type'>) => 
    toastManager.error(message, options),
  
  warning: (message: string, options?: Omit<ToastProps, 'message' | 'type'>) => 
    toastManager.warning(message, options),
  
  info: (message: string, options?: Omit<ToastProps, 'message' | 'type'>) => 
    toastManager.info(message, options),
  
  show: (options: ToastProps) => 
    toastManager.show(options),
  
  clear: () => 
    toastManager.clear()
}

// 默认导出
export default toast

// 便捷方法导出
export const showToast = toast.show
export const showSuccess = toast.success
export const showError = toast.error
export const showWarning = toast.warning
export const showInfo = toast.info
export const clearToasts = toast.clear