<template>
  <div class="chat-list">
    <!-- 应用头部栏 -->
    <AppBar
      title="Chats"
      :actions="appBarActions"
      @action="handleAppBarAction"
    />

    <!-- 搜索栏 -->
    <div class="search-section">
      <SearchBar
        v-model="searchQuery"
        placeholder="Search chats..."
        @search="handleSearch"
      />
    </div>

    <!-- 聊天列表 -->
    <div class="chat-content">
      <!-- 空状态 -->
      <EmptyState
        v-if="chatList.length === 0"
        :icon="MessageCircle"
        title="No chats yet"
        description="Start a conversation with your contacts"
        action-text="Start Chat"
        @action="handleStartChat"
      />

      <!-- 聊天列表 -->
      <div v-else class="chat-items">
        <ListItem
          v-for="chat in filteredChatList"
          :key="chat.id"
          :avatar="{
            src: chat.avatar,
            name: chat.name,
            isOnline: chat.isOnline
          }"
          :title="chat.name"
          :subtitle="chat.lastMessage"
          :meta="formatTime(chat.lastMessageTime)"
          :badge="chat.unreadCount > 0 ? {
            count: chat.unreadCount,
            variant: 'primary'
          } : undefined"
          @click="openChat(chat)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, MessageCircle } from 'lucide-vue-next'
import {
  AppBar,
  SearchBar,
  EmptyState,
  ListItem,
  type AppBarAction
} from '@/components'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'ChatList'
})

const router = useRouter()

// 搜索相关
const searchQuery = ref('')

// AppBar 操作
const appBarActions: AppBarAction[] = [
  {
    icon: Plus,
    onClick: () => console.log('添加聊天'),
    key: 'add'
  }
]

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑将在这里实现
}

// 处理 AppBar 操作
const handleAppBarAction = (key: string, action: AppBarAction) => {
  console.log('AppBar action:', key, action)
}

// 处理开始聊天
const handleStartChat = () => {
  router.push('/contacts')
}

// 过滤后的聊天列表
const filteredChatList = computed(() => {
  if (!searchQuery.value.trim()) {
    return chatList.value
  }
  return chatList.value.filter(chat => 
    chat.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 聊天列表数据
const chatList = ref([
  {
    id: 1,
    name: 'Alice Johnson',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'Hey! How are you doing?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    unreadCount: 2,
    isOnline: true,
    showInitials: false
  },
  {
    id: 2,
    name: 'Bob Smith',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'See you tomorrow!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    unreadCount: 0,
    isOnline: false,
    showInitials: false
  },
  {
    id: 3,
    name: 'Carol Davis',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'Thanks for your help!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    unreadCount: 1,
    isOnline: true,
    showInitials: false
  }
])

// 打开聊天
const openChat = (chat: any) => {
  router.push(`/chat/${chat.id}`)
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return 'now'
  if (minutes < 60) return `${minutes}m`
  if (hours < 24) return `${hours}h`
  if (days < 7) return `${days}d`
  
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}


</script>

<style scoped>
.chat-list {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.chat-content {
  flex: 1;
  overflow-y: auto;
}

.chat-items {
  padding: var(--space-2) 0;
}



</style>