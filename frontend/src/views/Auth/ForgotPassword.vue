<template>
  <div class="forgot-password">
    <!-- 顶部导航栏 -->
    <header class="app-bar">
      <div class="header-content">
        <button class="back-btn" @click="$router.back()">
          <ChevronLeft :size="24" />
        </button>
        <h1>忘记密码</h1>
        <div class="header-spacer"></div>
      </div>
    </header>

    <!-- 内容区域 -->
    <div class="forgot-content">
      <!-- 说明信息 -->
      <div class="info-section">
        <div class="info-icon">
          <KeyRound :size="48" />
        </div>
        <h2 class="info-title">重置密码</h2>
        <p class="info-subtitle">
          请输入您的邮箱地址，我们将向您发送重置密码的链接
        </p>
      </div>

      <!-- 表单 -->
      <div class="form-section">
        <form @submit.prevent="handleSubmit" class="forgot-form">
          <div class="form-group">
            <div class="input-container">
              <AtSign class="input-icon" />
              <input 
                type="email" 
                class="form-input" 
                placeholder="请输入邮箱地址"
                v-model="email"
                required
              />
            </div>
          </div>

          <button type="submit" class="submit-btn" :disabled="isLoading || !email">
            <span v-if="!isLoading">发送重置链接</span>
            <span v-else class="loading-text">
              <div class="loading-spinner"></div>
              发送中...
            </span>
          </button>
        </form>

        <!-- 返回登录 -->
        <div class="back-section">
          <p class="back-text">
            想起密码了？
            <router-link to="/login" class="back-link">
              返回登录
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, KeyRound, AtSign } from 'lucide-vue-next'
import { showSuccess, showError, showWarning } from '@/utils'

const router = useRouter()

// 表单数据
const email = ref('')
const isLoading = ref(false)

// 提交处理
const handleSubmit = async () => {
  if (!email.value.trim()) {
    showWarning('请输入邮箱地址')
    return
  }

  isLoading.value = true

  try {
    // TODO: 调用忘记密码API
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
    
    showSuccess('重置链接已发送到您的邮箱，请查收')
    
    // 发送成功后返回登录页面
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (error) {
    console.error('发送重置链接失败:', error)
    showError('发送失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.forgot-password {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.back-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.app-bar h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
}

.header-spacer {
  width: var(--button-height-sm);
}

.forgot-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-8) var(--space-4);
  overflow-y: auto;
}

.info-section {
  text-align: center;
  margin-bottom: var(--space-10);
}

.info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-20);
  height: var(--space-20);
  background: var(--color-warning);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-6);
}

.info-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-4) 0;
  letter-spacing: var(--letter-spacing-tight);
}

.info-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.form-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.forgot-form {
  margin-bottom: var(--space-8);
}

.form-group {
  margin-bottom: var(--space-6);
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  color: var(--color-text-tertiary);
  z-index: 1;
  width: 20px;
  height: 20px;
}

.form-input {
  width: 100%;
  height: var(--space-12);
  padding: 0 var(--space-4) 0 var(--space-12);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  background: var(--color-background-secondary);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  outline: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.form-input::placeholder {
  color: var(--color-text-tertiary);
}

.form-input:focus {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.submit-btn {
  width: 100%;
  height: var(--space-12);
  background: var(--color-warning);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.submit-btn:hover:not(:disabled) {
  background: var(--color-warning-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.loading-spinner {
  width: var(--space-4);
  height: var(--space-4);
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.back-section {
  text-align: center;
}

.back-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.back-link {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--duration-fast) var(--ease-out);
}

.back-link:hover {
  color: var(--color-primary-600);
}
</style>
