<template>
  <div class="login">
    <!-- 顶部导航栏 -->
    <AppBar title="登录" />

    <!-- 登录内容区域 -->
    <div class="login-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="app-logo">
          <div class="logo-icon">
            <MessageCircle :size="48" />
          </div>
        </div>
        <h2 class="welcome-title">欢迎回来</h2>
        <p class="welcome-subtitle">登录您的账户继续聊天</p>
      </div>

      <!-- 登录表单 -->
      <div class="form-section">
        <form @submit.prevent="handleLogin" class="login-form">
          <FormGroup label="用户名或邮箱" required>
            <Input
              v-model="loginForm.username"
              :prefix-icon="User"
              placeholder="请输入用户名或邮箱"
              :status="usernameStatus"
              :error-message="usernameError"
              autofocus
            />
          </FormGroup>

          <FormGroup label="密码" required>
            <Input
              v-model="loginForm.password"
              type="password"
              :prefix-icon="Lock"
              placeholder="请输入密码"
              show-password-toggle
              :status="passwordStatus"
              :error-message="passwordError"
            />
          </FormGroup>

          <div class="form-options">
            <Checkbox
              v-model="loginForm.rememberMe"
              label="记住我"
            />
            <router-link to="/forgot-password" class="forgot-link">
              忘记密码？
            </router-link>
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            block
            :loading="isLoading"
            :disabled="!isFormValid"
          >
            登录
          </Button>
        </form>

        <!-- 注册链接 -->
        <div class="register-section">
          <p class="register-text">
            还没有账户？
            <router-link to="/register" class="register-link">
              立即注册
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { MessageCircle, User, Lock } from 'lucide-vue-next'
import { AppBar, Input, Checkbox, FormGroup, Button } from '@/components'
import { showSuccess, showError, showWarning } from '@/utils'
import { useUserStore } from '@/stores/user'

defineOptions({
  name: 'Login'
})

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false
})

// 状态管理
const isLoading = ref(false)

// 表单验证
const usernameStatus = computed(() => {
  if (!loginForm.value.username) return 'default'
  return loginForm.value.username.trim().length >= 3 ? 'success' : 'error'
})

const usernameError = computed(() => {
  if (!loginForm.value.username) return ''
  return loginForm.value.username.trim().length >= 3 ? '' : '用户名至少需要3个字符'
})

const passwordStatus = computed(() => {
  if (!loginForm.value.password) return 'default'
  return loginForm.value.password.length >= 6 ? 'success' : 'error'
})

const passwordError = computed(() => {
  if (!loginForm.value.password) return ''
  return loginForm.value.password.length >= 6 ? '' : '密码至少需要6个字符'
})

const isFormValid = computed(() => {
  return loginForm.value.username.trim().length >= 3 &&
         loginForm.value.password.length >= 6
})

// 登录处理
const handleLogin = async () => {
  if (!isFormValid.value) {
    showWarning('请填写完整的登录信息')
    return
  }

  isLoading.value = true

  try {
    // 调用登录API
    const result = await userStore.login({
      username: loginForm.value.username.trim(),
      password: loginForm.value.password
    })

    if (result.success) {
      // 登录成功后跳转到聊天页面
      setTimeout(() => {
        router.push('/chat')
      }, 1000)
    }
  } catch (error) {
    console.error('登录失败:', error)
    // 错误已经在store中处理并显示了
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-6) var(--space-4);
  overflow-y: auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: var(--space-10);
}

.app-logo {
  margin-bottom: var(--space-6);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-20);
  height: var(--space-20);
  background: var(--color-primary-500);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: var(--letter-spacing-tight);
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.form-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-link {
  font-size: var(--font-size-sm);
  color: var(--color-text-link);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

.forgot-link:hover {
  color: var(--color-primary-600);
}

.register-section {
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border-primary);
}

.register-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.register-link {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--duration-fast) var(--ease-out);
}

.register-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}
</style>