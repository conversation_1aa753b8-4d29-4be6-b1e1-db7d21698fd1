<template>
  <div class="register">
    <!-- 顶部导航栏 -->
    <AppBar
      title="注册"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- 注册内容区域 -->
    <div class="register-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="app-logo">
          <div class="logo-icon">
            <UserPlus :size="48" />
          </div>
        </div>
        <h2 class="welcome-title">创建账户</h2>
        <p class="welcome-subtitle">加入我们，开始精彩的聊天体验</p>
      </div>

      <!-- 注册表单 -->
      <div class="form-section">
        <form @submit.prevent="handleRegister" class="register-form">
          <FormGroup label="用户名" required>
            <Input
              v-model="registerForm.username"
              :prefix-icon="User"
              placeholder="请输入用户名"
              :status="usernameStatus"
              :error-message="usernameError"
              autofocus
            />
          </FormGroup>

          <FormGroup label="邮箱地址" required>
            <Input
              v-model="registerForm.email"
              type="email"
              :prefix-icon="AtSign"
              placeholder="请输入邮箱地址"
              :status="emailStatus"
              :error-message="emailError"
            />
          </FormGroup>

          <FormGroup label="昵称" required>
            <Input
              v-model="registerForm.nickname"
              :prefix-icon="Smile"
              placeholder="请输入昵称"
              :status="nicknameStatus"
              :error-message="nicknameError"
            />
          </FormGroup>

          <FormGroup label="密码" required>
            <Input
              v-model="registerForm.password"
              type="password"
              :prefix-icon="Lock"
              placeholder="请输入密码"
              show-password-toggle
              :status="passwordStatus"
              :error-message="passwordError"
            />
          </FormGroup>

          <FormGroup label="确认密码" required>
            <Input
              v-model="registerForm.confirmPassword"
              type="password"
              :prefix-icon="Shield"
              placeholder="请确认密码"
              show-password-toggle
              :status="confirmPasswordStatus"
              :error-message="confirmPasswordError"
            />
          </FormGroup>

          <div class="form-options">
            <Checkbox
              v-model="registerForm.agreeTerms"
              required
            >
              <span class="agreement-text">
                我已阅读并同意
                <a href="#" class="terms-link">用户协议</a>
                和
                <a href="#" class="terms-link">隐私政策</a>
              </span>
            </Checkbox>
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            block
            :loading="isLoading"
            :disabled="!isFormValid"
          >
            注册
          </Button>
        </form>

        <!-- 登录链接 -->
        <div class="login-section">
          <p class="login-text">
            已有账户？
            <router-link to="/login" class="login-link">
              立即登录
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { UserPlus, User, AtSign, Smile, Lock, Shield } from 'lucide-vue-next'
import { AppBar, Input, Checkbox, FormGroup, Button } from '@/components'
import { showSuccess, showError, showWarning, isValidEmail } from '@/utils'
import { useUserStore } from '@/stores/user'

defineOptions({
  name: 'Register'
})

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const registerForm = ref({
  username: '',
  email: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 状态管理
const isLoading = ref(false)

// 表单验证状态
const usernameStatus = computed(() => {
  if (!registerForm.value.username) return 'default'
  return registerForm.value.username.trim().length >= 3 ? 'success' : 'error'
})

const usernameError = computed(() => {
  if (!registerForm.value.username) return ''
  return registerForm.value.username.trim().length >= 3 ? '' : '用户名至少需要3个字符'
})

const emailStatus = computed(() => {
  if (!registerForm.value.email) return 'default'
  return isValidEmail(registerForm.value.email) ? 'success' : 'error'
})

const emailError = computed(() => {
  if (!registerForm.value.email) return ''
  return isValidEmail(registerForm.value.email) ? '' : '请输入有效的邮箱地址'
})

const nicknameStatus = computed(() => {
  if (!registerForm.value.nickname) return 'default'
  return registerForm.value.nickname.trim().length >= 2 ? 'success' : 'error'
})

const nicknameError = computed(() => {
  if (!registerForm.value.nickname) return ''
  return registerForm.value.nickname.trim().length >= 2 ? '' : '昵称至少需要2个字符'
})

const passwordStatus = computed(() => {
  if (!registerForm.value.password) return 'default'
  return registerForm.value.password.length >= 6 ? 'success' : 'error'
})

const passwordError = computed(() => {
  if (!registerForm.value.password) return ''
  return registerForm.value.password.length >= 6 ? '' : '密码至少需要6个字符'
})

const confirmPasswordStatus = computed(() => {
  if (!registerForm.value.confirmPassword) return 'default'
  if (registerForm.value.password !== registerForm.value.confirmPassword) return 'error'
  return registerForm.value.confirmPassword.length >= 6 ? 'success' : 'error'
})

const confirmPasswordError = computed(() => {
  if (!registerForm.value.confirmPassword) return ''
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    return '两次输入的密码不一致'
  }
  return registerForm.value.confirmPassword.length >= 6 ? '' : '密码至少需要6个字符'
})

// 表单验证
const isFormValid = computed(() => {
  return usernameStatus.value === 'success' &&
         emailStatus.value === 'success' &&
         nicknameStatus.value === 'success' &&
         passwordStatus.value === 'success' &&
         confirmPasswordStatus.value === 'success' &&
         registerForm.value.agreeTerms
})

// 注册处理
const handleRegister = async () => {
  if (!isFormValid.value) {
    showWarning('请填写完整的注册信息')
    return
  }

  isLoading.value = true

  try {
    // 调用注册API
    const result = await userStore.register({
      username: registerForm.value.username.trim(),
      email: registerForm.value.email.trim(),
      nickname: registerForm.value.nickname.trim(),
      password: registerForm.value.password
    })

    if (result.success) {
      showSuccess('注册成功！即将跳转到登录页面')
      // 注册成功后跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  } catch (error) {
    console.error('注册失败:', error)
    // 错误已经在store中处理并显示了
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.register {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.register-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-6) var(--space-4);
  overflow-y: auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: var(--space-8);
}

.app-logo {
  margin-bottom: var(--space-6);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-20);
  height: var(--space-20);
  background: var(--color-success);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: var(--letter-spacing-tight);
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.form-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.agreement-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.terms-link {
  color: var(--color-text-link);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

.terms-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

.login-section {
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border-primary);
}

.login-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.login-link {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--duration-fast) var(--ease-out);
}

.login-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}
</style>