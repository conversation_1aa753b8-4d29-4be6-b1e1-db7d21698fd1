<template>
  <div class="badge-test">
    <div class="header">
      <h1>徽标数量测试</h1>
      <p>测试WebSocket徽标数量广播功能</p>
    </div>

    <div class="status-section">
      <h2>连接状态</h2>
      <div class="status-item">
        <span class="label">WebSocket状态:</span>
        <span :class="['status', { connected: webSocketService.isConnected.value, connecting: webSocketService.isConnecting.value }]">
          {{ webSocketService.isConnected.value ? '已连接' : webSocketService.isConnecting.value ? '连接中' : '未连接' }}
        </span>
      </div>
      <div class="status-item">
        <span class="label">用户登录状态:</span>
        <span :class="['status', { connected: userStore.isLoggedIn }]">
          {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
        </span>
      </div>
    </div>

    <div class="badge-section">
      <h2>徽标数量</h2>
      <div class="badge-display">
        <div class="badge-item">
          <span class="label">当前未读数量:</span>
          <span class="badge-count">{{ chatStore.totalUnreadCount }}</span>
        </div>
        <div class="badge-item">
          <span class="label">聊天列表:</span>
          <div class="chat-list">
            <div v-for="chat in chatStore.chats" :key="chat.id" class="chat-item">
              <span>{{ chat.name }}</span>
              <span class="unread">{{ chat.unreadCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="actions-section">
      <h2>测试操作</h2>
      <div class="actions">
        <button @click="connectWebSocket" :disabled="webSocketService.isConnected.value">
          连接WebSocket
        </button>
        <button @click="disconnectWebSocket" :disabled="!webSocketService.isConnected.value">
          断开WebSocket
        </button>
        <button @click="fetchChats">
          刷新聊天列表
        </button>
        <button @click="simulateNewMessage">
          模拟新消息
        </button>
      </div>
    </div>

    <div class="logs-section">
      <h2>消息日志</h2>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="type">{{ log.type }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/services/websocket'
import { useUserStore } from '@/stores/user'
import { useChatStore } from '@/stores/chat'

const webSocketService = useWebSocket()
const userStore = useUserStore()
const chatStore = useChatStore()

// 日志记录
const logs = ref<Array<{ timestamp: string; type: string; message: string }>>([])

const addLog = (type: string, message: string) => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    message
  })
  // 只保留最近50条日志
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// WebSocket连接操作
const connectWebSocket = () => {
  webSocketService.connect()
  addLog('ACTION', '尝试连接WebSocket')
}

const disconnectWebSocket = () => {
  webSocketService.disconnect()
  addLog('ACTION', '断开WebSocket连接')
}

// 刷新聊天列表
const fetchChats = async () => {
  await chatStore.fetchChats()
  addLog('ACTION', '刷新聊天列表')
}

// 模拟新消息（增加未读数量）
const simulateNewMessage = () => {
  if (chatStore.chats.length > 0) {
    const randomChat = chatStore.chats[Math.floor(Math.random() * chatStore.chats.length)]
    randomChat.unreadCount++
    addLog('SIMULATE', `为聊天 "${randomChat.name}" 增加未读消息`)
  }
}

// 监听WebSocket消息
const originalHandleMessage = webSocketService.handleMessage
webSocketService.handleMessage = function(message: any) {
  // 记录收到的消息
  addLog('RECEIVED', `${message.type}: ${JSON.stringify(message.data)}`)
  
  // 调用原始处理函数
  return originalHandleMessage.call(this, message)
}

onMounted(() => {
  addLog('INFO', '徽标测试页面已加载')
  
  // 如果用户已登录但WebSocket未连接，自动连接
  if (userStore.isLoggedIn && !webSocketService.isConnected.value) {
    connectWebSocket()
  }
  
  // 加载聊天列表
  fetchChats()
})

onUnmounted(() => {
  // 恢复原始处理函数
  webSocketService.handleMessage = originalHandleMessage
})
</script>

<style scoped>
.badge-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
}

.status-section,
.badge-section,
.actions-section,
.logs-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.status-item,
.badge-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  color: #333;
}

.status.connected {
  color: #4CAF50;
}

.status.connecting {
  color: #FF9800;
}

.badge-count {
  background: #f44336;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 14px;
  min-width: 20px;
  text-align: center;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chat-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  background: white;
  border-radius: 4px;
}

.unread {
  background: #2196F3;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #2196F3;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.actions button:hover:not(:disabled) {
  background: #1976D2;
}

.actions button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.timestamp {
  color: #666;
  min-width: 80px;
}

.type {
  color: #2196F3;
  min-width: 80px;
  font-weight: bold;
}

.message {
  color: #333;
  flex: 1;
}
</style>
