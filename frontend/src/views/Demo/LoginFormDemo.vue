<template>
  <div class="login-demo">
    <div class="login-container">
      <!-- 头部 -->
      <div class="login-header">
        <div class="logo-section">
          <div class="logo-icon">
            <MessageCircle :size="32" />
          </div>
          <h1 class="welcome-title">欢迎回来</h1>
          <p class="welcome-subtitle">登录您的账户继续聊天</p>
        </div>
      </div>

      <!-- 登录表单 -->
      <div class="form-section">
        <form @submit.prevent="handleLogin" class="login-form">
          <FormGroup label="用户名或邮箱" required>
            <Input 
              v-model="loginForm.username"
              :prefix-icon="User"
              placeholder="请输入用户名或邮箱"
              :status="usernameStatus"
              :error-message="usernameError"
              autofocus
            />
          </FormGroup>
          
          <FormGroup label="密码" required>
            <Input 
              v-model="loginForm.password"
              type="password"
              :prefix-icon="Lock"
              placeholder="请输入密码"
              show-password-toggle
              :status="passwordStatus"
              :error-message="passwordError"
            />
          </FormGroup>

          <div class="form-options">
            <Checkbox 
              v-model="loginForm.rememberMe"
              label="记住我"
            />
            <router-link to="/forgot-password" class="forgot-link">
              忘记密码？
            </router-link>
          </div>

          <Button 
            type="submit" 
            variant="primary"
            size="lg"
            block
            :loading="isLoading"
            :disabled="!isFormValid"
          >
            登录
          </Button>
        </form>

        <!-- 注册链接 -->
        <div class="register-section">
          <p class="register-text">
            还没有账户？
            <router-link to="/register" class="register-link">
              立即注册
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { MessageCircle, User, Lock } from 'lucide-vue-next'
import { Input, Checkbox, FormGroup, Button } from '@/components'
import { showSuccess, showError, showWarning } from '@/utils'

defineOptions({
  name: 'LoginFormDemo'
})

const router = useRouter()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false
})

// 状态管理
const isLoading = ref(false)

// 表单验证
const usernameStatus = computed(() => {
  if (!loginForm.value.username) return 'default'
  return loginForm.value.username.trim().length >= 3 ? 'success' : 'error'
})

const usernameError = computed(() => {
  if (!loginForm.value.username) return ''
  return loginForm.value.username.trim().length >= 3 ? '' : '用户名至少需要3个字符'
})

const passwordStatus = computed(() => {
  if (!loginForm.value.password) return 'default'
  return loginForm.value.password.length >= 6 ? 'success' : 'error'
})

const passwordError = computed(() => {
  if (!loginForm.value.password) return ''
  return loginForm.value.password.length >= 6 ? '' : '密码至少需要6个字符'
})

const isFormValid = computed(() => {
  return loginForm.value.username.trim().length >= 3 && 
         loginForm.value.password.length >= 6
})

// 登录处理
const handleLogin = async () => {
  if (!isFormValid.value) {
    showWarning('请填写完整的登录信息')
    return
  }

  isLoading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    showSuccess('登录成功！')
    
    // 跳转到聊天页面
    setTimeout(() => {
      router.push('/chat')
    }, 1000)
  } catch (error) {
    console.error('登录失败:', error)
    showError('登录失败，请检查用户名和密码')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-demo {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
  padding: var(--space-4);
}

.login-container {
  width: 100%;
  max-width: 400px;
  background: var(--color-background-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
}

.login-header {
  padding: var(--space-8) var(--space-6) var(--space-6);
  text-align: center;
  background: var(--color-background-secondary);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-16);
  height: var(--space-16);
  background: var(--color-primary-500);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  letter-spacing: var(--letter-spacing-tight);
}

.welcome-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.form-section {
  padding: var(--space-6);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-link {
  color: var(--color-primary-500);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--duration-fast) var(--ease-out);
}

.forgot-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

.register-section {
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border-primary);
}

.register-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.register-link {
  color: var(--color-primary-500);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--ease-out);
}

.register-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}


</style>
