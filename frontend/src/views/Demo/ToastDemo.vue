<template>
  <div class="toast-demo">
    <div class="app-bar">
      <button class="back-btn" @click="$router.back()">
        <ChevronLeft />
      </button>
      <h1 class="app-title">Toast 演示</h1>
    </div>

    <div class="demo-content">
      <div class="demo-section">
        <h2 class="section-title">基础用法</h2>
        <div class="button-grid">
          <button class="demo-btn success" @click="showSuccessToast">
            <CheckCircle />
            成功提示
          </button>
          <button class="demo-btn error" @click="showErrorToast">
            <XCircle />
            错误提示
          </button>
          <button class="demo-btn warning" @click="showWarningToast">
            <AlertTriangle />
            警告提示
          </button>
          <button class="demo-btn info" @click="showInfoToast">
            <Info />
            信息提示
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2 class="section-title">自定义配置</h2>
        <div class="button-grid">
          <button class="demo-btn" @click="showLongDurationToast">
            <Clock />
            长时间显示
          </button>
          <button class="demo-btn" @click="showNonClosableToast">
            <Lock />
            不可关闭
          </button>
          <button class="demo-btn" @click="showClickableToast">
            <MousePointer />
            可点击
          </button>
          <button class="demo-btn" @click="showMultipleToasts">
            <Layers />
            多个提示
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2 class="section-title">实际场景</h2>
        <div class="button-grid">
          <button class="demo-btn" @click="simulateLogin">
            <LogIn />
            模拟登录
          </button>
          <button class="demo-btn" @click="simulateUpload">
            <Upload />
            模拟上传
          </button>
          <button class="demo-btn" @click="simulateNetworkError">
            <Wifi />
            网络错误
          </button>
          <button class="demo-btn" @click="simulateFormValidation">
            <FileText />
            表单验证
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2 class="section-title">控制操作</h2>
        <div class="button-grid">
          <button class="demo-btn danger" @click="clearAllToasts">
            <Trash2 />
            清除所有
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2 class="section-title">使用说明</h2>
        <div class="usage-code">
          <h3>导入方式</h3>
          <pre><code>import { toast, showSuccess, showError } from '@/utils'</code></pre>
          
          <h3>基础用法</h3>
          <pre><code>// 成功提示
showSuccess('操作成功！')

// 错误提示
showError('操作失败，请重试')

// 警告提示
showWarning('请注意检查输入')

// 信息提示
showInfo('这是一条信息')</code></pre>
          
          <h3>自定义配置</h3>
          <pre><code>// 自定义显示时间
showSuccess('成功！', { duration: 5000 })

// 不可关闭
showInfo('重要信息', { closable: false })

// 点击回调
showSuccess('点击我', {
  onClick: () => console.log('Toast被点击了')
})</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ChevronLeft, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info,
  Clock,
  Lock,
  MousePointer,
  Layers,
  LogIn,
  Upload,
  Wifi,
  FileText,
  Trash2
} from 'lucide-vue-next'
import { 
  toast, 
  showSuccess, 
  showError, 
  showWarning, 
  showInfo, 
  clearToasts 
} from '@/utils'

// 基础用法
const showSuccessToast = () => {
  showSuccess('操作成功！')
}

const showErrorToast = () => {
  showError('操作失败，请重试')
}

const showWarningToast = () => {
  showWarning('请注意检查输入内容')
}

const showInfoToast = () => {
  showInfo('这是一条普通信息提示')
}

// 自定义配置
const showLongDurationToast = () => {
  showInfo('这条消息会显示10秒钟', { duration: 10000 })
}

const showNonClosableToast = () => {
  showWarning('这条消息不能手动关闭，5秒后自动消失', { 
    closable: false,
    duration: 5000
  })
}

const showClickableToast = () => {
  showInfo('点击这条消息试试看', {
    onClick: () => {
      showSuccess('你点击了Toast消息！')
    }
  })
}

const showMultipleToasts = () => {
  showSuccess('第一条消息')
  setTimeout(() => showWarning('第二条消息'), 500)
  setTimeout(() => showInfo('第三条消息'), 1000)
  setTimeout(() => showError('第四条消息'), 1500)
}

// 实际场景模拟
const simulateLogin = async () => {
  showInfo('正在登录...')
  
  // 模拟登录过程
  setTimeout(() => {
    const success = Math.random() > 0.3
    if (success) {
      showSuccess('登录成功！欢迎回来')
    } else {
      showError('登录失败：用户名或密码错误')
    }
  }, 2000)
}

const simulateUpload = async () => {
  showInfo('开始上传文件...')
  
  setTimeout(() => {
    showWarning('上传中，请稍候...')
  }, 1000)
  
  setTimeout(() => {
    const success = Math.random() > 0.2
    if (success) {
      showSuccess('文件上传成功！')
    } else {
      showError('上传失败：文件格式不支持')
    }
  }, 3000)
}

const simulateNetworkError = () => {
  showError('网络连接失败，请检查网络设置后重试', {
    duration: 5000
  })
}

const simulateFormValidation = () => {
  const errors = [
    '请输入用户名',
    '邮箱格式不正确',
    '密码长度至少6位',
    '两次密码输入不一致'
  ]
  
  errors.forEach((error, index) => {
    setTimeout(() => {
      showWarning(error)
    }, index * 800)
  })
}

const clearAllToasts = () => {
  clearToasts()
  showInfo('已清除所有Toast消息')
}
</script>

<style scoped>
.toast-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-btn:hover {
  background: var(--color-background-secondary);
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.demo-content {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
}

.demo-section {
  margin-bottom: var(--space-6);
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-3) 0;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-3);
}

.demo-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-md);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 48px;
}

.demo-btn:hover {
  background: var(--color-background-secondary);
  border-color: var(--color-border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.demo-btn:active {
  transform: translateY(0);
}

.demo-btn.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: rgb(34, 197, 94);
}

.demo-btn.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: rgb(239, 68, 68);
}

.demo-btn.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: rgb(245, 158, 11);
}

.demo-btn.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: rgb(59, 130, 246);
}

.demo-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: rgb(239, 68, 68);
}

.usage-code {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-4);
}

.usage-code h3 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
}

.usage-code h3:not(:first-child) {
  margin-top: var(--space-4);
}

.usage-code pre {
  background: var(--color-background-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-sm);
  padding: var(--space-3);
  margin: var(--space-2) 0;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
}

.usage-code code {
  color: var(--color-text-secondary);
  white-space: pre;
}

/* 图标样式 */
.back-btn svg {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease-in-out;
}

.back-btn:hover svg {
  width: 26px;
  height: 26px;
}

.demo-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.demo-btn:hover svg {
  width: 22px;
  height: 22px;
  transform: scale(1.1);
}


</style>