<template>
  <div class="component-demo">
    <AppBar
      title="组件演示"
      :show-back="true"
      :actions="appBarActions"
      @back="handleBack"
      @action="handleAppBarAction"
    />
    
    <div class="demo-content">
      <!-- Button 演示 -->
      <section class="demo-section">
        <h2>Button 组件</h2>
        <div class="demo-grid">
          <Button variant="primary">主要按钮</Button>
          <Button variant="secondary">次要按钮</Button>
          <Button variant="ghost">幽灵按钮</Button>
          <Button variant="danger">危险按钮</Button>
          <Button :loading="true">加载中</Button>
          <Button :icon="Plus" icon-position="left">添加</Button>
          <Button :icon="Search" circle />
        </div>
      </section>
      
      <!-- Avatar 演示 -->
      <section class="demo-section">
        <h2>Avatar 组件</h2>
        <div class="demo-grid">
          <Avatar name="<PERSON>" size="sm" />
          <Avatar name="<PERSON>" size="md" :show-online-status="true" :is-online="true" />
          <Avatar 
            name="<PERSON>" 
            size="lg" 
            src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
          />
          <Avatar name="David Wilson" size="xl" :square="true" />
        </div>
      </section>
      
      <!-- Badge 演示 -->
      <section class="demo-section">
        <h2>Badge 组件</h2>
        <div class="demo-grid">
          <Badge :count="5" />
          <Badge :count="99" :max-count="99" />
          <Badge :count="100" :max-count="99" />
          <Badge variant="success" :dot="true" />
          <Badge variant="error" text="NEW" />
        </div>
      </section>
      
      <!-- SearchBar 演示 -->
      <section class="demo-section">
        <h2>SearchBar 组件</h2>
        <SearchBar
          v-model="searchQuery"
          placeholder="搜索内容..."
          :clearable="true"
          @search="handleSearch"
        />
      </section>

      <!-- Input 演示 -->
      <section class="demo-section">
        <h2>Input 组件</h2>
        <div class="demo-form">
          <FormGroup label="用户名" required>
            <Input
              v-model="formData.username"
              :prefix-icon="User"
              placeholder="请输入用户名"
              clearable
            />
          </FormGroup>

          <FormGroup label="邮箱地址" help-text="请输入有效的邮箱地址">
            <Input
              v-model="formData.email"
              type="email"
              :prefix-icon="Mail"
              placeholder="请输入邮箱"
              :status="emailStatus"
              :error-message="emailError"
            />
          </FormGroup>

          <FormGroup label="密码" required>
            <Input
              v-model="formData.password"
              type="password"
              :prefix-icon="Lock"
              placeholder="请输入密码"
              show-password-toggle
            />
          </FormGroup>

          <FormGroup>
            <Checkbox
              v-model="formData.rememberMe"
              label="记住我"
            />
          </FormGroup>

          <FormGroup>
            <Checkbox
              v-model="formData.agreeTerms"
              status="error"
              :error-message="!formData.agreeTerms ? '请同意用户协议' : ''"
            >
              我已阅读并同意 <a href="#" style="color: var(--color-primary-500)">用户协议</a>
            </Checkbox>
          </FormGroup>
        </div>
      </section>
      
      <!-- ListItem 演示 -->
      <section class="demo-section">
        <h2>ListItem 组件</h2>
        <div class="demo-list">
          <ListItem
            v-for="item in demoListItems"
            :key="item.id"
            :avatar="item.avatar"
            :title="item.title"
            :subtitle="item.subtitle"
            :meta="item.meta"
            :badge="item.badge"
            :actions="item.actions"
            @click="handleListItemClick(item)"
            @action="handleListItemAction"
          />
        </div>
      </section>
      
      <!-- EmptyState 演示 -->
      <section class="demo-section">
        <h2>EmptyState 组件</h2>
        <EmptyState
          :icon="MessageCircle"
          title="暂无消息"
          description="开始与您的联系人聊天吧"
          action-text="开始聊天"
          @action="handleEmptyAction"
        />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, MessageCircle, Phone, Video, Settings, User, Lock, Mail } from 'lucide-vue-next'
import {
  AppBar,
  Button,
  Avatar,
  Badge,
  SearchBar,
  ListItem,
  EmptyState,
  Input,
  Checkbox,
  FormGroup,
  type AppBarAction,
  type ListItemAction
} from '@/components'

defineOptions({
  name: 'ComponentDemo'
})

const router = useRouter()
const searchQuery = ref('')

// 表单数据
const formData = ref({
  username: '',
  email: '',
  password: '',
  rememberMe: false,
  agreeTerms: false
})

// 邮箱验证状态
const emailStatus = computed(() => {
  if (!formData.value.email) return 'default'
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(formData.value.email) ? 'success' : 'error'
})

const emailError = computed(() => {
  if (!formData.value.email) return ''
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(formData.value.email) ? '' : '请输入有效的邮箱地址'
})

// AppBar 操作
const appBarActions: AppBarAction[] = [
  {
    icon: Settings,
    onClick: () => console.log('设置'),
    key: 'settings'
  }
]

// 演示列表数据
const demoListItems = ref([
  {
    id: 1,
    avatar: {
      src: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      name: 'Alice Johnson',
      isOnline: true
    },
    title: 'Alice Johnson',
    subtitle: '你好，最近怎么样？',
    meta: '5分钟前',
    badge: {
      count: 2,
      variant: 'primary' as const
    },
    actions: [
      {
        icon: Phone,
        onClick: () => console.log('语音通话'),
        key: 'call'
      },
      {
        icon: Video,
        onClick: () => console.log('视频通话'),
        key: 'video'
      }
    ] as ListItemAction[]
  },
  {
    id: 2,
    avatar: {
      name: 'Bob Smith',
      isOnline: false
    },
    title: 'Bob Smith',
    subtitle: '明天见！',
    meta: '1小时前',
    actions: [
      {
        icon: Phone,
        onClick: () => console.log('语音通话'),
        key: 'call'
      }
    ] as ListItemAction[]
  }
])

// 事件处理
const handleBack = () => {
  router.back()
}

const handleAppBarAction = (key: string, action: AppBarAction) => {
  console.log('AppBar action:', key, action)
}

const handleSearch = (value: string) => {
  console.log('搜索:', value)
}

const handleListItemClick = (item: any) => {
  console.log('点击列表项:', item)
}

const handleListItemAction = (key: string, action: ListItemAction) => {
  console.log('列表项操作:', key, action)
}

const handleEmptyAction = () => {
  console.log('空状态操作')
}
</script>

<style scoped>
.component-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.demo-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.demo-section {
  margin-bottom: var(--space-8);
}

.demo-section h2 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-4) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--color-border-primary);
}

.demo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: center;
}

.demo-form {
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.demo-list {
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--color-background-primary);
}


</style>
