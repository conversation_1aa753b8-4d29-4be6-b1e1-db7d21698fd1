<template>
  <div class="friend-requests">
    <!-- 顶部导航栏 -->
    <AppBar
      title="新的朋友"
      :show-back="true"
      @back="handleBack"
    />

    <!-- 标签页 -->
    <div class="tabs-container">
      <div class="tabs">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'received' }"
          @click="activeTab = 'received'"
        >
          收到的请求
          <span v-if="pendingRequests.length > 0" class="badge">
            {{ pendingRequests.length }}
          </span>
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'sent' }"
          @click="activeTab = 'sent'"
        >
          发送的请求
          <span v-if="pendingSentRequests.length > 0" class="badge">
            {{ pendingSentRequests.length }}
          </span>
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 收到的请求 -->
      <div v-if="activeTab === 'received'" class="requests-list">
        <!-- 空状态 -->
        <EmptyState
          v-if="!loading && pendingRequests.length === 0"
          :icon="UserCheck"
          title="暂无好友请求"
          description="还没有收到任何好友请求"
        />

        <!-- 请求列表 -->
        <div v-else class="request-items">
          <div
            v-for="request in pendingRequests"
            :key="request.id"
            class="request-item"
          >
            <div class="request-info">
              <div class="user-avatar">
                <img
                  v-if="request.user.avatar_url"
                  :src="request.user.avatar_url"
                  :alt="request.user.nickname || request.user.username"
                  class="avatar-image"
                />
                <div v-else class="avatar-placeholder">
                  {{ getAvatarText(request.user.nickname || request.user.username) }}
                </div>
              </div>
              <div class="user-details">
                <h4 class="user-name">{{ request.user.nickname || request.user.username }}</h4>
                <p class="user-username">@{{ request.user.username }}</p>
                <p v-if="request.request_message" class="request-message">
                  {{ request.request_message }}
                </p>
                <p class="request-time">{{ formatTime(request.created_at) }}</p>
              </div>
            </div>
            <div class="request-actions">
              <button
                class="reject-button"
                :disabled="loading"
                @click="handleRejectRequest(request.id)"
              >
                拒绝
              </button>
              <button
                class="accept-button"
                :disabled="loading"
                @click="handleAcceptRequest(request.id)"
              >
                接受
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 发送的请求 -->
      <div v-if="activeTab === 'sent'" class="requests-list">
        <!-- 空状态 -->
        <EmptyState
          v-if="!loading && pendingSentRequests.length === 0"
          :icon="UserCheck"
          title="暂无发送的请求"
          description="还没有发送任何好友请求"
        />

        <!-- 请求列表 -->
        <div v-else class="request-items">
          <div
            v-for="request in pendingSentRequests"
            :key="request.id"
            class="request-item"
          >
            <div class="request-info">
              <div class="user-avatar">
                <img
                  v-if="request.user.avatar_url"
                  :src="request.user.avatar_url"
                  :alt="request.user.nickname || request.user.username"
                  class="avatar-image"
                />
                <div v-else class="avatar-placeholder">
                  {{ getAvatarText(request.user.nickname || request.user.username) }}
                </div>
              </div>
              <div class="user-details">
                <h4 class="user-name">{{ request.user.nickname || request.user.username }}</h4>
                <p class="user-username">@{{ request.user.username }}</p>
                <p v-if="request.request_message" class="request-message">
                  {{ request.request_message }}
                </p>
                <p class="request-time">{{ formatTime(request.created_at) }}</p>
              </div>
            </div>
            <div class="request-actions">
              <button
                class="cancel-button"
                :disabled="loading"
                @click="handleCancelRequest(request.id)"
              >
                撤销
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">加载中...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { UserCheck } from 'lucide-vue-next'
import { AppBar, EmptyState } from '@/components'
import { useContactsStore } from '@/stores/contacts'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'FriendRequests'
})

const router = useRouter()
const contactsStore = useContactsStore()

// 状态
const activeTab = ref<'received' | 'sent'>('received')

// 计算属性
const pendingRequests = computed(() => contactsStore.pendingRequests)
const pendingSentRequests = computed(() => contactsStore.pendingSentRequests)
const loading = computed(() => contactsStore.loading)

// 处理返回
const handleBack = () => {
  router.back()
}

// 获取头像文字
const getAvatarText = (name: string): string => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 格式化时间
const formatTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString()
}

// 处理接受请求
const handleAcceptRequest = async (requestId: number) => {
  await contactsStore.handleFriendRequest(requestId, 'accept')
}

// 处理拒绝请求
const handleRejectRequest = async (requestId: number) => {
  await contactsStore.handleFriendRequest(requestId, 'reject')
}

// 处理撤销请求
const handleCancelRequest = async (requestId: number) => {
  await contactsStore.cancelFriendRequest(requestId)
}

// 组件挂载时获取数据
onMounted(async () => {
  await contactsStore.fetchFriendRequests()
})
</script>

<style scoped>
.friend-requests {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.tabs-container {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.tabs {
  display: flex;
  padding: 0 var(--space-4);
}

.tab-button {
  flex: 1;
  height: 48px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  transition: color 0.2s ease;
}

.tab-button.active {
  color: var(--color-primary);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

.badge {
  background: var(--color-danger);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.requests-list {
  padding: var(--space-2) 0;
}

.request-items {
  display: flex;
  flex-direction: column;
}

.request-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-border-primary);
}

.request-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-width: 0;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-username {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.request-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-1);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.request-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin: 0;
}

.request-actions {
  display: flex;
  gap: var(--space-2);
  flex-shrink: 0;
}

.accept-button,
.reject-button,
.cancel-button {
  height: 32px;
  padding: 0 var(--space-3);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.accept-button {
  background: var(--color-success);
  color: white;
}

.accept-button:hover:not(:disabled) {
  background: var(--color-success-dark);
}

.reject-button,
.cancel-button {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.reject-button:hover:not(:disabled),
.cancel-button:hover:not(:disabled) {
  background: var(--color-background-tertiary);
}

.accept-button:disabled,
.reject-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  gap: var(--space-3);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
