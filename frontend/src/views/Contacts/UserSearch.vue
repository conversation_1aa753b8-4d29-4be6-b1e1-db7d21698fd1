<template>
  <div class="user-search">
    <!-- 顶部导航栏 -->
    <AppBar
      title="添加好友"
      :show-back="true"
      @back="handleBack"
    />

    <!-- 搜索栏 -->
    <div class="search-section">
      <SearchBar
        v-model="searchQuery"
        placeholder="搜索用户名、昵称或邮箱..."
        :loading="loading"
        @search="handleSearch"
        @clear="handleClear"
      />
    </div>

    <!-- 搜索结果 -->
    <div class="search-content">
      <!-- 空状态 -->
      <EmptyState
        v-if="!loading && searchQuery && searchResults.length === 0"
        :icon="UserX"
        title="未找到用户"
        description="请尝试其他关键词"
      />

      <!-- 搜索提示 -->
      <EmptyState
        v-else-if="!loading && !searchQuery"
        :icon="Search"
        title="搜索用户"
        description="输入用户名、昵称或邮箱来搜索用户"
      />

      <!-- 搜索结果列表 -->
      <div v-else-if="searchResults.length > 0" class="search-results">
        <ListItem
          v-for="user in searchResults"
          :key="user.id"
          :avatar="{
            src: user.avatar_url,
            name: user.nickname || user.username
          }"
          :title="user.nickname || user.username"
          :subtitle="user.username"
          :actions="getUserActions(user)"
          @action="handleUserAction"
        />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">搜索中...</p>
      </div>
    </div>

    <!-- 添加好友弹窗 -->
    <AddFriendModal
      v-if="showAddFriendModal"
      :user="selectedUser"
      @confirm="handleAddFriend"
      @cancel="showAddFriendModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, UserX, UserPlus } from 'lucide-vue-next'
import {
  AppBar,
  SearchBar,
  EmptyState,
  ListItem,
  type ListItemAction
} from '@/components'
import AddFriendModal from '@/components/contacts/AddFriendModal.vue'
import { useContactsStore } from '@/stores/contacts'
import type { User } from '@/services/api'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'UserSearch'
})

const router = useRouter()
const contactsStore = useContactsStore()

// 搜索相关
const searchQuery = ref('')
const selectedUser = ref<User | null>(null)
const showAddFriendModal = ref(false)

// 计算属性
const searchResults = computed(() => contactsStore.searchResults)
const loading = computed(() => contactsStore.loading)

// 处理返回
const handleBack = () => {
  router.back()
}

// 处理搜索
const handleSearch = async () => {
  if (searchQuery.value.trim()) {
    await contactsStore.searchUsers(searchQuery.value.trim())
  }
}

// 处理清空搜索
const handleClear = () => {
  searchQuery.value = ''
  contactsStore.searchResults = []
}

// 获取用户操作按钮
const getUserActions = (user: User): ListItemAction[] => [
  {
    icon: UserPlus,
    key: 'add-friend',
    tooltip: '添加好友',
    onClick: () => showAddFriendDialog(user)
  }
]

// 处理用户操作
const handleUserAction = (key: string, action: ListItemAction) => {
  console.log('User action:', key, action)
}

// 显示添加好友弹窗
const showAddFriendDialog = (user: User) => {
  selectedUser.value = user
  showAddFriendModal.value = true
}

// 处理添加好友
const handleAddFriend = async (message: string) => {
  if (!selectedUser.value) return
  
  const result = await contactsStore.sendFriendRequest(selectedUser.value.id, message)
  
  if (result.success) {
    showAddFriendModal.value = false
    selectedUser.value = null
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑
})
</script>

<style scoped>
.user-search {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.search-content {
  flex: 1;
  overflow-y: auto;
}

.search-results {
  padding: var(--space-2) 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  gap: var(--space-3);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
