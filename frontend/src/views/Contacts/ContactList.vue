<template>
  <div class="contact-list">
    <!-- 顶部导航栏 -->
    <AppBar
      title="联系人"
      :show-back="false"
    >
      <template #actions>
        <button
          class="add-friend-btn"
          @click="goToAddFriend"
          :title="'添加好友'"
        >
          <UserPlus :size="20" />
        </button>
      </template>
    </AppBar>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <div class="menu-items">
        <div class="menu-item" @click="goToFriendRequests">
          <div class="menu-icon">
            <UserCheck :size="20" />
          </div>
          <span class="menu-text">新的朋友</span>
          <div class="menu-badge-container">
            <span v-if="pendingRequestsCount > 0" class="menu-badge">
              {{ pendingRequestsCount }}
            </span>
            <ChevronRight :size="16" class="menu-arrow" />
          </div>
        </div>

      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <SearchBar
        v-model="searchQuery"
        placeholder="搜索联系人..."
        @search="handleSearch"
      />
    </div>

    <!-- 联系人列表 -->
    <div class="contact-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">加载中...</p>
      </div>

      <!-- 空状态 -->
      <EmptyState
        v-else-if="filteredFriends.length === 0 && !searchQuery"
        :icon="Users"
        title="暂无好友"
        description="添加好友开始聊天吧"
      />

      <!-- 搜索无结果 -->
      <EmptyState
        v-else-if="filteredFriends.length === 0 && searchQuery"
        :icon="Search"
        title="未找到联系人"
        description="请尝试其他关键词"
      />

      <!-- 联系人列表 -->
      <div v-else class="contact-items">
        <ListItem
          v-for="contact in filteredFriends"
          :key="contact.id"
          :avatar="{
            src: contact.avatar_url,
            name: contact.nickname || contact.username,
            isOnline: getOnlineStatus(contact)
          }"
          :title="contact.nickname || contact.username"
          :subtitle="getContactSubtitle(contact)"
          :actions="getContactActions(contact)"
          @click="startChat(contact)"
          @action="handleContactAction"
        />
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      v-if="showDeleteConfirm"
      title="删除好友"
      :message="`确定要删除好友 ${selectedContact?.nickname || selectedContact?.username} 吗？`"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      @confirm="handleConfirmDelete"
      @cancel="showDeleteConfirm = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  UserPlus,
  UserCheck,
  Phone,
  Video,
  Users,
  Search,
  ChevronRight,
  MoreHorizontal,
  MessageCircle,
  Trash2,
  UserX
} from 'lucide-vue-next'
import {
  AppBar,
  SearchBar,
  EmptyState,
  ListItem,
  type AppBarAction,
  type ListItemAction
} from '@/components'
import ConfirmDialog from '@/components/feedback/ConfirmDialog.vue'
import FriendStatusIndicator from '@/components/contacts/FriendStatusIndicator.vue'
import { useContactsStore, type Contact } from '@/stores/contacts'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'ContactList'
})

const router = useRouter()
const contactsStore = useContactsStore()

// 状态
const searchQuery = ref('')
const selectedContact = ref<Contact | null>(null)
const showDeleteConfirm = ref(false)

// 计算属性
const friends = computed(() => contactsStore.friends)
const pendingRequests = computed(() => contactsStore.pendingRequests)
const loading = computed(() => contactsStore.loading)

const pendingRequestsCount = computed(() => pendingRequests.value.length)

// 过滤后的好友列表
const filteredFriends = computed(() => {
  if (!searchQuery.value.trim()) {
    return friends.value
  }
  const query = searchQuery.value.toLowerCase()
  return friends.value.filter(contact =>
    (contact.nickname || contact.username).toLowerCase().includes(query) ||
    contact.username.toLowerCase().includes(query) ||
    (contact.remark && contact.remark.toLowerCase().includes(query))
  )
})



// 导航到添加好友页面
const goToAddFriend = () => {
  router.push('/contacts/search')
}

// 导航到好友请求页面
const goToFriendRequests = () => {
  router.push('/contacts/requests')
}

// 获取在线状态
const getOnlineStatus = (contact: Contact): boolean => {
  // 这里可以根据实际的在线状态逻辑来判断
  // 暂时返回随机状态作为示例
  return contact.is_active
}

// 获取联系人副标题
const getContactSubtitle = (contact: Contact): string => {
  if (contact.remark) {
    return contact.remark
  }

  // 根据最后活跃时间显示状态
  if (getOnlineStatus(contact)) {
    return '在线'
  }

  // 这里可以根据实际的最后活跃时间来显示
  const lastActiveText = getLastActiveText(contact.updated_at)
  return lastActiveText
}

// 获取最后活跃时间文本
const getLastActiveText = (lastActiveAt: string): string => {
  const date = new Date(lastActiveAt)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚在线'
  if (minutes < 60) return `${minutes}分钟前在线`
  if (hours < 24) return `${hours}小时前在线`
  if (days < 7) return `${days}天前在线`

  return '很久未在线'
}

// 获取联系人操作按钮
const getContactActions = (contact: Contact): ListItemAction[] => [
  {
    icon: Phone,
    key: 'call',
    tooltip: '语音通话',
    onClick: () => callContact(contact)
  },
  {
    icon: Video,
    key: 'video',
    tooltip: '视频通话',
    onClick: () => videoCall(contact)
  },
  {
    icon: MoreHorizontal,
    key: 'more',
    tooltip: '更多',
    onClick: () => showContactMenu(contact)
  }
]

// 处理联系人操作
const handleContactAction = (key: string, action: ListItemAction) => {
  console.log('Contact action:', key, action)
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 显示联系人菜单
const showContactMenu = (contact: Contact) => {
  // 这里可以显示更多操作菜单，暂时直接显示删除确认
  selectedContact.value = contact
  showDeleteConfirm.value = true
}

// 处理确认删除
const handleConfirmDelete = async () => {
  if (!selectedContact.value) return

  const result = await contactsStore.removeFriend(selectedContact.value.id)

  if (result.success) {
    showDeleteConfirm.value = false
    selectedContact.value = null
  }
}

// 开始聊天
const startChat = (contact: Contact) => {
  router.push(`/chat/${contact.id}`)
}

// 语音通话
const callContact = (contact: Contact) => {
  console.log('Calling:', contact.nickname || contact.username)
  // 这里可以集成语音通话功能
}

// 视频通话
const videoCall = (contact: Contact) => {
  console.log('Video calling:', contact.nickname || contact.username)
  // 这里可以集成视频通话功能
}

// 组件挂载时获取数据
onMounted(async () => {
  // 只获取好友列表，徽章数量通过WebSocket实时更新
  await contactsStore.fetchFriends()
})


</script>

<style scoped>
.contact-list {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.menu-section {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.menu-items {
  padding: var(--space-2) 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background: var(--color-background-secondary);
}

.menu-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.menu-text {
  flex: 1;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.menu-badge-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.menu-badge {
  background: var(--color-danger);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-arrow {
  color: var(--color-text-tertiary);
  flex-shrink: 0;
}

.search-section {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.contact-content {
  flex: 1;
  overflow-y: auto;
}

.contact-items {
  padding: var(--space-2) 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  gap: var(--space-3);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* 添加好友按钮样式 */
.add-friend-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.add-friend-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.add-friend-btn:active {
  transform: translateY(0) scale(0.98);
}

/* 菜单徽章样式 */
.menu-badge-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.menu-badge {
  background: var(--color-error);
  color: white;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>