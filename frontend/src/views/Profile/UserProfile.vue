<template>
  <div class="user-profile">
    <header class="app-bar">
      <h1>我的</h1>
    </header>
    <div class="profile-content">
      <div class="empty-state">
        <div class="empty-icon">👤</div>
        <p class="empty-text">个人信息</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 用户个人资料页面
</script>

<style scoped>
.user-profile {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-bar {
  height: 56px;
  background: #1976d2;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-bar h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.profile-content {
  flex: 1;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  margin: 0;
  font-size: 16px;
}
</style>