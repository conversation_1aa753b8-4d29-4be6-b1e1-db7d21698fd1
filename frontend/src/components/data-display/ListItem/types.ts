import type { Component } from 'vue'

export interface ListItemAvatar {
  /**
   * 头像图片地址
   */
  src?: string
  
  /**
   * 用户名
   */
  name: string
  
  /**
   * 是否在线
   */
  isOnline?: boolean
  
  /**
   * 头像尺寸
   */
  size?: 'sm' | 'md' | 'lg'
}

export interface ListItemBadge {
  /**
   * 徽章数量
   */
  count?: number
  
  /**
   * 徽章变体
   */
  variant?: 'primary' | 'success' | 'warning' | 'error' | 'info'
  
  /**
   * 是否显示为圆点
   */
  dot?: boolean
}

export interface ListItemAction {
  /**
   * 图标组件
   */
  icon: Component
  
  /**
   * 点击事件处理函数
   */
  onClick: (event: Event) => void
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 操作标识
   */
  key?: string
  
  /**
   * 工具提示
   */
  tooltip?: string
}

export interface ListItemProps {
  /**
   * 头像配置
   */
  avatar?: ListItemAvatar
  
  /**
   * 标题
   */
  title: string
  
  /**
   * 副标题
   */
  subtitle?: string
  
  /**
   * 元信息（如时间）
   */
  meta?: string
  
  /**
   * 徽章配置
   */
  badge?: ListItemBadge
  
  /**
   * 操作按钮列表
   */
  actions?: ListItemAction[]
  
  /**
   * 是否可点击
   */
  clickable?: boolean
  
  /**
   * 是否激活状态
   */
  active?: boolean
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 列表项尺寸
   */
  size?: 'sm' | 'md' | 'lg'
}

export interface ListItemEmits {
  /**
   * 点击事件
   */
  click: [event: MouseEvent]
  
  /**
   * 操作按钮点击事件
   */
  action: [key: string, action: ListItemAction, event: Event]
}
