<template>
  <div
    :class="itemClasses"
    @click="handleClick"
  >
    <!-- 头像 -->
    <Avatar
      v-if="avatar"
      :src="avatar.src"
      :name="avatar.name"
      :size="avatarSize"
      :show-online-status="avatar.isOnline !== undefined"
      :is-online="avatar.isOnline"
      class="list-item-avatar"
    />
    
    <!-- 主要内容 -->
    <div :class="contentClasses">
      <!-- 头部信息 -->
      <div :class="headerClasses">
        <h3 :class="titleClasses">
          <slot name="title">{{ title }}</slot>
        </h3>
        <span v-if="meta" :class="metaClasses">
          <slot name="meta">{{ meta }}</slot>
        </span>
      </div>
      
      <!-- 副标题和徽章 -->
      <div v-if="subtitle || badge" :class="footerClasses">
        <p v-if="subtitle" :class="subtitleClasses">
          <slot name="subtitle">{{ subtitle }}</slot>
        </p>
        <Badge
          v-if="badge"
          :count="badge.count"
          :variant="badge.variant"
          :dot="badge.dot"
          class="list-item-badge"
        />
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="actions?.length" :class="actionsClasses">
      <button
        v-for="(action, index) in actions"
        :key="action.key || index"
        :class="actionButtonClasses"
        :disabled="action.disabled"
        :title="action.tooltip"
        @click.stop="handleAction(action, index, $event)"
      >
        <component :is="action.icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Avatar } from '../../base/Avatar'
import { Badge } from '../../base/Badge'
import type { ListItemProps, ListItemEmits, ListItemAction } from './types'

const props = withDefaults(defineProps<ListItemProps>(), {
  clickable: true,
  active: false,
  disabled: false,
  size: 'lg'
})

const emit = defineEmits<ListItemEmits>()

// 列表项样式类
const itemClasses = computed(() => [
  'list-item',
  `list-item-${props.size}`,
  {
    'list-item-clickable': props.clickable && !props.disabled,
    'list-item-active': props.active,
    'list-item-disabled': props.disabled
  }
])

// 内容样式类
const contentClasses = computed(() => [
  'list-item-content'
])

// 头部样式类
const headerClasses = computed(() => [
  'list-item-header'
])

// 底部样式类
const footerClasses = computed(() => [
  'list-item-footer'
])

// 标题样式类
const titleClasses = computed(() => [
  'list-item-title'
])

// 副标题样式类
const subtitleClasses = computed(() => [
  'list-item-subtitle'
])

// 元信息样式类
const metaClasses = computed(() => [
  'list-item-meta'
])

// 操作区域样式类
const actionsClasses = computed(() => [
  'list-item-actions'
])

// 操作按钮样式类
const actionButtonClasses = computed(() => [
  'list-item-action-btn'
])

// 头像尺寸
const avatarSize = computed(() => {
  const sizeMap = {
    sm: 'sm' as const,
    md: 'md' as const,
    lg: 'lg' as const
  }
  return sizeMap[props.size]
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.disabled) {
    emit('click', event)
  }
}

// 处理操作按钮点击
const handleAction = (action: ListItemAction, index: number, event: Event) => {
  if (!action.disabled) {
    action.onClick(event)
    emit('action', action.key || index.toString(), action, event)
  }
}
</script>

<style scoped>
.list-item {
  display: flex;
  align-items: center;
  transition: all var(--duration-fast) var(--ease-out);
  border-bottom: 1px solid var(--color-border-primary);
  background: var(--color-background-primary);
}

.list-item-clickable {
  cursor: pointer;
}

.list-item-clickable:hover:not(.list-item-disabled) {
  background: var(--color-background-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.list-item-active {
  background: var(--color-primary-50);
  border-color: var(--color-primary-200);
}

.list-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 尺寸变体 */
.list-item-sm {
  padding: var(--space-2) var(--space-3);
  gap: var(--space-2);
  border-radius: var(--radius-md);
  margin: 0 var(--space-2);
  margin-bottom: var(--space-1);
}

.list-item-md {
  padding: var(--space-3) var(--space-4);
  gap: var(--space-3);
  border-radius: var(--radius-lg);
  margin: 0 var(--space-3);
  margin-bottom: var(--space-1);
}

.list-item-lg {
  padding: var(--space-4) var(--space-5);
  gap: var(--space-4);
  border-radius: var(--radius-lg);
  margin: 0 var(--space-3);
  margin-bottom: var(--space-2);
}

/* 头像 */
.list-item-avatar {
  flex-shrink: 0;
}

/* 内容区域 */
.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-1);
}

.list-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 标题 */
.list-item-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: var(--letter-spacing-tight);
  flex: 1;
  min-width: 0;
}

/* 副标题 */
.list-item-subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: var(--line-height-tight);
  flex: 1;
  min-width: 0;
}

/* 元信息 */
.list-item-meta {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  flex-shrink: 0;
  margin-left: var(--space-2);
  font-weight: var(--font-weight-medium);
}

/* 徽章 */
.list-item-badge {
  flex-shrink: 0;
  margin-left: var(--space-2);
}

/* 操作区域 */
.list-item-actions {
  display: flex;
  gap: var(--space-2);
  margin-left: var(--space-3);
  flex-shrink: 0;
}

.list-item-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--space-7);
  height: var(--space-7);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-xs);
}

.list-item-action-btn:hover:not(:disabled) {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

.list-item-action-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.list-item-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.list-item-action-btn svg {
  width: 16px;
  height: 16px;
  transition: all var(--duration-fast) var(--ease-out);
}

.list-item-action-btn:hover:not(:disabled) svg {
  width: 18px;
  height: 18px;
}


</style>
