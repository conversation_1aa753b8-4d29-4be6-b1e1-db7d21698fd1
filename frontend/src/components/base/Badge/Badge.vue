<template>
  <span
    v-if="shouldShow"
    :class="badgeClasses"
    @click="handleClick"
  >
    {{ displayText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BadgeProps, BadgeEmits } from './types'

const props = withDefaults(defineProps<BadgeProps>(), {
  variant: 'primary',
  size: 'md',
  maxCount: 99,
  dot: false,
  showZero: false,
  show: true
})

const emit = defineEmits<BadgeEmits>()

// 徽章样式类
const badgeClasses = computed(() => [
  'badge',
  `badge-${props.variant}`,
  `badge-${props.size}`,
  {
    'badge-dot': props.dot,
    'badge-clickable': !!emit.click
  }
])

// 是否应该显示徽章
const shouldShow = computed(() => {
  if (!props.show) return false
  if (props.dot) return true
  if (props.text) return true
  if (props.count === undefined) return false
  if (props.count === 0 && !props.showZero) return false
  return true
})

// 显示文本
const displayText = computed(() => {
  if (props.dot) return ''
  if (props.text) return props.text
  if (props.count === undefined) return ''
  
  if (props.count > props.maxCount) {
    return `${props.maxCount}+`
  }
  
  return props.count.toString()
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}
</script>

<style scoped>
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  box-shadow: var(--shadow-xs);
  transition: all var(--duration-fast) var(--ease-out);
}

.badge-clickable {
  cursor: pointer;
}

.badge-clickable:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

/* 尺寸变体 */
.badge-sm {
  min-width: 16px;
  height: 16px;
  padding: 0 var(--space-1);
  font-size: 10px;
  line-height: 1;
}

.badge-md {
  min-width: 18px;
  height: 18px;
  padding: 0 var(--space-2);
  font-size: var(--font-size-xs);
  line-height: 1;
}

.badge-lg {
  min-width: 20px;
  height: 20px;
  padding: 0 var(--space-2);
  font-size: var(--font-size-xs);
  line-height: 1;
}

/* 圆点样式 */
.badge-dot {
  min-width: auto;
  padding: 0;
  border-radius: var(--radius-full);
}

.badge-dot.badge-sm {
  width: 6px;
  height: 6px;
}

.badge-dot.badge-md {
  width: 8px;
  height: 8px;
}

.badge-dot.badge-lg {
  width: 10px;
  height: 10px;
}

/* 颜色变体 */
.badge-primary {
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
}

.badge-success {
  background: var(--color-success);
  color: var(--color-text-inverse);
}

.badge-warning {
  background: var(--color-warning);
  color: var(--color-text-inverse);
}

.badge-error {
  background: var(--color-error);
  color: var(--color-text-inverse);
}

.badge-info {
  background: var(--color-info);
  color: var(--color-text-inverse);
}

.badge-gray {
  background: var(--color-gray-500);
  color: var(--color-text-inverse);
}

/* 活跃状态 */
.badge-clickable:active {
  transform: scale(0.95);
}
</style>
