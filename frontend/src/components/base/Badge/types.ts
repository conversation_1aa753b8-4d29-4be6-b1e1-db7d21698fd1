export interface BadgeProps {
  /**
   * 徽章数量
   */
  count?: number
  
  /**
   * 最大显示数量
   */
  maxCount?: number
  
  /**
   * 徽章变体
   */
  variant?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'gray'
  
  /**
   * 徽章尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 是否显示为圆点
   */
  dot?: boolean
  
  /**
   * 是否显示零值
   */
  showZero?: boolean
  
  /**
   * 自定义文本
   */
  text?: string
  
  /**
   * 是否显示徽章
   */
  show?: boolean
}

export interface BadgeEmits {
  /**
   * 点击事件
   */
  click: [event: MouseEvent]
}
