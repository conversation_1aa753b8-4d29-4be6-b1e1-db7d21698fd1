<template>
  <div :class="avatarClasses" @click="handleClick">
    <!-- 头像图片 -->
    <img
      v-if="src && !showInitials"
      :src="src"
      :alt="alt || name"
      :class="imageClasses"
      @error="handleImageError"
    />
    
    <!-- 首字母头像 -->
    <div
      v-if="showInitials"
      :class="initialsClasses"
      :style="{ backgroundColor: avatarColor }"
    >
      {{ initials }}
    </div>
    
    <!-- 在线状态指示器 -->
    <div
      v-if="showOnlineStatus"
      :class="statusClasses"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { AvatarProps, AvatarEmits } from './types'

const props = withDefaults(defineProps<AvatarProps>(), {
  size: 'md',
  showOnlineStatus: false,
  isOnline: false,
  square: false
})

const emit = defineEmits<AvatarEmits>()

// 是否显示首字母
const showInitials = ref(!props.src)

// 头像容器样式类
const avatarClasses = computed(() => [
  'avatar',
  `avatar-${props.size}`,
  {
    'avatar-square': props.square,
    'avatar-clickable': !!emit.click
  }
])

// 图片样式类
const imageClasses = computed(() => [
  'avatar-image'
])

// 首字母样式类
const initialsClasses = computed(() => [
  'avatar-initials',
  `avatar-initials-${props.size}`
])

// 状态指示器样式类
const statusClasses = computed(() => [
  'avatar-status',
  `avatar-status-${props.size}`,
  {
    'avatar-status-online': props.isOnline,
    'avatar-status-offline': !props.isOnline
  }
])

// 获取首字母
const initials = computed(() => {
  return props.name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
})

// 生成头像颜色
const avatarColor = computed(() => {
  if (props.fallbackColor) {
    return props.fallbackColor
  }
  
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  
  let hash = 0
  for (let i = 0; i < props.name.length; i++) {
    hash = props.name.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
})

// 处理图片加载错误
const handleImageError = (event: Event) => {
  showInitials.value = true
  emit('error', event)
}

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}
</script>

<style scoped>
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: var(--color-background-secondary);
  box-shadow: var(--shadow-sm);
}

.avatar-square {
  border-radius: var(--radius-lg);
}

.avatar-clickable {
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.avatar-clickable:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* 尺寸变体 */
.avatar-xs {
  width: var(--avatar-size-xs);
  height: var(--avatar-size-xs);
}

.avatar-sm {
  width: var(--avatar-size-sm);
  height: var(--avatar-size-sm);
}

.avatar-md {
  width: var(--avatar-size-md);
  height: var(--avatar-size-md);
}

.avatar-lg {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
}

.avatar-xl {
  width: var(--avatar-size-xl);
  height: var(--avatar-size-xl);
}

.avatar-2xl {
  width: var(--avatar-size-2xl);
  height: var(--avatar-size-2xl);
}

/* 图片样式 */
.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 首字母样式 */
.avatar-initials {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
}

.avatar-initials-xs {
  font-size: 10px;
}

.avatar-initials-sm {
  font-size: 12px;
}

.avatar-initials-md {
  font-size: 14px;
}

.avatar-initials-lg {
  font-size: var(--font-size-base);
}

.avatar-initials-xl {
  font-size: var(--font-size-lg);
}

.avatar-initials-2xl {
  font-size: var(--font-size-xl);
}

/* 在线状态指示器 */
.avatar-status {
  position: absolute;
  border: 2px solid var(--color-background-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-xs);
}

.avatar-status-xs {
  width: 8px;
  height: 8px;
  bottom: 0;
  right: 0;
}

.avatar-status-sm {
  width: 10px;
  height: 10px;
  bottom: 0;
  right: 0;
}

.avatar-status-md {
  width: var(--space-3);
  height: var(--space-3);
  bottom: var(--space-1);
  right: var(--space-1);
}

.avatar-status-lg {
  width: var(--space-3);
  height: var(--space-3);
  bottom: var(--space-1);
  right: var(--space-1);
}

.avatar-status-xl {
  width: var(--space-4);
  height: var(--space-4);
  bottom: var(--space-2);
  right: var(--space-2);
}

.avatar-status-2xl {
  width: var(--space-5);
  height: var(--space-5);
  bottom: var(--space-2);
  right: var(--space-2);
}

.avatar-status-online {
  background: var(--color-success);
}

.avatar-status-offline {
  background: var(--color-gray-400);
}
</style>
