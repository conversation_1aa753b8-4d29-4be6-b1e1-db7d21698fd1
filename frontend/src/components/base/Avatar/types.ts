export interface AvatarProps {
  /**
   * 头像图片地址
   */
  src?: string
  
  /**
   * 图片 alt 属性
   */
  alt?: string
  
  /**
   * 用户名，用于生成首字母和颜色
   */
  name: string
  
  /**
   * 头像尺寸
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  
  /**
   * 是否显示在线状态
   */
  showOnlineStatus?: boolean
  
  /**
   * 是否在线
   */
  isOnline?: boolean
  
  /**
   * 自定义背景色（用于首字母头像）
   */
  fallbackColor?: string
  
  /**
   * 是否为方形头像
   */
  square?: boolean
}

export interface AvatarEmits {
  /**
   * 图片加载错误事件
   */
  error: [event: Event]
  
  /**
   * 点击事件
   */
  click: [event: MouseEvent]
}
