import type { Component } from 'vue'

export interface ButtonProps {
  /**
   * 按钮变体
   */
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger'
  
  /**
   * 按钮尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 图标组件
   */
  icon?: Component
  
  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right'
  
  /**
   * 按钮类型
   */
  type?: 'button' | 'submit' | 'reset'
  
  /**
   * 是否为块级按钮
   */
  block?: boolean
  
  /**
   * 是否为圆形按钮
   */
  circle?: boolean
}

export interface ButtonEmits {
  /**
   * 点击事件
   */
  click: [event: MouseEvent]
}
