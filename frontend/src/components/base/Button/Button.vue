<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="button-loading">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 左侧图标 -->
    <component
      v-if="icon && iconPosition === 'left' && !loading"
      :is="icon"
      :class="iconClasses"
    />
    
    <!-- 按钮内容 -->
    <span v-if="$slots.default && !circle" :class="contentClasses">
      <slot />
    </span>
    
    <!-- 右侧图标 -->
    <component
      v-if="icon && iconPosition === 'right' && !loading"
      :is="icon"
      :class="iconClasses"
    />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ButtonProps, ButtonEmits } from './types'

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'md',
  type: 'button',
  iconPosition: 'left',
  disabled: false,
  loading: false,
  block: false,
  circle: false
})

const emit = defineEmits<ButtonEmits>()

// 按钮样式类
const buttonClasses = computed(() => [
  'btn',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-block': props.block,
    'btn-circle': props.circle,
    'btn-loading': props.loading,
    'btn-disabled': props.disabled
  }
])

// 图标样式类
const iconClasses = computed(() => [
  'btn-icon',
  `btn-icon-${props.size}`
])

// 内容样式类
const contentClasses = computed(() => [
  'btn-content',
  {
    'btn-content-with-icon': props.icon
  }
])

// 点击处理
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.btn:focus:not(:focus-visible) {
  outline: none;
}

/* 尺寸变体 */
.btn-sm {
  height: var(--button-height-sm);
  padding: 0 var(--space-3);
  font-size: var(--font-size-xs);
  gap: var(--space-1);
}

.btn-md {
  height: var(--button-height-md);
  padding: 0 var(--space-4);
  font-size: var(--font-size-sm);
  gap: var(--space-2);
}

.btn-lg {
  height: var(--button-height-lg);
  padding: 0 var(--space-5);
  font-size: var(--font-size-base);
  gap: var(--space-2);
}

/* 颜色变体 */
.btn-primary {
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
  border-color: var(--color-primary-500);
}

.btn-primary:hover:not(.btn-disabled):not(.btn-loading) {
  background: var(--color-primary-600);
  border-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  border-color: var(--color-border-secondary);
}

.btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border-color: transparent;
}

.btn-ghost:hover:not(.btn-disabled):not(.btn-loading) {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.btn-danger {
  background: var(--color-error);
  color: var(--color-text-inverse);
  border-color: var(--color-error);
}

.btn-danger:hover:not(.btn-disabled):not(.btn-loading) {
  background: var(--color-error-dark);
  border-color: var(--color-error-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 特殊状态 */
.btn-block {
  width: 100%;
}

.btn-circle {
  border-radius: var(--radius-full);
  padding: 0;
  aspect-ratio: 1;
}

.btn-circle.btn-sm {
  width: var(--button-height-sm);
}

.btn-circle.btn-md {
  width: var(--button-height-md);
}

.btn-circle.btn-lg {
  width: var(--button-height-lg);
}

.btn-loading {
  pointer-events: none;
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 图标样式 */
.btn-icon-sm {
  width: var(--icon-size-xs);
  height: var(--icon-size-xs);
}

.btn-icon-md {
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
}

.btn-icon-lg {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
}

/* 加载动画 */
.button-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 内容样式 */
.btn-content {
  display: flex;
  align-items: center;
}

/* 活跃状态 */
.btn:active:not(.btn-disabled):not(.btn-loading) {
  transform: translateY(0) scale(0.98);
}
</style>
