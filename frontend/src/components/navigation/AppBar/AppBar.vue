<template>
  <header :class="headerClasses">
    <div :class="contentClasses">
      <!-- 左侧区域 -->
      <div class="app-bar-left">
        <!-- 返回按钮 -->
        <button
          v-if="showBack"
          :class="backButtonClasses"
          @click="handleBack"
        >
          <ArrowLeft />
        </button>
        
        <!-- 标题 -->
        <h1 :class="titleClasses">
          <slot name="title">{{ title }}</slot>
        </h1>
      </div>
      
      <!-- 中间区域 -->
      <div v-if="$slots.center" class="app-bar-center">
        <slot name="center" />
      </div>
      
      <!-- 右侧操作区域 -->
      <div v-if="actions?.length || $slots.actions" :class="actionsClasses">
        <slot name="actions">
          <button
            v-for="(action, index) in actions"
            :key="action.key || index"
            :class="actionButtonClasses"
            :disabled="action.disabled"
            :title="action.tooltip"
            @click="handleAction(action, index)"
          >
            <component :is="action.icon" />
          </button>
        </slot>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowLeft } from 'lucide-vue-next'
import type { AppBarProps, AppBarEmits, AppBarAction } from './types'

const props = withDefaults(defineProps<AppBarProps>(), {
  title: '',
  showBack: false,
  sticky: true,
  bordered: true,
  variant: 'default'
})

const emit = defineEmits<AppBarEmits>()

// 头部样式类
const headerClasses = computed(() => [
  'app-bar',
  `app-bar-${props.variant}`,
  {
    'app-bar-sticky': props.sticky,
    'app-bar-bordered': props.bordered
  }
])

// 内容样式类
const contentClasses = computed(() => [
  'app-bar-content'
])

// 标题样式类
const titleClasses = computed(() => [
  'app-bar-title'
])

// 操作区域样式类
const actionsClasses = computed(() => [
  'app-bar-actions'
])

// 返回按钮样式类
const backButtonClasses = computed(() => [
  'app-bar-button',
  'app-bar-back'
])

// 操作按钮样式类
const actionButtonClasses = computed(() => [
  'app-bar-button',
  'app-bar-action'
])

// 处理返回
const handleBack = () => {
  emit('back')
}

// 处理操作按钮点击
const handleAction = (action: AppBarAction, index: number) => {
  if (!action.disabled) {
    action.onClick()
    emit('action', action.key || index.toString(), action)
  }
}
</script>

<style scoped>
.app-bar {
  background: var(--color-background-primary);
  padding: var(--space-3) var(--space-4);
  z-index: var(--z-index-sticky);
  transition: all var(--duration-fast) var(--ease-out);
}

.app-bar-sticky {
  position: sticky;
  top: 0;
}

.app-bar-bordered {
  border-bottom: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-xs);
}

.app-bar-transparent {
  background: transparent;
}

.app-bar-blur {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

/* 内容布局 */
.app-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 100%;
}

.app-bar-left {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  min-width: 0;
}

.app-bar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.app-bar-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-shrink: 0;
}

/* 标题样式 */
.app-bar-title {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

/* 按钮样式 */
.app-bar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.app-bar-button:hover:not(:disabled) {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.app-bar-button:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
}

.app-bar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.app-bar-button:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.app-bar-button:focus:not(:focus-visible) {
  outline: none;
}

/* 图标样式 */
.app-bar-button svg {
  width: 20px;
  height: 20px;
  transition: all var(--duration-fast) var(--ease-out);
}

.app-bar-button:hover:not(:disabled) svg {
  width: 22px;
  height: 22px;
}



/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .app-bar-blur {
    background: rgba(0, 0, 0, 0.8);
  }
}
</style>
