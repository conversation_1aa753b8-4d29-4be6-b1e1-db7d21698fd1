import type { Component } from 'vue'

export interface AppBarAction {
  /**
   * 图标组件
   */
  icon: Component
  
  /**
   * 点击事件处理函数
   */
  onClick: () => void
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 操作标识
   */
  key?: string
  
  /**
   * 工具提示
   */
  tooltip?: string
}

export interface AppBarProps {
  /**
   * 标题
   */
  title?: string
  
  /**
   * 是否显示返回按钮
   */
  showBack?: boolean
  
  /**
   * 操作按钮列表
   */
  actions?: AppBarAction[]
  
  /**
   * 是否固定在顶部
   */
  sticky?: boolean
  
  /**
   * 是否显示边框
   */
  bordered?: boolean
  
  /**
   * 背景色变体
   */
  variant?: 'default' | 'transparent' | 'blur'
}

export interface AppBarEmits {
  /**
   * 返回按钮点击事件
   */
  back: []
  
  /**
   * 操作按钮点击事件
   */
  action: [key: string, action: AppBarAction]
}
