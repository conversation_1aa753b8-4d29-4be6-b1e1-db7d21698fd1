<template>
  <div class="status-indicator">
    <!-- 在线状态点 -->
    <div 
      class="status-dot" 
      :class="statusClass"
      :title="statusText"
    ></div>
    
    <!-- 状态文本（可选） -->
    <span v-if="showText" class="status-text">
      {{ statusText }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  isOnline?: boolean
  lastActiveAt?: string
  showText?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  isOnline: false,
  showText: false,
  size: 'medium'
})

// 计算状态类名
const statusClass = computed(() => {
  const baseClass = `status-${props.size}`
  if (props.isOnline) {
    return `${baseClass} online`
  }
  return `${baseClass} offline`
})

// 计算状态文本
const statusText = computed(() => {
  if (props.isOnline) {
    return '在线'
  }
  
  if (props.lastActiveAt) {
    return getLastActiveText(props.lastActiveAt)
  }
  
  return '离线'
})

// 获取最后活跃时间文本
const getLastActiveText = (lastActiveAt: string): string => {
  const date = new Date(lastActiveAt)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚在线'
  if (minutes < 60) return `${minutes}分钟前在线`
  if (hours < 24) return `${hours}小时前在线`
  if (days < 7) return `${days}天前在线`
  
  return '很久未在线'
}
</script>

<style scoped>
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-dot {
  border-radius: 50%;
  flex-shrink: 0;
  position: relative;
}

/* 尺寸变体 */
.status-small {
  width: 8px;
  height: 8px;
}

.status-medium {
  width: 10px;
  height: 10px;
}

.status-large {
  width: 12px;
  height: 12px;
}

/* 状态颜色 */
.online {
  background: var(--color-success);
  box-shadow: 0 0 0 2px var(--color-background-primary);
}

.offline {
  background: var(--color-text-tertiary);
  box-shadow: 0 0 0 2px var(--color-background-primary);
}

/* 在线状态动画 */
.online::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: var(--color-success);
  animation: pulse 2s infinite;
}

.status-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  white-space: nowrap;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
