<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3 class="modal-title">添加好友</h3>
        <button class="close-button" @click="$emit('cancel')">
          <X :size="20" />
        </button>
      </div>

      <!-- 用户信息 -->
      <div class="user-info">
        <div class="user-avatar">
          <img
            v-if="user?.avatar_url"
            :src="user.avatar_url"
            :alt="user.nickname || user.username"
            class="avatar-image"
          />
          <div v-else class="avatar-placeholder">
            {{ getAvatarText(user?.nickname || user?.username || '') }}
          </div>
        </div>
        <div class="user-details">
          <h4 class="user-name">{{ user?.nickname || user?.username }}</h4>
          <p class="user-username">@{{ user?.username }}</p>
        </div>
      </div>

      <!-- 验证消息 -->
      <div class="message-section">
        <label class="message-label">验证消息</label>
        <textarea
          v-model="message"
          class="message-input"
          placeholder="请输入验证消息（可选）"
          rows="3"
          maxlength="200"
        ></textarea>
        <div class="message-counter">
          {{ message.length }}/200
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <button class="cancel-button" @click="$emit('cancel')">
          取消
        </button>
        <button 
          class="confirm-button" 
          :disabled="loading"
          @click="handleConfirm"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '发送中...' : '发送请求' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { X } from 'lucide-vue-next'
import type { User } from '@/services/api'

interface Props {
  user: User | null
}

interface Emits {
  (e: 'confirm', message: string): void
  (e: 'cancel'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const message = ref('')
const loading = ref(false)

// 获取头像文字
const getAvatarText = (name: string): string => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  emit('cancel')
}

// 处理确认
const handleConfirm = async () => {
  loading.value = true
  try {
    emit('confirm', message.value.trim())
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal-content {
  background: var(--color-background-primary);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-4) var(--space-3);
  border-bottom: 1px solid var(--color-border-primary);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-username {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-section {
  padding: 0 var(--space-4) var(--space-4);
}

.message-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.message-input {
  width: 100%;
  min-height: 80px;
  padding: var(--space-3);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  resize: vertical;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.message-input::placeholder {
  color: var(--color-text-tertiary);
}

.message-counter {
  text-align: right;
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin-top: var(--space-1);
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  border-top: 1px solid var(--color-border-primary);
}

.cancel-button,
.confirm-button {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.cancel-button {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.cancel-button:hover {
  background: var(--color-background-tertiary);
}

.confirm-button {
  background: var(--color-primary);
  color: white;
}

.confirm-button:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.confirm-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
