<template>
  <Teleport to="body">
    <Transition
      name="toast"
      enter-active-class="toast-enter-active"
      leave-active-class="toast-leave-active"
      enter-from-class="toast-enter-from"
      leave-to-class="toast-leave-to"
    >
      <div
        v-if="visible"
        class="toast-container"
        :class="`toast-${type}`"
        @click="handleClick"
      >
        <div class="toast-content">
          <div class="toast-icon">
            <CheckCircle v-if="type === 'success'" />
            <XCircle v-else-if="type === 'error'" />
            <AlertTriangle v-else-if="type === 'warning'" />
            <Info v-else-if="type === 'info'" />
          </div>
          <div class="toast-message">{{ message }}</div>
          <button
            v-if="closable"
            class="toast-close"
            @click.stop="close"
          >
            <X :size="16" />
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-vue-next'

export interface ToastProps {
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  closable?: boolean
  onClick?: () => void
  onClose?: () => void
}

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'info',
  duration: 3000,
  closable: true
})

const visible = ref(false)
let timer: number | null = null

const show = () => {
  visible.value = true
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const close = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  setTimeout(() => {
    props.onClose?.()
  }, 300) // 等待动画完成
}

const handleClick = () => {
  props.onClick?.()
}

onMounted(() => {
  show()
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})

defineExpose({
  close
})
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: var(--space-5, 20px);
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  width: calc(100vw - var(--space-6));
  max-width: 420px;
  min-width: 280px;
  padding: 0;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  cursor: pointer;
  user-select: none;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-lg);
  border: 1px solid;
  font-family: var(--font-family-primary);
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  word-break: break-word;
}

.toast-close {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--space-6);
  height: var(--space-6);
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* Toast类型样式 */
.toast-success {
  background: var(--color-success);
}

.toast-success .toast-content {
  border-color: var(--color-success-light);
  color: var(--color-text-inverse);
}

.toast-error {
  background: var(--color-error);
}

.toast-error .toast-content {
  border-color: var(--color-error-light);
  color: var(--color-text-inverse);
}

.toast-warning {
  background: var(--color-warning);
}

.toast-warning .toast-content {
  border-color: var(--color-warning-light);
  color: var(--color-text-inverse);
}

.toast-info {
  background: var(--color-info);
}

.toast-info .toast-content {
  border-color: var(--color-info-light);
  color: var(--color-text-inverse);
}

/* 动画 - 增强版 */
.toast-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(-30px) scale(0.8);
  filter: blur(4px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-30px) scale(0.8);
  filter: blur(4px);
}

/* 悬停动画效果 */
.toast-container {
  transition: all 0.2s ease-in-out;
}

.toast-container:hover {
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 点击动画效果 */
.toast-container:active {
  transform: translateX(-50%) translateY(0) scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* 关闭按钮动画 */
.toast-close {
  transition: all 0.2s ease-in-out;
}

.toast-close:hover {
  transform: scale(1.1) rotate(90deg);
  background-color: rgba(255, 255, 255, 0.2);
}

.toast-close:active {
  transform: scale(0.95) rotate(90deg);
}

/* 图标样式和动画 */
.toast-icon {
  transition: transform 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-icon svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease-in-out;
}

.toast-container:hover .toast-icon {
  transform: scale(1.1) rotate(5deg);
}

.toast-container:hover .toast-icon svg {
  width: 24px;
  height: 24px;
}

/* 进入时的弹性动画 */
@keyframes toast-bounce-in {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-50px) scale(0.3) rotate(10deg);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(10px) scale(1.05) rotate(-2deg);
  }
  70% {
    transform: translateX(-50%) translateY(-5px) scale(0.98) rotate(1deg);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1) rotate(0deg);
  }
}

/* 离开时的淡出动画 */
@keyframes toast-fade-out {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px) scale(0.8);
  }
}

/* 应用动画 */
.toast-enter-active .toast-container {
  animation: toast-bounce-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-leave-active .toast-container {
  animation: toast-fade-out 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .toast-success {
    background: var(--color-success-dark);
  }
  
  .toast-error {
    background: var(--color-error-dark);
  }
  
  .toast-warning {
    background: var(--color-warning-dark);
  }
  
  .toast-info {
    background: var(--color-info-dark);
  }
}


</style>