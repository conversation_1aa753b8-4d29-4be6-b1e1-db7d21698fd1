<template>
  <div :class="containerClasses">
    <!-- 图标 -->
    <div v-if="icon || $slots.icon" :class="iconClasses">
      <slot name="icon">
        <component
          v-if="icon"
          :is="icon"
          :color="iconColor"
          :size="iconSizeValue"
        />
      </slot>
    </div>
    
    <!-- 标题 -->
    <h3 :class="titleClasses">
      <slot name="title">{{ title }}</slot>
    </h3>
    
    <!-- 描述 -->
    <p v-if="description || $slots.description" :class="descriptionClasses">
      <slot name="description">{{ description }}</slot>
    </p>
    
    <!-- 操作按钮 -->
    <div v-if="actionText || $slots.action" :class="actionClasses">
      <slot name="action">
        <button
          v-if="actionText"
          :class="buttonClasses"
          @click="handleAction"
        >
          {{ actionText }}
        </button>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { EmptyStateProps, EmptyStateEmits } from './types'

const props = withDefaults(defineProps<EmptyStateProps>(), {
  iconSize: 'lg',
  iconColor: '#6c757d'
})

const emit = defineEmits<EmptyStateEmits>()

// 容器样式类
const containerClasses = computed(() => [
  'empty-state'
])

// 图标样式类
const iconClasses = computed(() => [
  'empty-icon',
  `empty-icon-${props.iconSize}`
])

// 标题样式类
const titleClasses = computed(() => [
  'empty-title'
])

// 描述样式类
const descriptionClasses = computed(() => [
  'empty-description'
])

// 操作区域样式类
const actionClasses = computed(() => [
  'empty-action'
])

// 按钮样式类
const buttonClasses = computed(() => [
  'empty-button'
])

// 图标尺寸值
const iconSizeValue = computed(() => {
  const sizeMap = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 80
  }
  return sizeMap[props.iconSize]
})

// 处理操作按钮点击
const handleAction = () => {
  emit('action')
}
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-10) var(--space-5);
  min-height: 200px;
}

/* 图标样式 */
.empty-icon {
  margin-bottom: var(--space-6);
  opacity: 0.6;
  transition: all var(--duration-normal) var(--ease-out);
}

.empty-icon-sm {
  margin-bottom: var(--space-4);
}

.empty-icon-md {
  margin-bottom: var(--space-5);
}

.empty-icon-lg {
  margin-bottom: var(--space-6);
}

.empty-icon-xl {
  margin-bottom: var(--space-8);
}

/* 标题样式 */
.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
  line-height: var(--line-height-tight);
}

/* 描述样式 */
.empty-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-6) 0;
  line-height: var(--line-height-normal);
  max-width: 400px;
}

/* 操作区域 */
.empty-action {
  margin-top: var(--space-2);
}

/* 按钮样式 */
.empty-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: var(--button-height-md);
  padding: 0 var(--space-5);
  border: 1px solid var(--color-primary-500);
  border-radius: var(--radius-lg);
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  user-select: none;
}

.empty-button:hover {
  background: var(--color-primary-600);
  border-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.empty-button:active {
  transform: translateY(0) scale(0.98);
}

.empty-button:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.empty-button:focus:not(:focus-visible) {
  outline: none;
}

/* 悬停动画 */
.empty-state:hover .empty-icon {
  opacity: 0.8;
  transform: scale(1.05);
}



/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .empty-icon {
    opacity: 0.5;
  }
  
  .empty-state:hover .empty-icon {
    opacity: 0.7;
  }
}
</style>
