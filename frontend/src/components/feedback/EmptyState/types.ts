import type { Component } from 'vue'

export interface EmptyStateProps {
  /**
   * 图标组件
   */
  icon?: Component
  
  /**
   * 标题
   */
  title: string
  
  /**
   * 描述文本
   */
  description?: string
  
  /**
   * 操作按钮文本
   */
  actionText?: string
  
  /**
   * 图标大小
   */
  iconSize?: 'sm' | 'md' | 'lg' | 'xl'
  
  /**
   * 图标颜色
   */
  iconColor?: string
}

export interface EmptyStateEmits {
  /**
   * 操作按钮点击事件
   */
  action: []
}
