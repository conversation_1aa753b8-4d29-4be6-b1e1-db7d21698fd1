<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3 class="modal-title">{{ title }}</h3>
      </div>

      <!-- 内容 -->
      <div class="modal-body">
        <p class="modal-message">{{ message }}</p>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <button class="cancel-button" @click="$emit('cancel')">
          {{ cancelText }}
        </button>
        <button 
          class="confirm-button" 
          :class="{ danger: type === 'danger' }"
          @click="$emit('confirm')"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'default' | 'danger'
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

withDefaults(defineProps<Props>(), {
  confirmText: '确定',
  cancelText: '取消',
  type: 'default'
})

defineEmits<Emits>()

// 处理遮罩层点击
const handleOverlayClick = () => {
  // 可以选择是否允许点击遮罩层关闭
  // emit('cancel')
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal-content {
  background: var(--color-background-primary);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 400px;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  padding: var(--space-4) var(--space-4) var(--space-3);
  border-bottom: 1px solid var(--color-border-primary);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  text-align: center;
}

.modal-body {
  padding: var(--space-4);
}

.modal-message {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin: 0;
  text-align: center;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  border-top: 1px solid var(--color-border-primary);
}

.cancel-button,
.confirm-button {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.cancel-button:hover {
  background: var(--color-background-tertiary);
}

.confirm-button {
  background: var(--color-primary);
  color: white;
}

.confirm-button:hover {
  background: var(--color-primary-dark);
}

.confirm-button.danger {
  background: var(--color-danger);
}

.confirm-button.danger:hover {
  background: var(--color-danger-dark);
}
</style>
