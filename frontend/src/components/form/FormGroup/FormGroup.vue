<template>
  <div :class="containerClasses">
    <!-- 标签 -->
    <label
      v-if="label || $slots.label"
      :class="labelClasses"
      :style="labelStyles"
    >
      <slot name="label">
        {{ label }}
        <span v-if="required" class="form-group-required">*</span>
      </slot>
    </label>
    
    <!-- 表单控件容器 -->
    <div :class="controlClasses">
      <!-- 表单控件 -->
      <div :class="fieldClasses">
        <slot />
      </div>
      
      <!-- 错误信息 -->
      <div v-if="errorMessage && status === 'error'" :class="errorClasses">
        {{ errorMessage }}
      </div>
      
      <!-- 帮助文本 -->
      <div v-if="helpText && status !== 'error'" :class="helpClasses">
        {{ helpText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { FormGroupProps, FormGroupEmits } from './types'

const props = withDefaults(defineProps<FormGroupProps>(), {
  required: false,
  status: 'default',
  size: 'md',
  labelPosition: 'top',
  disabled: false
})

defineEmits<FormGroupEmits>()

// 容器样式类
const containerClasses = computed(() => [
  'form-group',
  `form-group-${props.size}`,
  `form-group-${props.status}`,
  `form-group-label-${props.labelPosition}`,
  {
    'form-group-disabled': props.disabled,
    'form-group-required': props.required
  }
])

// 标签样式类
const labelClasses = computed(() => [
  'form-group-label',
  `form-group-label-${props.size}`
])

// 标签样式
const labelStyles = computed(() => {
  if (props.labelPosition === 'left' || props.labelPosition === 'right') {
    return {
      width: props.labelWidth || 'auto',
      flexShrink: 0
    }
  }
  return {}
})

// 控件容器样式类
const controlClasses = computed(() => [
  'form-group-control',
  {
    'form-group-control-flex': props.labelPosition === 'left' || props.labelPosition === 'right'
  }
])

// 字段样式类
const fieldClasses = computed(() => [
  'form-group-field'
])

// 错误信息样式类
const errorClasses = computed(() => [
  'form-group-error',
  `form-group-error-${props.size}`
])

// 帮助文本样式类
const helpClasses = computed(() => [
  'form-group-help',
  `form-group-help-${props.size}`
])
</script>

<style scoped>
.form-group {
  display: flex;
  flex-direction: column;
  transition: all var(--duration-fast) var(--ease-out);
}

/* 标签位置变体 */
.form-group-label-left,
.form-group-label-right {
  flex-direction: row;
  align-items: flex-start;
}

.form-group-label-right {
  flex-direction: row-reverse;
}

/* 尺寸变体 - 移除下间距，由父容器的gap控制 */
.form-group-sm {
  /* margin-bottom: var(--space-4); */
}

.form-group-md {
  /* margin-bottom: var(--space-5); */
}

.form-group-lg {
  /* margin-bottom: var(--space-6); */
}

/* 禁用状态 */
.form-group-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 标签 */
.form-group-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  transition: color var(--duration-fast) var(--ease-out);
}

.form-group-label-sm {
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-2);
}

.form-group-label-md {
  font-size: var(--font-size-base);
  margin-bottom: var(--space-2);
}

.form-group-label-lg {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-3);
}

/* 左右标签布局 */
.form-group-label-left .form-group-label,
.form-group-label-right .form-group-label {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  min-height: var(--input-height-md);
}

.form-group-label-left .form-group-label {
  margin-right: var(--space-4);
}

.form-group-label-right .form-group-label {
  margin-left: var(--space-4);
}

/* 必填标记 */
.form-group-required {
  color: var(--color-error);
  margin-left: var(--space-1);
}

/* 控件容器 */
.form-group-control {
  display: flex;
  flex-direction: column;
}

.form-group-control-flex {
  flex: 1;
  min-width: 0;
}

/* 字段容器 */
.form-group-field {
  position: relative;
}

/* 错误信息 */
.form-group-error {
  color: var(--color-error);
  line-height: var(--line-height-tight);
  margin-top: var(--space-1);
}

.form-group-error-sm {
  font-size: var(--font-size-xs);
}

.form-group-error-md {
  font-size: var(--font-size-xs);
}

.form-group-error-lg {
  font-size: var(--font-size-sm);
}

/* 帮助文本 */
.form-group-help {
  color: var(--color-text-tertiary);
  line-height: var(--line-height-tight);
  margin-top: var(--space-1);
}

.form-group-help-sm {
  font-size: var(--font-size-xs);
}

.form-group-help-md {
  font-size: var(--font-size-xs);
}

.form-group-help-lg {
  font-size: var(--font-size-sm);
}

/* 验证状态 */
.form-group-success .form-group-label {
  color: var(--color-success);
}

.form-group-warning .form-group-label {
  color: var(--color-warning);
}

.form-group-error .form-group-label {
  color: var(--color-error);
}



/* 聚焦状态 */
.form-group:focus-within .form-group-label {
  color: var(--color-primary-600);
}

.form-group-success:focus-within .form-group-label {
  color: var(--color-success);
}

.form-group-warning:focus-within .form-group-label {
  color: var(--color-warning);
}

.form-group-error:focus-within .form-group-label {
  color: var(--color-error);
}
</style>
