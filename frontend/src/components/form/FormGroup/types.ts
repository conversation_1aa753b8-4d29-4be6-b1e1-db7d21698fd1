export interface FormGroupProps {
  /**
   * 标签文本
   */
  label?: string
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 错误信息
   */
  errorMessage?: string
  
  /**
   * 帮助文本
   */
  helpText?: string
  
  /**
   * 验证状态
   */
  status?: 'default' | 'success' | 'warning' | 'error'
  
  /**
   * 表单组尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 标签位置
   */
  labelPosition?: 'top' | 'left' | 'right'
  
  /**
   * 标签宽度（当 labelPosition 为 left 或 right 时有效）
   */
  labelWidth?: string
  
  /**
   * 是否禁用
   */
  disabled?: boolean
}

export interface FormGroupEmits {
  // FormGroup 组件通常不需要自定义事件
  // 事件由内部的表单控件处理
}
