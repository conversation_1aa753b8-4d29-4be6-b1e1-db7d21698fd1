export interface SearchBarProps {
  /**
   * 输入值
   */
  modelValue: string
  
  /**
   * 占位符文本
   */
  placeholder?: string
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否可清除
   */
  clearable?: boolean
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 输入框尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 是否自动聚焦
   */
  autofocus?: boolean
  
  /**
   * 最大长度
   */
  maxlength?: number
}

export interface SearchBarEmits {
  /**
   * 输入值更新事件
   */
  'update:modelValue': [value: string]
  
  /**
   * 输入事件
   */
  input: [event: Event]
  
  /**
   * 搜索事件
   */
  search: [value: string]
  
  /**
   * 清除事件
   */
  clear: []
  
  /**
   * 聚焦事件
   */
  focus: [event: FocusEvent]
  
  /**
   * 失焦事件
   */
  blur: [event: FocusEvent]
}
