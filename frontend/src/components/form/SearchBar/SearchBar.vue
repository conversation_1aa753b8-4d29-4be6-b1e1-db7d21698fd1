<template>
  <div :class="containerClasses">
    <!-- 搜索图标 -->
    <Search :class="iconClasses" />
    
    <!-- 输入框 -->
    <input
      ref="inputRef"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :class="inputClasses"
      type="text"
      @input="handleInput"
      @keyup.enter="handleSearch"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    
    <!-- 加载指示器 -->
    <div v-if="loading" :class="loadingClasses">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 清除按钮 -->
    <button
      v-else-if="clearable && modelValue"
      :class="clearClasses"
      type="button"
      @click="handleClear"
    >
      <X :size="16" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search, X } from 'lucide-vue-next'
import type { SearchBarProps, SearchBarEmits } from './types'

const props = withDefaults(defineProps<SearchBarProps>(), {
  placeholder: 'Search...',
  disabled: false,
  clearable: true,
  loading: false,
  size: 'md',
  autofocus: false
})

const emit = defineEmits<SearchBarEmits>()

const inputRef = ref<HTMLInputElement>()

// 容器样式类
const containerClasses = computed(() => [
  'search-bar',
  `search-bar-${props.size}`,
  {
    'search-bar-disabled': props.disabled,
    'search-bar-loading': props.loading
  }
])

// 输入框样式类
const inputClasses = computed(() => [
  'search-input'
])

// 图标样式类
const iconClasses = computed(() => [
  'search-icon'
])

// 加载指示器样式类
const loadingClasses = computed(() => [
  'search-loading'
])

// 清除按钮样式类
const clearClasses = computed(() => [
  'search-clear'
])

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
  emit('input', event)
}

// 处理搜索
const handleSearch = () => {
  emit('search', props.modelValue)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
  inputRef.value?.focus()
}

// 处理聚焦
const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

// 处理失焦
const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 自动聚焦
onMounted(() => {
  if (props.autofocus) {
    inputRef.value?.focus()
  }
})

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped>
.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-full);
  transition: all var(--duration-fast) var(--ease-out);
}

.search-bar:focus-within {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-bar-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 尺寸变体 */
.search-bar-sm {
  height: var(--input-height-sm);
  padding: 0 var(--space-3);
  gap: var(--space-2);
}

.search-bar-md {
  height: var(--input-height-md);
  padding: 0 var(--space-3);
  gap: var(--space-2);
}

.search-bar-lg {
  height: var(--input-height-lg);
  padding: 0 var(--space-4);
  gap: var(--space-3);
}

/* 搜索图标 */
.search-icon {
  color: var(--color-text-tertiary);
  flex-shrink: 0;
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
}

/* 输入框 */
.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  min-width: 0;
}

.search-input::placeholder {
  color: var(--color-text-tertiary);
}

.search-input:disabled {
  cursor: not-allowed;
}

/* 加载指示器 */
.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 清除按钮 */
.search-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--space-6);
  height: var(--space-6);
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  color: var(--color-text-tertiary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  flex-shrink: 0;
}

.search-clear:hover {
  color: var(--color-text-secondary);
  background: var(--color-background-tertiary);
}

.search-clear:active {
  transform: scale(0.95);
}


</style>
