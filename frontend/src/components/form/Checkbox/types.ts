export interface CheckboxProps {
  /**
   * 选中状态
   */
  modelValue: boolean
  
  /**
   * 标签文本
   */
  label?: string
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 复选框尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 是否为中间状态
   */
  indeterminate?: boolean
  
  /**
   * 复选框值（用于表单提交）
   */
  value?: string | number
  
  /**
   * 复选框名称
   */
  name?: string
  
  /**
   * 验证状态
   */
  status?: 'default' | 'success' | 'warning' | 'error'
  
  /**
   * 错误信息
   */
  errorMessage?: string
}

export interface CheckboxEmits {
  /**
   * 选中状态更新事件
   */
  'update:modelValue': [value: boolean]
  
  /**
   * 变化事件
   */
  change: [value: boolean, event: Event]
  
  /**
   * 聚焦事件
   */
  focus: [event: FocusEvent]
  
  /**
   * 失焦事件
   */
  blur: [event: FocusEvent]
}
