<template>
  <div :class="containerClasses">
    <label :class="labelClasses">
      <!-- 隐藏的原生复选框 -->
      <input
        ref="checkboxRef"
        type="checkbox"
        :checked="modelValue"
        :disabled="disabled"
        :required="required"
        :value="value"
        :name="name"
        :class="inputClasses"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 自定义复选框外观 -->
      <span :class="checkmarkClasses">
        <!-- 选中状态的勾选图标 -->
        <Check v-if="modelValue && !indeterminate" :class="iconClasses" />
        <!-- 中间状态的横线图标 -->
        <Minus v-else-if="indeterminate" :class="iconClasses" />
      </span>
      
      <!-- 标签文本 -->
      <span v-if="label || $slots.default" :class="textClasses">
        <slot>{{ label }}</slot>
      </span>
    </label>
    
    <!-- 错误信息 -->
    <div v-if="errorMessage && status === 'error'" :class="errorClasses">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Check, Minus } from 'lucide-vue-next'
import type { CheckboxProps, CheckboxEmits } from './types'

const props = withDefaults(defineProps<CheckboxProps>(), {
  disabled: false,
  required: false,
  size: 'md',
  indeterminate: false,
  status: 'default'
})

const emit = defineEmits<CheckboxEmits>()

const checkboxRef = ref<HTMLInputElement>()

// 容器样式类
const containerClasses = computed(() => [
  'checkbox-container',
  `checkbox-container-${props.size}`,
  `checkbox-container-${props.status}`,
  {
    'checkbox-container-disabled': props.disabled
  }
])

// 标签样式类
const labelClasses = computed(() => [
  'checkbox-label',
  {
    'checkbox-label-disabled': props.disabled
  }
])

// 输入框样式类
const inputClasses = computed(() => [
  'checkbox-input'
])

// 复选框外观样式类
const checkmarkClasses = computed(() => [
  'checkbox-checkmark',
  `checkbox-checkmark-${props.size}`,
  {
    'checkbox-checkmark-checked': props.modelValue,
    'checkbox-checkmark-indeterminate': props.indeterminate,
    'checkbox-checkmark-disabled': props.disabled
  }
])

// 图标样式类
const iconClasses = computed(() => [
  'checkbox-icon'
])

// 文本样式类
const textClasses = computed(() => [
  'checkbox-text',
  `checkbox-text-${props.size}`
])

// 错误信息样式类
const errorClasses = computed(() => [
  'checkbox-error'
])

// 处理变化
const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.checked)
  emit('change', target.checked, event)
}

// 处理聚焦
const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

// 处理失焦
const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 监听中间状态变化
watch(() => props.indeterminate, (newVal) => {
  if (checkboxRef.value) {
    checkboxRef.value.indeterminate = newVal
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  focus: () => checkboxRef.value?.focus(),
  blur: () => checkboxRef.value?.blur()
})
</script>

<style scoped>
.checkbox-container {
  position: relative;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.checkbox-label-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 隐藏原生复选框 */
.checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 自定义复选框外观 */
.checkbox-checkmark {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-border-secondary);
  border-radius: var(--radius-sm);
  background: var(--color-background-primary);
  transition: all var(--duration-fast) var(--ease-out);
  flex-shrink: 0;
}

/* 尺寸变体 */
.checkbox-checkmark-sm {
  width: 16px;
  height: 16px;
}

.checkbox-checkmark-md {
  width: 18px;
  height: 18px;
}

.checkbox-checkmark-lg {
  width: 20px;
  height: 20px;
}

/* 选中状态 */
.checkbox-checkmark-checked {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

/* 中间状态 */
.checkbox-checkmark-indeterminate {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

/* 禁用状态 */
.checkbox-checkmark-disabled {
  background: var(--color-background-tertiary);
  border-color: var(--color-border-tertiary);
}

/* 悬停效果 */
.checkbox-label:not(.checkbox-label-disabled):hover .checkbox-checkmark {
  border-color: var(--color-primary-400);
  transform: scale(1.05);
}

/* 聚焦效果 */
.checkbox-input:focus + .checkbox-checkmark {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.checkbox-input:focus:not(:focus-visible) + .checkbox-checkmark {
  outline: none;
}

/* 验证状态 */
.checkbox-container-success .checkbox-checkmark {
  border-color: var(--color-success);
}

.checkbox-container-success .checkbox-checkmark-checked {
  background: var(--color-success);
  border-color: var(--color-success);
}

.checkbox-container-warning .checkbox-checkmark {
  border-color: var(--color-warning);
}

.checkbox-container-warning .checkbox-checkmark-checked {
  background: var(--color-warning);
  border-color: var(--color-warning);
}

.checkbox-container-error .checkbox-checkmark {
  border-color: var(--color-error);
}

.checkbox-container-error .checkbox-checkmark-checked {
  background: var(--color-error);
  border-color: var(--color-error);
}

/* 图标 */
.checkbox-icon {
  color: var(--color-text-inverse);
  transition: all var(--duration-fast) var(--ease-out);
}

.checkbox-checkmark-sm .checkbox-icon {
  width: 10px;
  height: 10px;
}

.checkbox-checkmark-md .checkbox-icon {
  width: 12px;
  height: 12px;
}

.checkbox-checkmark-lg .checkbox-icon {
  width: 14px;
  height: 14px;
}

/* 标签文本 */
.checkbox-text {
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  transition: color var(--duration-fast) var(--ease-out);
}

.checkbox-text-sm {
  font-size: var(--font-size-sm);
  margin-left: var(--space-2);
}

.checkbox-text-md {
  font-size: var(--font-size-base);
  margin-left: var(--space-2);
}

.checkbox-text-lg {
  font-size: var(--font-size-lg);
  margin-left: var(--space-3);
}

.checkbox-label-disabled .checkbox-text {
  color: var(--color-text-tertiary);
}

/* 错误信息 */
.checkbox-error {
  margin-top: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}

/* 活跃状态 */
.checkbox-label:active:not(.checkbox-label-disabled) .checkbox-checkmark {
  transform: scale(0.95);
}


</style>
