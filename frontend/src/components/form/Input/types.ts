import type { Component } from 'vue'

export interface InputProps {
  /**
   * 输入值
   */
  modelValue: string | number
  
  /**
   * 输入框类型
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  
  /**
   * 占位符文本
   */
  placeholder?: string
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否只读
   */
  readonly?: boolean
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 输入框尺寸
   */
  size?: 'sm' | 'md' | 'lg'
  
  /**
   * 验证状态
   */
  status?: 'default' | 'success' | 'warning' | 'error'
  
  /**
   * 错误信息
   */
  errorMessage?: string
  
  /**
   * 是否自动聚焦
   */
  autofocus?: boolean
  
  /**
   * 最大长度
   */
  maxlength?: number
  
  /**
   * 最小长度
   */
  minlength?: number
  
  /**
   * 前置图标
   */
  prefixIcon?: Component
  
  /**
   * 后置图标
   */
  suffixIcon?: Component
  
  /**
   * 是否显示密码切换按钮（仅当 type="password" 时有效）
   */
  showPasswordToggle?: boolean
  
  /**
   * 是否可清除
   */
  clearable?: boolean
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 输入框名称
   */
  name?: string
  
  /**
   * 自动完成
   */
  autocomplete?: string
}

export interface InputEmits {
  /**
   * 输入值更新事件
   */
  'update:modelValue': [value: string | number]
  
  /**
   * 输入事件
   */
  input: [event: Event]
  
  /**
   * 变化事件
   */
  change: [event: Event]
  
  /**
   * 聚焦事件
   */
  focus: [event: FocusEvent]
  
  /**
   * 失焦事件
   */
  blur: [event: FocusEvent]
  
  /**
   * 按键事件
   */
  keydown: [event: KeyboardEvent]
  
  /**
   * 回车事件
   */
  enter: [event: KeyboardEvent]
  
  /**
   * 清除事件
   */
  clear: []
}
