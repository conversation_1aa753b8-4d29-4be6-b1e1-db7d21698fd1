<template>
  <div :class="containerClasses">
    <!-- 前置图标 -->
    <component
      v-if="prefixIcon"
      :is="prefixIcon"
      :class="prefixIconClasses"
    />
    
    <!-- 输入框 -->
    <input
      ref="inputRef"
      :value="modelValue"
      :type="currentType"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :required="required"
      :maxlength="maxlength"
      :minlength="minlength"
      :name="name"
      :autocomplete="autocomplete"
      :class="inputClasses"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @keyup.enter="handleEnter"
    />
    
    <!-- 加载指示器 -->
    <div v-if="loading" :class="loadingClasses">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 密码切换按钮 -->
    <button
      v-else-if="showPasswordToggle && type === 'password'"
      :class="toggleClasses"
      type="button"
      @click="togglePasswordVisibility"
    >
      <Eye v-if="showPassword" :size="16" />
      <EyeOff v-else :size="16" />
    </button>
    
    <!-- 清除按钮 -->
    <button
      v-else-if="clearable && modelValue && !disabled && !readonly"
      :class="clearClasses"
      type="button"
      @click="handleClear"
    >
      <X :size="16" />
    </button>
    
    <!-- 后置图标 -->
    <component
      v-else-if="suffixIcon"
      :is="suffixIcon"
      :class="suffixIconClasses"
    />
    
    <!-- 错误信息 -->
    <div v-if="errorMessage && status === 'error'" :class="errorClasses">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Eye, EyeOff, X } from 'lucide-vue-next'
import type { InputProps, InputEmits } from './types'

const props = withDefaults(defineProps<InputProps>(), {
  type: 'text',
  placeholder: '',
  disabled: false,
  readonly: false,
  required: false,
  size: 'md',
  status: 'default',
  autofocus: false,
  showPasswordToggle: true,
  clearable: false,
  loading: false,
  autocomplete: 'off'
})

const emit = defineEmits<InputEmits>()

const inputRef = ref<HTMLInputElement>()
const showPassword = ref(false)

// 当前输入框类型
const currentType = computed(() => {
  if (props.type === 'password' && showPassword.value) {
    return 'text'
  }
  return props.type
})

// 容器样式类
const containerClasses = computed(() => [
  'input-container',
  `input-container-${props.size}`,
  `input-container-${props.status}`,
  {
    'input-container-disabled': props.disabled,
    'input-container-readonly': props.readonly,
    'input-container-loading': props.loading,
    'input-container-with-prefix': props.prefixIcon,
    'input-container-with-suffix': props.suffixIcon || props.clearable || (props.showPasswordToggle && props.type === 'password')
  }
])

// 输入框样式类
const inputClasses = computed(() => [
  'input-field'
])

// 前置图标样式类
const prefixIconClasses = computed(() => [
  'input-icon',
  'input-prefix-icon'
])

// 后置图标样式类
const suffixIconClasses = computed(() => [
  'input-icon',
  'input-suffix-icon'
])

// 加载指示器样式类
const loadingClasses = computed(() => [
  'input-loading'
])

// 密码切换按钮样式类
const toggleClasses = computed(() => [
  'input-action-btn',
  'input-password-toggle'
])

// 清除按钮样式类
const clearClasses = computed(() => [
  'input-action-btn',
  'input-clear'
])

// 错误信息样式类
const errorClasses = computed(() => [
  'input-error'
])

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('update:modelValue', value)
  emit('input', event)
}

// 处理变化
const handleChange = (event: Event) => {
  emit('change', event)
}

// 处理聚焦
const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

// 处理失焦
const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 处理按键
const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}

// 处理回车
const handleEnter = (event: KeyboardEvent) => {
  emit('enter', event)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
  inputRef.value?.focus()
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 自动聚焦
onMounted(() => {
  if (props.autofocus) {
    inputRef.value?.focus()
  }
})

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  select: () => inputRef.value?.select()
})
</script>

<style scoped>
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-out);
}

.input-container:focus-within {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-container-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--color-background-tertiary);
}

.input-container-readonly {
  background: var(--color-background-tertiary);
}

/* 验证状态 */
.input-container-success {
  border-color: var(--color-success);
}

.input-container-success:focus-within {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.input-container-warning {
  border-color: var(--color-warning);
}

.input-container-warning:focus-within {
  border-color: var(--color-warning);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.input-container-error {
  border-color: var(--color-error);
}

.input-container-error:focus-within {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 尺寸变体 */
.input-container-sm {
  height: var(--input-height-sm);
  padding: 0 var(--space-3);
  gap: var(--space-2);
}

.input-container-md {
  height: var(--input-height-md);
  padding: 0 var(--space-4);
  gap: var(--space-2);
}

.input-container-lg {
  height: var(--input-height-lg);
  padding: 0 var(--space-5);
  gap: var(--space-3);
}

/* 带前置图标的容器 */
.input-container-with-prefix.input-container-sm {
  padding-left: var(--space-2);
}

.input-container-with-prefix.input-container-md {
  padding-left: var(--space-3);
}

.input-container-with-prefix.input-container-lg {
  padding-left: var(--space-4);
}

/* 带后置图标的容器 */
.input-container-with-suffix.input-container-sm {
  padding-right: var(--space-2);
}

.input-container-with-suffix.input-container-md {
  padding-right: var(--space-3);
}

.input-container-with-suffix.input-container-lg {
  padding-right: var(--space-4);
}

/* 输入框 */
.input-field {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-family: var(--font-family-primary);
  color: var(--color-text-primary);
  min-width: 0;
}

.input-container-sm .input-field {
  font-size: var(--font-size-sm);
}

.input-container-md .input-field {
  font-size: var(--font-size-base);
}

.input-container-lg .input-field {
  font-size: var(--font-size-lg);
}

.input-field::placeholder {
  color: var(--color-text-tertiary);
}

.input-field:disabled {
  cursor: not-allowed;
}

/* 图标 */
.input-icon {
  color: var(--color-text-tertiary);
  flex-shrink: 0;
  transition: color var(--duration-fast) var(--ease-out);
}

.input-container:focus-within .input-icon {
  color: var(--color-text-secondary);
}

.input-container-sm .input-icon {
  width: 16px;
  height: 16px;
}

.input-container-md .input-icon {
  width: 18px;
  height: 18px;
}

.input-container-lg .input-icon {
  width: 20px;
  height: 20px;
}

/* 操作按钮 */
.input-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: var(--color-text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-out);
  flex-shrink: 0;
}

.input-action-btn:hover {
  color: var(--color-text-secondary);
  background: var(--color-background-tertiary);
}

.input-action-btn:active {
  transform: scale(0.95);
}

.input-container-sm .input-action-btn {
  width: var(--space-6);
  height: var(--space-6);
}

.input-container-md .input-action-btn {
  width: var(--space-7);
  height: var(--space-7);
}

.input-container-lg .input-action-btn {
  width: var(--space-8);
  height: var(--space-8);
}

/* 加载指示器 */
.input-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.loading-spinner {
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.input-container-sm .loading-spinner {
  width: 14px;
  height: 14px;
}

.input-container-md .loading-spinner {
  width: 16px;
  height: 16px;
}

.input-container-lg .loading-spinner {
  width: 18px;
  height: 18px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误信息 */
.input-error {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}


</style>
