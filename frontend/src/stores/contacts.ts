import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { friendsAPI, type User, type BlockedUser } from '@/services/api'
import { toast } from '@/utils/toast'

export interface Contact {
  id: number
  username: string
  nickname: string
  email?: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
  isBlocked: boolean
  isFriend: boolean
  addedAt: string
  remark?: string
}

export interface ContactRequest {
  id: number
  user: User
  request_message?: string
  created_at: string
}

export const useContactsStore = defineStore('contacts', () => {
  const contacts = ref<Contact[]>([])
  const contactRequests = ref<ContactRequest[]>([])
  const sentRequests = ref<ContactRequest[]>([])
  const blockedUsers = ref<BlockedUser[]>([])
  const searchResults = ref<User[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 好友列表
  const friends = computed(() => {
    return contacts.value.filter(contact => contact.isFriend && !contact.isBlocked)
  })

  // 黑名单
  const blockedContacts = computed(() => {
    return blockedUsers.value
  })

  // 待处理的好友请求
  const pendingRequests = computed(() => {
    return contactRequests.value
  })

  // 发送的好友请求
  const pendingSentRequests = computed(() => {
    return sentRequests.value
  })

  // 获取好友列表
  const fetchFriends = async () => {
    try {
      loading.value = true
      error.value = null

      const friendsList = await friendsAPI.getFriendsList()

      // 转换为Contact格式
      contacts.value = friendsList.map(friendship => ({
        id: friendship.friend.id,
        username: friendship.friend.username,
        nickname: friendship.friend.nickname,
        email: friendship.friend.email,
        avatar_url: friendship.friend.avatar_url,
        is_active: friendship.friend.is_active,
        created_at: friendship.friend.created_at,
        updated_at: friendship.friend.updated_at,
        isBlocked: false,
        isFriend: true,
        addedAt: friendship.created_at,
        remark: undefined
      }))

      console.log('获取好友列表成功:', contacts.value.length)
    } catch (err: any) {
      error.value = err.message || '获取好友列表失败'
      console.error('获取好友列表失败:', err)
      toast.error('获取好友列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取好友请求列表（包含接收和发送的）
  const fetchFriendRequests = async () => {
    try {
      loading.value = true
      error.value = null

      const [receivedRequests, sentRequestsList] = await Promise.all([
        friendsAPI.getFriendRequests(),
        friendsAPI.getSentFriendRequests()
      ])

      contactRequests.value = receivedRequests
      sentRequests.value = sentRequestsList

      console.log('获取好友请求成功:', {
        received: receivedRequests.length,
        sent: sentRequestsList.length
      })
    } catch (err: any) {
      error.value = err.message || '获取好友请求失败'
      console.error('获取好友请求失败:', err)
      toast.error('获取好友请求失败')
    } finally {
      loading.value = false
    }
  }

  // 只获取接收到的好友请求（用于联系人页面徽章显示）
  const fetchReceivedRequests = async () => {
    try {
      loading.value = true
      error.value = null

      const receivedRequests = await friendsAPI.getFriendRequests()
      contactRequests.value = receivedRequests

      console.log('获取接收请求成功:', receivedRequests.length)
    } catch (err: any) {
      error.value = err.message || '获取好友请求失败'
      console.error('获取好友请求失败:', err)
      toast.error('获取好友请求失败')
    } finally {
      loading.value = false
    }
  }

  // 获取屏蔽用户列表
  const fetchBlockedUsers = async () => {
    try {
      loading.value = true
      error.value = null

      const blocked = await friendsAPI.getBlockedUsers()
      blockedUsers.value = blocked

      console.log('获取屏蔽列表成功:', blocked.length)
    } catch (err: any) {
      error.value = err.message || '获取屏蔽列表失败'
      console.error('获取屏蔽列表失败:', err)
      toast.error('获取屏蔽列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索用户
  const searchUsers = async (keyword: string) => {
    try {
      if (!keyword.trim()) {
        searchResults.value = []
        return
      }

      loading.value = true
      error.value = null

      const results = await friendsAPI.searchUsers(keyword, 20)
      searchResults.value = results

      console.log('搜索用户成功:', results.length)
    } catch (err: any) {
      error.value = err.message || '搜索用户失败'
      console.error('搜索用户失败:', err)
      searchResults.value = []
      toast.error('搜索用户失败')
    } finally {
      loading.value = false
    }
  }

  // 发送好友请求
  const sendFriendRequest = async (userId: number, message: string = '') => {
    try {
      loading.value = true
      error.value = null

      const response = await friendsAPI.addFriend({
        friend_id: userId,
        request_message: message
      })

      if (response.success) {
        toast.success(response.message || '好友请求发送成功')
        // 刷新发送的请求列表
        await fetchFriendRequests()
        return { success: true }
      } else {
        throw new Error(response.message || '发送失败')
      }
    } catch (err: any) {
      error.value = err.message || '发送好友请求失败'
      console.error('发送好友请求失败:', err)
      toast.error(err.message || '发送好友请求失败')
      return { success: false, error: err.message || '发送失败' }
    } finally {
      loading.value = false
    }
  }

  // 处理好友请求
  const handleFriendRequest = async (requestId: number, action: 'accept' | 'reject') => {
    try {
      loading.value = true
      error.value = null

      let response
      if (action === 'accept') {
        response = await friendsAPI.acceptFriendRequest(requestId)
      } else {
        response = await friendsAPI.rejectFriendRequest(requestId)
      }

      if (response.success) {
        toast.success(response.message || `好友请求已${action === 'accept' ? '接受' : '拒绝'}`)

        // 从请求列表中移除
        const index = contactRequests.value.findIndex(r => r.id === requestId)
        if (index > -1) {
          contactRequests.value.splice(index, 1)
        }

        // 如果接受，刷新好友列表
        if (action === 'accept') {
          await fetchFriends()
        }

        return { success: true }
      } else {
        throw new Error(response.message || '处理失败')
      }
    } catch (err: any) {
      error.value = err.message || '处理好友请求失败'
      console.error('处理好友请求失败:', err)
      toast.error(err.message || '处理好友请求失败')
      return { success: false, error: err.message || '处理失败' }
    } finally {
      loading.value = false
    }
  }

  // 删除好友
  const removeFriend = async (friendId: number) => {
    try {
      loading.value = true
      error.value = null

      const response = await friendsAPI.removeFriend(friendId)

      if (response.success) {
        toast.success(response.message || '好友删除成功')

        // 从好友列表中移除
        const index = contacts.value.findIndex(c => c.id === friendId)
        if (index > -1) {
          contacts.value.splice(index, 1)
        }

        return { success: true }
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (err: any) {
      error.value = err.message || '删除好友失败'
      console.error('删除好友失败:', err)
      toast.error(err.message || '删除好友失败')
      return { success: false, error: err.message || '删除失败' }
    } finally {
      loading.value = false
    }
  }

  // 屏蔽用户
  const blockUser = async (userId: number) => {
    try {
      loading.value = true
      error.value = null

      const response = await friendsAPI.blockUser(userId)

      if (response.success) {
        toast.success(response.message || '用户屏蔽成功')

        // 刷新屏蔽列表和好友列表
        await Promise.all([fetchBlockedUsers(), fetchFriends()])

        return { success: true }
      } else {
        throw new Error(response.message || '屏蔽失败')
      }
    } catch (err: any) {
      error.value = err.message || '屏蔽用户失败'
      console.error('屏蔽用户失败:', err)
      toast.error(err.message || '屏蔽用户失败')
      return { success: false, error: err.message || '屏蔽失败' }
    } finally {
      loading.value = false
    }
  }

  // 解除屏蔽
  const unblockUser = async (userId: number) => {
    try {
      loading.value = true
      error.value = null

      const response = await friendsAPI.unblockUser(userId)

      if (response.success) {
        toast.success(response.message || '解除屏蔽成功')

        // 刷新屏蔽列表
        await fetchBlockedUsers()

        return { success: true }
      } else {
        throw new Error(response.message || '解除屏蔽失败')
      }
    } catch (err: any) {
      error.value = err.message || '解除屏蔽失败'
      console.error('解除屏蔽失败:', err)
      toast.error(err.message || '解除屏蔽失败')
      return { success: false, error: err.message || '解除屏蔽失败' }
    } finally {
      loading.value = false
    }
  }

  // 撤销好友请求
  const cancelFriendRequest = async (requestId: number) => {
    try {
      loading.value = true
      error.value = null

      const response = await friendsAPI.cancelFriendRequest(requestId)

      if (response.success) {
        toast.success(response.message || '好友请求已撤销')

        // 从发送的请求列表中移除
        const index = sentRequests.value.findIndex(r => r.id === requestId)
        if (index > -1) {
          sentRequests.value.splice(index, 1)
        }

        return { success: true }
      } else {
        throw new Error(response.message || '撤销失败')
      }
    } catch (err: any) {
      error.value = err.message || '撤销好友请求失败'
      console.error('撤销好友请求失败:', err)
      toast.error(err.message || '撤销好友请求失败')
      return { success: false, error: err.message || '撤销失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取好友关系状态
  const getFriendshipStatus = async (userId: number) => {
    try {
      const status = await friendsAPI.getFriendshipStatus(userId)
      return status
    } catch (err: any) {
      console.error('获取好友状态失败:', err)
      return null
    }
  }

  return {
    // 状态
    contacts,
    contactRequests,
    sentRequests,
    blockedUsers,
    searchResults,
    loading,
    error,

    // 计算属性
    friends,
    blockedContacts,
    pendingRequests,
    pendingSentRequests,

    // 方法
    fetchFriends,
    fetchFriendRequests,
    fetchReceivedRequests,
    fetchBlockedUsers,
    searchUsers,
    sendFriendRequest,
    handleFriendRequest,
    removeFriend,
    blockUser,
    unblockUser,
    cancelFriendRequest,
    getFriendshipStatus
  }
})