import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Message {
  id: string
  chatId: string
  senderId: string
  content: string
  type: 'text' | 'image' | 'file' | 'audio'
  timestamp: Date
  status: 'sending' | 'sent' | 'delivered' | 'read'
}

export interface Chat {
  id: string
  name: string
  avatar?: string
  type: 'private' | 'group'
  participants: string[]
  lastMessage?: Message
  unreadCount: number
  updatedAt: Date
}

export const useChatStore = defineStore('chat', () => {
  const chats = ref<Chat[]>([])
  const messages = ref<Message[]>([])
  const currentChatId = ref<string | null>(null)

  // 当前聊天
  const currentChat = computed(() => {
    return chats.value.find(chat => chat.id === currentChatId.value) || null
  })

  // 当前聊天的消息
  const currentMessages = computed(() => {
    if (!currentChatId.value) return []
    return messages.value
      .filter(msg => msg.chatId === currentChatId.value)
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
  })

  // 总未读消息数
  const totalUnreadCount = computed(() => {
    return chats.value.reduce((total, chat) => total + chat.unreadCount, 0)
  })

  // 获取聊天列表
  const fetchChats = async () => {
    try {
      // TODO: 调用API获取聊天列表
      console.log('获取聊天列表')
      
      // 模拟数据
      chats.value = [
        {
          id: '1',
          name: '张三',
          type: 'private',
          participants: ['1', '2'],
          unreadCount: 2,
          updatedAt: new Date(),
          lastMessage: {
            id: '1',
            chatId: '1',
            senderId: '2',
            content: '你好，最近怎么样？',
            type: 'text',
            timestamp: new Date(),
            status: 'read'
          }
        },
        {
          id: '2',
          name: '工作群',
          type: 'group',
          participants: ['1', '2', '3', '4'],
          unreadCount: 0,
          updatedAt: new Date(Date.now() - 3600000),
          lastMessage: {
            id: '2',
            chatId: '2',
            senderId: '3',
            content: '明天的会议准备好了吗？',
            type: 'text',
            timestamp: new Date(Date.now() - 3600000),
            status: 'read'
          }
        }
      ]
    } catch (error) {
      console.error('获取聊天列表失败:', error)
    }
  }

  // 获取聊天消息
  const fetchMessages = async (chatId: string) => {
    try {
      // TODO: 调用API获取消息
      console.log('获取消息:', chatId)
      
      // 模拟数据
      const mockMessages: Message[] = [
        {
          id: '1',
          chatId,
          senderId: '2',
          content: '你好！',
          type: 'text',
          timestamp: new Date(Date.now() - 7200000),
          status: 'read'
        },
        {
          id: '2',
          chatId,
          senderId: '1',
          content: '你好，最近怎么样？',
          type: 'text',
          timestamp: new Date(Date.now() - 3600000),
          status: 'read'
        }
      ]
      
      // 合并消息，避免重复
      const existingIds = new Set(messages.value.map(msg => msg.id))
      const newMessages = mockMessages.filter(msg => !existingIds.has(msg.id))
      messages.value.push(...newMessages)
    } catch (error) {
      console.error('获取消息失败:', error)
    }
  }

  // 发送消息
  const sendMessage = async (chatId: string, content: string, type: Message['type'] = 'text') => {
    try {
      const message: Message = {
        id: Date.now().toString(),
        chatId,
        senderId: '1', // 当前用户ID
        content,
        type,
        timestamp: new Date(),
        status: 'sending'
      }
      
      messages.value.push(message)
      
      // TODO: 调用API发送消息
      console.log('发送消息:', message)
      
      // 模拟发送成功
      setTimeout(() => {
        message.status = 'sent'
      }, 1000)
      
      // 更新聊天的最后消息
      const chat = chats.value.find(c => c.id === chatId)
      if (chat) {
        chat.lastMessage = message
        chat.updatedAt = new Date()
      }
      
      return message
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  // 设置当前聊天
  const setCurrentChat = (chatId: string | null) => {
    currentChatId.value = chatId
    
    // 清除未读计数
    if (chatId) {
      const chat = chats.value.find(c => c.id === chatId)
      if (chat) {
        chat.unreadCount = 0
      }
    }
  }

  // 标记消息为已读
  const markAsRead = (chatId: string) => {
    const chat = chats.value.find(c => c.id === chatId)
    if (chat) {
      chat.unreadCount = 0
    }
    
    // 标记消息为已读
    messages.value
      .filter(msg => msg.chatId === chatId && msg.senderId !== '1')
      .forEach(msg => {
        if (msg.status !== 'read') {
          msg.status = 'read'
        }
      })
  }

  return {
    chats,
    messages,
    currentChatId,
    currentChat,
    currentMessages,
    totalUnreadCount,
    fetchChats,
    fetchMessages,
    sendMessage,
    setCurrentChat,
    markAsRead
  }
})