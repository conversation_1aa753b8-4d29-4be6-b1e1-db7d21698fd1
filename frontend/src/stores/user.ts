import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, type User as ApiUser, type LoginRequest, type RegisterRequest } from '@/services/api'
import { showSuccess, showError } from '@/utils'

export interface User {
  id: number
  username: string
  nickname: string
  email?: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<User | null>(null)
  const isLoggedIn = ref(false)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      const response = await authAPI.login(loginData)

      // 保存用户信息和token
      currentUser.value = response.user
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token
      isLoggedIn.value = true

      showSuccess(response.message || '登录成功')
      return { success: true, user: response.user }
    } catch (error: any) {
      console.error('登录失败:', error)
      const message = error.message || '登录失败，请稍后重试'
      showError(message)
      return { success: false, error: message }
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      const response = await authAPI.register(registerData)

      showSuccess(response.message || '注册成功')
      return { success: true, user: response.user }
    } catch (error: any) {
      console.error('注册失败:', error)
      const message = error.message || '注册失败，请稍后重试'
      showError(message)
      return { success: false, error: message }
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (accessToken.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      currentUser.value = null
      isLoggedIn.value = false
      accessToken.value = null
      refreshToken.value = null
      showSuccess('已退出登录')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      if (!accessToken.value) {
        return { success: false, error: '未登录' }
      }

      const user = await authAPI.getCurrentUser()
      currentUser.value = user
      return { success: true, user }
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      return { success: false, error: error.message || '获取用户信息失败' }
    }
  }

  // 刷新token
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新token')
      }

      const response = await authAPI.refreshToken(refreshToken.value)
      accessToken.value = response.access_token
      return { success: true }
    } catch (error: any) {
      console.error('刷新token失败:', error)
      // token刷新失败，清除登录状态
      logout()
      return { success: false, error: error.message || 'token刷新失败' }
    }
  }

  // 初始化用户状态（从本地存储恢复）
  const initializeAuth = async () => {
    if (accessToken.value && refreshToken.value) {
      // 尝试获取用户信息验证token有效性
      const result = await getCurrentUser()
      if (!result.success) {
        // 如果获取用户信息失败，尝试刷新token
        const refreshResult = await refreshAccessToken()
        if (refreshResult.success) {
          // 刷新成功后再次获取用户信息
          await getCurrentUser()
        }
      }
    }
  }

  // 更新用户信息
  const updateProfile = (updates: Partial<User>) => {
    if (currentUser.value) {
      currentUser.value = { ...currentUser.value, ...updates }
    }
  }

  // 计算属性：token（为了兼容现有代码）
  const token = computed(() => accessToken.value)

  return {
    currentUser,
    isLoggedIn,
    accessToken,
    refreshToken,
    token,
    login,
    register,
    logout,
    getCurrentUser,
    refreshAccessToken,
    initializeAuth,
    updateProfile
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['currentUser', 'isLoggedIn', 'accessToken', 'refreshToken']
  }
})