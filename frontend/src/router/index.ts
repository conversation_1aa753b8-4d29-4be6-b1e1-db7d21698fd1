import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/chat'
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat/ChatList.vue'),
    meta: { title: '消息', requiresAuth: true }
  },
  {
    path: '/chat/:id',
    name: 'ChatDetail',
    component: () => import('@/views/Chat/ChatDetail.vue'),
    meta: { title: '聊天', requiresAuth: true }
  },
  {
    path: '/contacts',
    name: 'Contacts',
    component: () => import('@/views/Contacts/ContactList.vue'),
    meta: { title: '联系人', requiresAuth: true }
  },
  {
    path: '/contacts/search',
    name: 'UserSearch',
    component: () => import('@/views/Contacts/UserSearch.vue'),
    meta: { title: '添加好友', requiresAuth: true }
  },
  {
    path: '/contacts/requests',
    name: 'FriendRequests',
    component: () => import('@/views/Contacts/FriendRequests.vue'),
    meta: { title: '新的朋友', requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile/UserProfile.vue'),
    meta: { title: '我的', requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Auth/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Auth/Register.vue'),
    meta: { title: '注册' }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/Auth/ForgotPassword.vue'),
    meta: { title: '忘记密码' }
  },
  {
    path: '/demo',
    name: 'ToastDemo',
    component: () => import('@/views/Demo/ToastDemo.vue'),
    meta: { title: 'Toast演示' }
  },
  {
    path: '/test/badge',
    name: 'BadgeTest',
    component: () => import('@/views/Test/BadgeTest.vue'),
    meta: { title: '徽标测试', requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    const userStore = useUserStore()

    // 如果没有登录，重定向到登录页
    if (!userStore.isLoggedIn || !userStore.accessToken) {
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 如果已登录但没有用户信息，尝试获取用户信息
    if (!userStore.currentUser) {
      try {
        await userStore.getCurrentUser()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 获取用户信息失败，可能token已过期，重定向到登录页
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }

  // 如果已登录用户访问登录或注册页面，重定向到聊天页面
  if ((to.name === 'Login' || to.name === 'Register' || to.name === 'ForgotPassword')) {
    const userStore = useUserStore()
    if (userStore.isLoggedIn && userStore.accessToken) {
      next({ name: 'Chat' })
      return
    }
  }

  next()
})

export default router