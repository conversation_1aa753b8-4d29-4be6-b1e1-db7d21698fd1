import { ref, reactive } from 'vue'
import { useChatStore } from '../stores/chat'
import { useUserStore } from '../stores/user'
import { useContactsStore } from '../stores/contacts'
import type { Message } from '../stores/chat'

export interface WebSocketMessage {
  type: 'heartbeat' | 'chat_message' | 'typing_status' | 'user_status' | 'message_status' |
        'user_online' | 'user_offline' | 'friend_request' | 'friend_accepted' |
        'friend_removed' | 'chat_created' | 'system_notification' | 'error'
  data: any
  timestamp?: string
  message_id?: string
}

class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private heartbeatInterval: number | null = null
  private heartbeatTimer: number | null = null

  public isConnected = ref(false)
  public isConnecting = ref(false)
  public connectionError = ref<string | null>(null)

  constructor() {
    // 不在构造函数中自动连接，由应用层控制连接时机
  }

  // 连接WebSocket
  connect() {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return
    }

    this.isConnecting.value = true
    this.connectionError.value = null

    try {
      const userStore = useUserStore()
      const token = userStore.token
      
      if (!token) {
        console.warn('No token available for WebSocket connection')
        this.isConnecting.value = false
        return
      }

      // 构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      const wsUrl = `${protocol}//${host}/api/ws?token=${token}`

      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)
    } catch (error) {
      console.error('WebSocket connection error:', error)
      this.isConnecting.value = false
      this.connectionError.value = '连接失败'
    }
  }

  // 连接成功
  private onOpen() {
    console.log('WebSocket connected')
    this.isConnected.value = true
    this.isConnecting.value = false
    this.reconnectAttempts = 0
    this.connectionError.value = null
    
    // 启动心跳
    this.startHeartbeat()
  }

  // 接收消息
  private onMessage(event: MessageEvent) {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      this.handleMessage(message)
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  // 连接关闭
  private onClose(event: CloseEvent) {
    console.log('WebSocket disconnected:', event.code, event.reason)
    this.isConnected.value = false
    this.isConnecting.value = false
    
    // 停止心跳
    this.stopHeartbeat()
    
    // 尝试重连
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnect()
    }
  }

  // 连接错误
  private onError(error: Event) {
    console.error('WebSocket error:', error)
    this.connectionError.value = '连接错误'
  }

  // 处理接收到的消息
  private handleMessage(message: WebSocketMessage) {
    const chatStore = useChatStore()
    const contactsStore = useContactsStore()

    switch (message.type) {
      case 'chat_message':
        // 新消息
        const newMessage: Message = message.data
        chatStore.messages.push(newMessage)

        // 更新聊天列表中的最后消息
        const chat = chatStore.chats.find(c => c.id === newMessage.chatId)
        if (chat) {
          chat.lastMessage = newMessage
          chat.updatedAt = new Date(newMessage.timestamp)

          // 如果不是当前聊天，增加未读计数
          if (chatStore.currentChatId !== newMessage.chatId) {
            chat.unreadCount++
          }
        }
        break

      case 'typing_status':
        // 正在输入状态
        console.log('User typing:', message.data)
        break

      case 'user_online':
      case 'user_offline':
        // 用户在线状态变化
        this.handleUserStatusChange(message.data)
        break

      case 'user_status':
        // 用户状态变化
        this.handleUserStatusChange(message.data)
        break

      case 'friend_request':
        // 收到新的好友请求
        console.log('New friend request:', message.data)
        contactsStore.fetchFriendRequests()
        break

      case 'friend_accepted':
        // 好友请求被接受
        console.log('Friend request accepted:', message.data)
        contactsStore.fetchFriends()
        contactsStore.fetchFriendRequests()
        break

      case 'friend_removed':
        // 好友被删除
        console.log('Friend removed:', message.data)
        contactsStore.fetchFriends()
        contactsStore.fetchFriendRequests()
        break

      case 'message_status':
        // 消息状态更新
        const { chatId, messageIds } = message.data
        chatStore.messages
          .filter(msg => msg.chatId === chatId && messageIds.includes(msg.id))
          .forEach(msg => {
            msg.status = 'read'
          })
        break

      default:
        console.log('Unknown message type:', message.type)
    }
  }

  // 处理用户状态变化
  private handleUserStatusChange(data: { userId: number; isOnline: boolean; lastActiveAt?: string }) {
    const contactsStore = useContactsStore()

    // 更新联系人列表中的用户状态
    const contact = contactsStore.contacts.find(c => c.id === data.userId)
    if (contact) {
      contact.is_active = data.isOnline
      if (data.lastActiveAt) {
        contact.updated_at = data.lastActiveAt
      }
    }

    console.log('User status changed:', data)
  }

  // 发送消息
  send(message: WebSocketMessage) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // 发送聊天消息
  sendChatMessage(chatId: string, content: string, type: Message['type'] = 'text') {
    this.send({
      type: 'chat_message',
      data: {
        chat_id: parseInt(chatId),
        content,
        message_type: type
      }
    })
  }

  // 发送正在输入状态
  sendTyping(chatId: string, isTyping: boolean) {
    this.send({
      type: 'typing_status',
      data: {
        chat_id: parseInt(chatId),
        is_typing: isTyping
      }
    })
  }

  // 标记消息为已读
  sendReadStatus(chatId: string, messageIds: string[]) {
    this.send({
      type: 'message_status',
      data: {
        chat_id: parseInt(chatId),
        message_ids: messageIds,
        status: 'read'
      }
    })
  }

  // 重连
  private reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      this.connectionError.value = '连接失败，请刷新页面重试'
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect()
    }, this.reconnectInterval * this.reconnectAttempts)
  }

  // 启动心跳
  private startHeartbeat() {
    this.heartbeatInterval = 30000 // 30秒
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({ type: 'heartbeat', data: {} })
      }
    }, this.heartbeatInterval)
  }

  // 停止心跳
  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 断开连接
  disconnect() {
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, 'User disconnected')
      this.ws = null
    }
    
    this.isConnected.value = false
    this.isConnecting.value = false
  }

  // 获取连接状态
  getConnectionState() {
    return {
      isConnected: this.isConnected.value,
      isConnecting: this.isConnecting.value,
      error: this.connectionError.value
    }
  }
}

// 创建全局WebSocket实例
let wsInstance: WebSocketService | null = null

export function useWebSocket() {
  if (!wsInstance) {
    wsInstance = new WebSocketService()
  }
  return wsInstance
}

export default WebSocketService